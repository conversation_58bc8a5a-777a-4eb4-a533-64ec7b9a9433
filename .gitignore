# ========================================
# نظام إدارة معمل الأسنان المتقدم v2.0
# ملف .gitignore
# ========================================

# ========================================
# Node.js
# ========================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ========================================
# Electron
# ========================================
dist/
build/
out/
release/
app/dist/
app/build/

# Electron-builder output
dist_electron/

# ========================================
# قاعدة البيانات والبيانات الحساسة
# Database & Sensitive Data
# ========================================
data/dental_lab_v2.db
data/dental_lab_v2.db-journal
data/dental_lab_v2.db-wal
data/dental_lab_v2.db-shm
data/backups/*.db
data/backups/*.sql
data/exports/*.xlsx
data/exports/*.pdf
data/logs/*.log

# بيانات المستخدمين
data/users/
data/sessions/
data/cache/

# مفاتيح التشفير
*.key
*.pem
*.p12
*.pfx

# ========================================
# ملفات النظام - System Files
# ========================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ========================================
# محررات النصوص - Text Editors
# ========================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Sublime Text
*.sublime-workspace
*.sublime-project

# Atom
.atom/

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# ========================================
# أدوات التطوير - Development Tools
# ========================================

# ESLint
.eslintcache

# Prettier
.prettierignore

# Jest
coverage/

# Webpack
.webpack/

# Rollup
.rollup.cache/

# Parcel
.parcel-cache/

# ========================================
# ملفات التوزيع - Distribution Files
# ========================================

# Compressed files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Installer files
*.exe
*.msi
*.deb
*.rpm
*.pkg
*.dmg
*.app

# ========================================
# ملفات مؤقتة - Temporary Files
# ========================================

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig
*.save

# Log files
*.log
logs/
*.log.*

# ========================================
# ملفات التكوين المحلية - Local Config
# ========================================

# Local configuration
config/local.json
config/development.json
config/production.json
.env.local

# User-specific files
.user
*.user
*.suo
*.userosscache
*.sln.docstates

# ========================================
# ملفات الاختبار - Test Files
# ========================================

# Test results
test-results/
test-reports/
coverage/
*.lcov

# Screenshots from tests
screenshots/
test-screenshots/

# ========================================
# ملفات التوثيق المؤقتة - Temporary Documentation
# ========================================

# Generated documentation
docs/generated/
api-docs/

# ========================================
# ملفات خاصة بالمشروع - Project Specific
# ========================================

# تقارير مخصصة
reports/custom/
reports/temp/

# ملفات التصدير المؤقتة
exports/temp/

# ملفات الاستيراد المؤقتة
imports/temp/

# ملفات الصور المؤقتة
assets/temp/
assets/uploads/

# ملفات الخطوط المؤقتة
fonts/temp/

# ========================================
# ملفات الأمان - Security Files
# ========================================

# SSL certificates
*.crt
*.cer
*.der
*.p7b
*.p7c
*.p7r
*.p7s
*.csr

# SSH keys
id_rsa
id_rsa.pub
id_dsa
id_dsa.pub
id_ecdsa
id_ecdsa.pub
id_ed25519
id_ed25519.pub

# GPG keys
*.gpg
*.asc

# ========================================
# ملفات التحليل - Analytics Files
# ========================================

# Performance monitoring
performance/
metrics/
analytics/

# ========================================
# ملفات أخرى - Other Files
# ========================================

# Package lock files (اختياري - يمكن إزالة التعليق حسب الحاجة)
# package-lock.json
# yarn.lock

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store?
ehthumbs.db
Icon?
Thumbs.db

# Runtime files
*.pid
*.seed
*.pid.lock

# Optional files
TODO.md
NOTES.md
CHANGELOG.draft.md
