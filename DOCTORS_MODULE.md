# وحدة إدارة الأطباء | Doctors Management Module

## 📋 نظرة عامة | Overview

وحدة إدارة الأطباء هي نظام شامل ومتطور لإدارة جميع جوانب التعامل مع الأطباء في معمل الأسنان، بما في ذلك إدارة البيانات الشخصية، قوائم الأسعار المخصصة، كشوف الحساب، والتقارير المفصلة.

## ✨ المميزات الرئيسية | Key Features

### 🏥 إدارة بيانات الأطباء
- **تسجيل شامل**: إضافة وتعديل بيانات الأطباء مع جميع المعلومات الضرورية
- **معلومات مفصلة**: الاسم، التخصص، العيادة، بيانات الاتصال، الترخيص
- **إعدادات مالية**: نسب الخصم، العمولة، الحد الائتماني، شروط الدفع
- **حالة النشاط**: تفعيل وإلغاء تفعيل الأطباء

### 💰 قوائم الأسعار المخصصة
- **أسعار فردية**: تحديد أسعار مختلفة لكل طبيب حسب نوع التركيبة
- **نسب خصم متدرجة**: تطبيق خصومات مختلفة حسب الكمية أو الفترة
- **تواريخ سريان**: تحديد فترات صلاحية للأسعار المخصصة
- **إدارة متقدمة**: نسخ الأسعار بين الأطباء وتطبيق خصومات شاملة

### 📊 كشوف الحساب
- **إنشاء تلقائي**: توليد كشوف حساب دورية بناءً على الفترة المحددة
- **تفاصيل شاملة**: عرض جميع التركيبات والخدمات في الفترة
- **حسابات دقيقة**: إجمالي المبالغ، الخصومات، صافي المستحق
- **متابعة المدفوعات**: تسجيل وتتبع المدفوعات المستلمة

### 📈 التقارير والإحصائيات
- **تقارير متنوعة**: ملخص الأطباء، الأداء، التحليل المالي
- **إحصائيات فورية**: عدد الحالات، الإيرادات، معدلات الإنجاز
- **تحليل الأداء**: تقييم أداء كل طبيب وتصنيفه
- **تقارير مخصصة**: إنشاء تقارير حسب معايير محددة

### 🖨️ نظام الطباعة
- **طباعة احترافية**: كشوف حساب بتصميم احترافي ومعايير عالية
- **قوالب متعددة**: قوالب مختلفة للكشوف والتقارير
- **معلومات شاملة**: شعار المعمل، بيانات الاتصال، التوقيعات
- **تصدير متعدد**: PDF، Excel، طباعة مباشرة

## 🏗️ البنية التقنية | Technical Architecture

### 📁 ملفات الوحدة | Module Files

```
js/modules/
├── doctors.js                     # الوحدة الرئيسية
├── doctor-price-manager.js        # مدير قوائم الأسعار
├── doctor-statement-manager.js    # مدير كشوف الحساب
├── doctor-reports-manager.js      # مدير التقارير
└── doctor-print-manager.js        # مدير الطباعة

css/
└── doctors.css                    # تصميم الوحدة

test-doctors.js                    # اختبارات الوحدة
```

### 🗄️ قاعدة البيانات | Database Schema

#### جدول الأطباء | Doctors Table
```sql
doctors:
- id (PRIMARY KEY)
- name (TEXT, NOT NULL)
- specialty (TEXT)
- phone (TEXT)
- email (TEXT)
- clinic_name (TEXT)
- license_number (TEXT)
- tax_number (TEXT)
- discount_percentage (REAL)
- commission_percentage (REAL)
- payment_terms (TEXT)
- credit_limit (REAL)
- current_balance (REAL)
- total_cases (INTEGER)
- total_revenue (REAL)
- isActive (BOOLEAN)
- createdAt (DATETIME)
- updatedAt (DATETIME)
```

#### جدول قوائم الأسعار | Price Lists Table
```sql
doctor_price_lists:
- id (PRIMARY KEY)
- doctor_id (FOREIGN KEY)
- prosthetic_type_id (FOREIGN KEY)
- custom_price (REAL)
- discount_percentage (REAL)
- effective_date (DATE)
- expiry_date (DATE)
- isActive (BOOLEAN)
```

#### جدول كشوف الحساب | Statements Table
```sql
doctor_statements:
- id (PRIMARY KEY)
- doctor_id (FOREIGN KEY)
- statement_number (TEXT, UNIQUE)
- statement_date (DATE)
- period_from (DATE)
- period_to (DATE)
- total_cases (INTEGER)
- total_amount (REAL)
- net_amount (REAL)
- current_balance (REAL)
- status (TEXT)
```

## 🚀 كيفية الاستخدام | How to Use

### 1️⃣ إضافة طبيب جديد

```javascript
// فتح نافذة إضافة طبيب جديد
doctorsManager.showNewDoctorModal();

// أو إضافة طبيب برمجياً
const doctorData = {
    name: 'د. أحمد محمد',
    specialty: 'طب الأسنان العام',
    phone: '0501234567',
    email: '<EMAIL>',
    discount_percentage: 15
};

await doctorsManager.saveDoctor(doctorData);
```

### 2️⃣ إدارة قوائم الأسعار

```javascript
// فتح إدارة أسعار طبيب معين
await doctorsManager.managePrices(doctorId);

// إضافة سعر مخصص
const customPrice = {
    doctor_id: 1,
    prosthetic_type_id: 5,
    custom_price: 450,
    discount_percentage: 10
};

await doctorPriceManager.addCustomPrice(customPrice);
```

### 3️⃣ إنشاء كشف حساب

```javascript
// إنشاء كشف حساب لفترة محددة
await doctorStatementManager.generateNewStatement(
    doctorId,
    '2024-01-01',  // من تاريخ
    '2024-01-31'   // إلى تاريخ
);

// طباعة كشف الحساب
await doctorPrintManager.printStatement(statementId);
```

### 4️⃣ إنشاء التقارير

```javascript
// إنشاء تقرير ملخص الأطباء
await doctorReportsManager.generateReport('doctor_summary');

// إنشاء تقرير الأداء
await doctorReportsManager.generateReport('doctor_performance');

// إنشاء التقرير المالي
await doctorReportsManager.generateReport('financial_summary');
```

## 📊 الإحصائيات والمؤشرات | Statistics & KPIs

### 📈 مؤشرات الأداء الرئيسية
- **إجمالي الأطباء**: عدد الأطباء المسجلين
- **الأطباء النشطون**: عدد الأطباء النشطين حالياً
- **إجمالي الإيرادات**: مجموع إيرادات جميع الأطباء
- **الرصيد المستحق**: إجمالي المبالغ المستحقة

### 📊 تحليل الأداء
- **معدل الإنجاز**: نسبة الحالات المكتملة لكل طبيب
- **متوسط وقت التسليم**: الوقت المتوسط لإنجاز التركيبات
- **معدل التحصيل**: نسبة المبالغ المحصلة من إجمالي المفوتر
- **تقييم الأداء**: تصنيف الأطباء (ممتاز، جيد، متوسط، ضعيف)

## 🔧 الإعدادات والتخصيص | Settings & Customization

### ⚙️ إعدادات عامة
- **شروط الدفع الافتراضية**: شهرياً، أسبوعياً، لكل حالة
- **نسب الخصم القياسية**: خصومات افتراضية حسب التخصص
- **حدود ائتمانية**: حدود افتراضية للأطباء الجدد
- **تنسيق أرقام كشوف الحساب**: نمط ترقيم الكشوف

### 🎨 تخصيص التصميم
- **قوالب الطباعة**: تخصيص شكل كشوف الحساب
- **ألوان الواجهة**: تخصيص ألوان وحدة الأطباء
- **شعار المعمل**: إضافة شعار المعمل في المطبوعات
- **معلومات الاتصال**: تحديث بيانات المعمل

## 🧪 الاختبارات | Testing

### 🔍 أنواع الاختبارات
- **اختبارات الوحدة**: اختبار كل وظيفة على حدة
- **اختبارات التكامل**: اختبار التفاعل بين المكونات
- **اختبارات الأداء**: قياس سرعة الاستجابة
- **اختبارات واجهة المستخدم**: التأكد من سهولة الاستخدام

### 🚀 تشغيل الاختبارات

```bash
# تشغيل اختبارات وحدة الأطباء
node test-doctors.js

# النتائج المتوقعة
✅ تحميل بيانات الأطباء: نجح
✅ إنشاء طبيب جديد: نجح
✅ تصفية الأطباء: نجح
✅ إحصائيات الأطباء: نجح
✅ إدارة قوائم الأسعار: نجح
✅ إنشاء كشوف الحساب: نجح
✅ إنشاء التقارير: نجح
✅ وظائف الطباعة: نجح

📊 معدل النجاح: 100%
```

## 🔒 الأمان والصلاحيات | Security & Permissions

### 🛡️ مستويات الصلاحيات
- **مدير النظام**: صلاحية كاملة لجميع العمليات
- **المدير المالي**: إدارة كشوف الحساب والمدفوعات
- **موظف الاستقبال**: عرض بيانات الأطباء فقط
- **فني الأسنان**: عرض قوائم الأسعار فقط

### 🔐 إجراءات الأمان
- **تشفير البيانات**: تشفير المعلومات الحساسة
- **سجل العمليات**: تسجيل جميع التغييرات
- **نسخ احتياطية**: نسخ تلقائية لبيانات الأطباء
- **التحقق من الهوية**: مصادقة المستخدم قبل العمليات الحساسة

## 📞 الدعم والمساعدة | Support & Help

### 🆘 الحصول على المساعدة
- **دليل المستخدم**: شرح مفصل لجميع الوظائف
- **فيديوهات تعليمية**: شروحات مرئية للعمليات
- **الدعم الفني**: فريق دعم متخصص
- **المنتدى**: مجتمع المستخدمين للمساعدة المتبادلة

### 🐛 الإبلاغ عن المشاكل
- **نظام التذاكر**: إبلاغ عن المشاكل والاقتراحات
- **سجل الأخطاء**: تسجيل تلقائي للأخطاء
- **تحديثات دورية**: إصلاحات وتحسينات مستمرة
- **ملاحظات المستخدمين**: تطوير مستمر بناءً على الملاحظات

## 🔄 التحديثات المستقبلية | Future Updates

### 🚀 مميزات قادمة
- **تكامل مع أنظمة خارجية**: ربط مع أنظمة إدارة العيادات
- **تطبيق الهاتف المحمول**: تطبيق للأطباء لمتابعة حالاتهم
- **ذكاء اصطناعي**: توقع احتياجات الأطباء وتحليل الأنماط
- **تقارير متقدمة**: تحليلات أعمق وتوقعات مستقبلية

### 📈 تحسينات مخططة
- **تحسين الأداء**: تسريع عمليات البحث والتصفية
- **واجهة محسنة**: تصميم أكثر سهولة وجاذبية
- **تصدير متقدم**: تصدير لصيغ أكثر وتخصيص أفضل
- **إشعارات ذكية**: تنبيهات تلقائية للمواعيد والمدفوعات

---

## 📝 ملاحظات المطور | Developer Notes

تم تطوير هذه الوحدة باستخدام أحدث التقنيات والمعايير لضمان الأداء العالي والموثوقية. الكود مُوثق بالكامل ويتبع أفضل الممارسات في البرمجة.

**تاريخ الإنشاء**: ديسمبر 2024  
**الإصدار**: 2.0  
**المطور**: فريق تطوير نظام إدارة معمل الأسنان المتقدم

---

🎉 **وحدة إدارة الأطباء - نظام شامل ومتطور لإدارة جميع جوانب التعامل مع الأطباء!**
