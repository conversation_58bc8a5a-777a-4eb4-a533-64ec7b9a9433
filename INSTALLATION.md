# دليل التثبيت والتشغيل
## Installation & Setup Guide

---

## 📋 المتطلبات الأساسية | System Requirements

### الحد الأدنى | Minimum Requirements
- **نظام التشغيل | OS:** Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **المعالج | CPU:** Intel Core i3 أو AMD Ryzen 3 (أو ما يعادلهما)
- **الذاكرة | RAM:** 4 GB
- **مساحة التخزين | Storage:** 2 GB مساحة فارغة
- **الشاشة | Display:** 1024x768 (الحد الأدنى)

### المتطلبات الموصى بها | Recommended Requirements
- **نظام التشغيل | OS:** Windows 11, macOS 12+, Ubuntu 20.04+
- **المعالج | CPU:** Intel Core i5 أو AMD Ryzen 5 (أو أحدث)
- **الذاكرة | RAM:** 8 GB أو أكثر
- **مساحة التخزين | Storage:** 5 GB مساحة فارغة
- **الشاشة | Display:** 1920x1080 أو أعلى

---

## 🛠️ تثبيت المتطلبات | Installing Prerequisites

### 1. تثبيت Node.js | Installing Node.js

#### Windows:
1. اذهب إلى [nodejs.org](https://nodejs.org)
2. حمل النسخة LTS (الموصى بها)
3. شغل ملف التثبيت واتبع التعليمات
4. أعد تشغيل الكمبيوتر

#### macOS:
```bash
# باستخدام Homebrew
brew install node

# أو حمل من الموقع الرسمي
# https://nodejs.org
```

#### Linux (Ubuntu/Debian):
```bash
# تحديث قائمة الحزم
sudo apt update

# تثبيت Node.js و npm
sudo apt install nodejs npm

# التحقق من التثبيت
node --version
npm --version
```

### 2. التحقق من التثبيت | Verify Installation

افتح Terminal أو Command Prompt وشغل:

```bash
node --version
npm --version
```

يجب أن تظهر أرقام الإصدارات.

---

## 📥 تحميل وتثبيت التطبيق | Download & Install

### الطريقة الأولى: تحميل مباشر | Direct Download

1. **حمل الملفات:**
   - حمل جميع ملفات المشروع
   - فك الضغط في مجلد منفصل

2. **افتح Terminal في مجلد المشروع:**
   ```bash
   cd path/to/dental-lab-advanced-v2
   ```

3. **ثبت التبعيات:**
   ```bash
   npm install
   ```

### الطريقة الثانية: Git Clone

```bash
# استنساخ المشروع
git clone https://github.com/dental-lab-systems/dental-lab-advanced-v2.git

# الدخول للمجلد
cd dental-lab-advanced-v2

# تثبيت التبعيات
npm install
```

---

## 🚀 تشغيل التطبيق | Running the Application

### Windows:
```batch
# الطريقة الأولى: استخدام ملف التشغيل
start.bat

# الطريقة الثانية: استخدام npm
npm start

# الطريقة الثالثة: وضع التطوير
npm run dev
```

### macOS/Linux:
```bash
# الطريقة الأولى: استخدام ملف التشغيل
./start.sh

# الطريقة الثانية: استخدام npm
npm start

# الطريقة الثالثة: وضع التطوير
npm run dev
```

### تشغيل في المتصفح | Browser Mode:
```bash
# تشغيل خادم محلي
npm run serve

# أو افتح index.html مباشرة في المتصفح
```

---

## 🔧 إعدادات إضافية | Additional Configuration

### 1. إعداد قاعدة البيانات | Database Setup

التطبيق يستخدم SQLite وسيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل.

### 2. إعداد النسخ الاحتياطية | Backup Configuration

```javascript
// في ملف js/core/globals.js
SYSTEM_CONFIG.database.backup = {
    enabled: true,
    interval: 24 * 60 * 60 * 1000, // 24 ساعة
    maxBackups: 7 // الاحتفاظ بـ 7 نسخ
};
```

### 3. إعداد اللغة الافتراضية | Default Language

```javascript
// في ملف js/core/globals.js
SYSTEM_CONFIG.ui.language = 'ar'; // أو 'en'
```

---

## 👤 بيانات الدخول الافتراضية | Default Login

### مدير النظام | Administrator:
- **اسم المستخدم:** `dentalmanager`
- **كلمة المرور:** `DentalLab@2025!`

### فني الأسنان | Technician:
- **اسم المستخدم:** `dentaltechnician`
- **كلمة المرور:** `Tech@2025!`

> ⚠️ **تحذير أمني:** يرجى تغيير كلمات المرور الافتراضية فور تسجيل الدخول الأول.

---

## 🔨 بناء التطبيق | Building the Application

### بناء لجميع المنصات | Build for All Platforms:
```bash
npm run build
```

### بناء لمنصة محددة | Build for Specific Platform:
```bash
# Windows
npm run build-win

# macOS
npm run build-mac

# Linux
npm run build-linux
```

### إنشاء حزمة محمولة | Create Portable Package:
```bash
npm run pack
```

---

## 🐛 حل المشاكل الشائعة | Troubleshooting

### مشكلة: "node is not recognized"
**الحل:**
1. تأكد من تثبيت Node.js بشكل صحيح
2. أعد تشغيل Terminal/Command Prompt
3. أعد تشغيل الكمبيوتر إذا لزم الأمر

### مشكلة: "npm install fails"
**الحل:**
```bash
# مسح cache
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules
npm install

# أو في Windows
rmdir /s node_modules
npm install
```

### مشكلة: "Permission denied" في Linux/macOS
**الحل:**
```bash
# إعطاء صلاحيات التنفيذ
chmod +x start.sh

# أو تشغيل بصلاحيات المدير
sudo npm install
```

### مشكلة: التطبيق لا يفتح
**الحل:**
1. تحقق من وجود أخطاء في Terminal
2. تأكد من إغلاق جميع نسخ التطبيق السابقة
3. جرب تشغيل `npm run dev` لرؤية الأخطاء

### مشكلة: قاعدة البيانات لا تعمل
**الحل:**
1. تأكد من وجود مجلد `data`
2. تحقق من صلاحيات الكتابة
3. احذف ملف قاعدة البيانات وأعد تشغيل التطبيق

---

## 📁 هيكل الملفات المهمة | Important File Structure

```
dental-lab-advanced-v2/
├── index.html              # الملف الرئيسي
├── main.js                 # ملف Electron الرئيسي
├── package.json            # إعدادات المشروع
├── start.bat              # ملف تشغيل Windows
├── start.sh               # ملف تشغيل Linux/macOS
├── js/
│   ├── core/              # الملفات الأساسية
│   └── modules/           # وحدات التطبيق
├── css/                   # ملفات التصميم
├── assets/                # الموارد (صور، أيقونات)
└── data/                  # بيانات التطبيق
    ├── dental_lab_v2.db   # قاعدة البيانات
    └── backups/           # النسخ الاحتياطية
```

---

## 🔄 التحديثات | Updates

### التحقق من التحديثات | Check for Updates:
1. افتح التطبيق
2. اذهب إلى قائمة "مساعدة"
3. اختر "التحقق من التحديثات"

### التحديث اليدوي | Manual Update:
1. حمل النسخة الجديدة
2. انسخ مجلد `data` من النسخة القديمة
3. ثبت النسخة الجديدة
4. انسخ مجلد `data` للنسخة الجديدة

---

## 📞 الدعم الفني | Technical Support

### في حالة وجود مشاكل:
1. **تحقق من ملف الأخطاء:** `data/logs/error.log`
2. **راجع التوثيق:** [docs.dentallab.com](https://docs.dentallab.com)
3. **تواصل معنا:**
   - 📧 البريد الإلكتروني: <EMAIL>
   - 🌐 الموقع: [dentallab.com](https://dentallab.com)
   - 🐛 الإبلاغ عن الأخطاء: [GitHub Issues](https://github.com/dental-lab-systems/dental-lab-advanced-v2/issues)

---

## ✅ قائمة التحقق | Checklist

قبل البدء، تأكد من:

- [ ] تثبيت Node.js (v16 أو أحدث)
- [ ] تثبيت npm
- [ ] تحميل جميع ملفات المشروع
- [ ] تشغيل `npm install` بنجاح
- [ ] التأكد من وجود مساحة كافية (2 GB على الأقل)
- [ ] إغلاق برامج مكافحة الفيروسات مؤقتاً (إذا لزم الأمر)
- [ ] تشغيل Terminal/Command Prompt كمدير (إذا لزم الأمر)

---

**🎉 مبروك! أنت الآن جاهز لاستخدام نظام إدارة معمل الأسنان المتقدم v2.0**
