# وحدة إدارة التركيبات | Prosthetics Management Module

## 🦷 نظام إدارة معمل الأسنان المتقدم v2.0

---

## 📋 نظرة عامة | Overview

وحدة إدارة التركيبات هي نظام شامل ومتطور لتسجيل وإدارة جميع أنواع التركيبات في معمل الأسنان. تتميز الوحدة بواجهة مستخدم حديثة ونظام حساب أسعار متقدم مع دعم كامل لجميع أنواع التركيبات.

The Prosthetics Management Module is a comprehensive and advanced system for registering and managing all types of dental prosthetics in the lab. The module features a modern user interface and advanced pricing system with full support for all prosthetic types.

---

## ✨ المميزات الرئيسية | Key Features

### 🎯 الوظائف الأساسية | Core Functions
- ✅ **تسجيل التركيبات الجديدة** - إضافة تركيبات جديدة بسهولة
- ✅ **إدارة قائمة التركيبات** - عرض وتحرير وحذف التركيبات
- ✅ **نظام اختيار الأسنان التفاعلي** - واجهة بصرية لاختيار الأسنان
- ✅ **حساب الأسعار التلقائي** - نظام ذكي لحساب الأسعار
- ✅ **نظام البرشل الجزئي المتقدم** - حسابات معقدة للبرشل الجزئي

### 🦷 أنواع التركيبات المدعومة | Supported Prosthetic Types

#### 1. تركيبات البورسلين | Porcelain Prosthetics
- **فيتا (Vita)** - تيجان بورسلين عالية الجودة
- **جى سرام (G-Ceram)** - سيراميك متطور
- **فيس (Face)** - قشور بورسلين للأسنان الأمامية

#### 2. تركيبات الزيركون | Zirconia Prosthetics
- **Full Anatomy** - زيركون كامل التشريح
- **Copy + Porcelain** - زيركون مع طبقة بورسلين
- **Onlay** - حشوات زيركون خارجية

#### 3. التركيبات المعدنية | Metal Prosthetics
- **معدن عادي** - تيجان معدنية تقليدية
- **Vitallium** - سبائك فيتاليوم عالية المقاومة

#### 4. الأطقم المتحركة | Removable Dentures
- **طقم كامل** - أطقم أسنان كاملة علوية أو سفلية
- **طقم جزئي** - أطقم جزئية مع إطار معدني
- **برشل جزئي** - جسور جزئية قابلة للإزالة

#### 5. أجهزة التقويم والحفظ | Orthodontic & Retention Appliances
- **جهاز تقويم متحرك** - أجهزة تقويم قابلة للإزالة
- **حافظ مكان** - أجهزة حفظ المكان للأطفال
- **واقي أسنان** - واقيات ليلية

#### 6. أعمال إضافية متخصصة | Specialized Additional Work
- **إصلاح كسر** - إصلاح التركيبات المكسورة
- **تعديل وضبط** - تعديلات على التركيبات الموجودة
- **إعادة تبطين** - إعادة تبطين الأطقم
- **تلميع وتنظيف** - خدمات التنظيف والتلميع

---

## 🎨 نظام اختيار الأسنان التفاعلي | Interactive Teeth Selection System

### المميزات | Features
- 🦷 **رسم تفاعلي للأسنان** - عرض بصري لجميع الأسنان
- 🎯 **اختيار متعدد** - إمكانية اختيار عدة أسنان
- 📍 **تحديد المواقع** - تمييز الأسنان الأمامية والخلفية
- 🔢 **ترقيم عالمي** - استخدام نظام الترقيم العالمي للأسنان
- ✨ **تأثيرات بصرية** - تأثيرات hover وانتقالات سلسة

### طريقة الاستخدام | How to Use
1. **اختيار الأسنان** - انقر على الأسنان المطلوبة
2. **أزرار التحكم** - استخدم أزرار اختيار الكل أو الفك العلوي/السفلي
3. **إزالة الاختيار** - انقر مرة أخرى لإلغاء اختيار السن
4. **عرض القائمة** - مراجعة الأسنان المختارة في القائمة الجانبية

---

## 💰 نظام حساب الأسعار المتقدم | Advanced Pricing System

### 🧮 حاسبة البرشل الجزئي | Partial Bridge Calculator

#### نظام التدرج في الأسعار | Tiered Pricing System
- **الأسنان 1-3:** السعر الكامل (0% خصم)
- **الأسنان 4-6:** خصم 10%
- **الأسنان 7-10:** خصم 20%
- **الأسنان 11+:** خصم 30%

#### معاملات المواد | Material Multipliers
- **معدن عادي:** 1.0x
- **فيتاليوم:** 1.2x
- **تيتانيوم:** 1.5x
- **ذهب:** 2.0x
- **سيراميك:** 1.3x
- **زيركون:** 1.4x

#### معاملات التعقيد | Complexity Factors
- **بسيط:** 1.0x
- **متوسط:** 1.2x
- **معقد:** 1.5x
- **معقد جداً:** 1.8x

#### معاملات الموقع | Location Factors
- **أمامي:** 1.3x (أسنان ظاهرة)
- **خلفي:** 1.0x (أضراس)
- **مختلط:** 1.15x (أمامي وخلفي)

### 💡 مثال على الحساب | Calculation Example

```javascript
// مثال: برشل جزئي 6 أسنان
const options = {
    teethCount: 6,
    basePrice: 150,
    material: 'zirconia',
    complexity: 'moderate',
    location: 'anterior',
    doctorDiscount: 15
};

// النتيجة:
// الأسنان 1-3: 3 × 150 = 450 ريال
// الأسنان 4-6: 3 × 150 × 0.9 = 405 ريال
// المجموع: 855 ريال
// معامل الزيركون: 855 × 1.4 = 1,197 ريال
// معامل التعقيد: 1,197 × 1.2 = 1,436.4 ريال
// معامل الموقع: 1,436.4 × 1.3 = 1,867.32 ريال
// خصم الطبيب 15%: 1,867.32 × 0.85 = 1,587.22 ريال
```

---

## 🗄️ قاعدة البيانات | Database Structure

### جدول التركيبات الرئيسي | Main Prosthetics Table
```sql
prosthetics:
- id (معرف فريد)
- patient_name (اسم المريض)
- patient_phone (هاتف المريض)
- doctor_id (معرف الطبيب)
- prosthetic_category (فئة التركيبة)
- prosthetic_type (نوع التركيبة)
- prosthetic_subtype (نوع فرعي)
- material (المادة)
- color_shade (درجة اللون)
- selected_teeth (الأسنان المختارة - JSON)
- teeth_positions (مواقع الأسنان - JSON)
- quantity (الكمية)
- unit_price (سعر الوحدة)
- total_price (السعر الإجمالي)
- discount_percentage (نسبة الخصم)
- discount_amount (مبلغ الخصم)
- final_price (السعر النهائي)
- status (الحالة)
- priority (الأولوية)
- delivery_date (تاريخ التسليم)
- notes (ملاحظات)
- special_instructions (تعليمات خاصة)
```

### جدول أنواع التركيبات | Prosthetic Types Table
```sql
prosthetic_types:
- id (معرف فريد)
- category (الفئة)
- type_name (اسم النوع)
- subtype (نوع فرعي)
- material (المادة)
- unit_price (سعر الوحدة)
- description (الوصف)
- is_per_tooth (حساب بالسن)
- special_calculation (حساب خاص)
- color_options (خيارات الألوان - JSON)
```

### جدول تفاصيل الأسنان | Prosthetic Teeth Table
```sql
prosthetic_teeth:
- id (معرف فريد)
- prosthetic_id (معرف التركيبة)
- tooth_number (رقم السن)
- tooth_position (موقع السن)
- tooth_type (نوع السن)
- tooth_name (اسم السن)
- special_notes (ملاحظات خاصة)
```

---

## 🎨 التصميم والواجهة | Design & Interface

### المكونات الرئيسية | Main Components
- **شريط الأدوات** - أزرار الإجراءات الرئيسية
- **إحصائيات سريعة** - عرض الأرقام المهمة
- **شريط البحث والتصفية** - أدوات البحث المتقدم
- **جدول التركيبات** - عرض قائمة التركيبات
- **نافذة التركيبة الجديدة** - نموذج إضافة تركيبة

### التصميم المتجاوب | Responsive Design
- 📱 **الهواتف المحمولة** - تخطيط محسن للشاشات الصغيرة
- 💻 **الأجهزة اللوحية** - واجهة متوسطة الحجم
- 🖥️ **أجهزة سطح المكتب** - استغلال كامل للشاشة

---

## 🧪 الاختبارات | Testing

### ملف الاختبار | Test File
```bash
# تشغيل اختبارات وحدة التركيبات
node test-prosthetics.js
```

### الاختبارات المتضمنة | Included Tests
1. **حاسبة البرشل الجزئي** - اختبار جميع وظائف الحساب
2. **أنواع التركيبات** - التحقق من وجود جميع الأنواع
3. **نظام اختيار الأسنان** - اختبار الاختيار والتحديد
4. **حساب الأسعار** - التحقق من دقة الحسابات

---

## 🚀 التشغيل والاستخدام | Running & Usage

### التشغيل | Running
1. **تأكد من تحميل التبعيات:**
   ```bash
   npm install
   ```

2. **شغل التطبيق:**
   ```bash
   npm start
   ```

3. **انتقل لوحدة التركيبات:**
   - انقر على "إدارة التركيبات" في القائمة الجانبية

### الاستخدام | Usage
1. **إضافة تركيبة جديدة:**
   - انقر على "تركيبة جديدة"
   - املأ بيانات المريض والطبيب
   - اختر نوع التركيبة
   - حدد الأسنان المطلوبة
   - راجع السعر المحسوب
   - احفظ التركيبة

2. **إدارة التركيبات:**
   - استخدم البحث والتصفية
   - عرض تفاصيل التركيبة
   - تعديل البيانات
   - طباعة التقارير

---

## 🔧 التخصيص | Customization

### إضافة نوع تركيبة جديد | Adding New Prosthetic Type
```javascript
// في قاعدة البيانات
await db.insert('prosthetic_types', {
    category: 'custom',
    type_name: 'نوع جديد',
    material: 'مادة مخصصة',
    unit_price: 500,
    description: 'وصف النوع الجديد',
    is_per_tooth: true,
    special_calculation: false
});
```

### تعديل معاملات الأسعار | Modifying Price Factors
```javascript
// في ملف partial-bridge-calculator.js
this.materialMultipliers = {
    'custom_material': 1.6,  // مادة مخصصة جديدة
    // ... باقي المواد
};
```

---

## 📈 الإحصائيات والتقارير | Statistics & Reports

### الإحصائيات المتاحة | Available Statistics
- **إجمالي التركيبات** - العدد الكلي
- **قيد التنفيذ** - التركيبات الجارية
- **مكتملة** - التركيبات المنجزة
- **إيرادات الشهر** - الإيرادات الشهرية

### التقارير | Reports
- **تقرير التركيبات** - قائمة مفصلة
- **تقرير الأطباء** - تركيبات كل طبيب
- **تقرير المواد** - استخدام المواد
- **تقرير الربحية** - تحليل الأرباح

---

## 🔮 التطوير المستقبلي | Future Development

### المميزات المخططة | Planned Features
- 📸 **رفع الصور** - إرفاق صور للتركيبات
- 📅 **جدولة المواعيد** - ربط مع نظام المواعيد
- 📊 **تقارير متقدمة** - تحليلات أعمق
- 🔔 **تنبيهات ذكية** - تذكيرات تلقائية
- 📱 **تطبيق الهاتف** - نسخة محمولة

---

## 📞 الدعم والمساعدة | Support & Help

### في حالة وجود مشاكل | If You Have Issues
1. **راجع ملف الأخطاء:** `data/logs/error.log`
2. **شغل الاختبارات:** `node test-prosthetics.js`
3. **تواصل معنا:** <EMAIL>

---

**🦷 وحدة إدارة التركيبات - نظام شامل ومتطور لإدارة جميع أنواع التركيبات!**
