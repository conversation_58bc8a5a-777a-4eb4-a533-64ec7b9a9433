# البدء السريع | Quick Start

## 🚀 تشغيل التطبيق في 3 خطوات

### الخطوة 1: التحقق من المتطلبات
```bash
# تحقق من وجود Node.js
node --version

# يجب أن يكون الإصدار 16 أو أحدث
```

### الخطوة 2: تثبيت التبعيات
```bash
# في مجلد المشروع
npm install
```

### الخطوة 3: تشغيل التطبيق

#### Windows:
```batch
# اختبار سريع للنظام
quick-test.bat

# تشغيل التطبيق
start.bat
```

#### macOS/Linux:
```bash
# اختبار سريع للنظام
./quick-test.sh

# تشغيل التطبيق
./start.sh
```

---

## 🔑 بيانات الدخول الافتراضية

### مدير النظام:
- **اسم المستخدم:** `dentalmanager`
- **كلمة المرور:** `DentalLab@2025!`

### فني الأسنان:
- **اسم المستخدم:** `dentaltechnician`
- **كلمة المرور:** `Tech@2025!`

---

## 📱 الوصول السريع للوحدات

بعد تسجيل الدخول، يمكنك الوصول إلى:

- 📊 **لوحة التحكم** - نظرة عامة على النشاط
- 🦷 **التركيبات** - إدارة التركيبات والطلبات
- 👨‍⚕️ **الأطباء** - قاعدة بيانات الأطباء
- 👥 **الموظفين** - إدارة فريق العمل
- 💰 **المالية** - الفواتير والمدفوعات
- 📊 **التقارير** - تقارير مفصلة
- 📦 **المخزون** - إدارة المواد والأدوات

---

## 🛠️ أوامر مفيدة

```bash
# تشغيل في وضع التطوير
npm run dev

# تشغيل في المتصفح
npm run serve

# بناء التطبيق
npm run build

# اختبار النظام
node test-system.js
```

---

## 🆘 مشاكل شائعة وحلولها

### المشكلة: "node is not recognized"
**الحل:** ثبت Node.js من [nodejs.org](https://nodejs.org)

### المشكلة: "npm install fails"
**الحل:**
```bash
npm cache clean --force
npm install
```

### المشكلة: التطبيق لا يفتح
**الحل:**
1. تأكد من إغلاق جميع نسخ التطبيق
2. شغل `npm run dev` لرؤية الأخطاء

---

## 📞 الدعم

- 📖 **التوثيق الكامل:** [README.md](README.md)
- 🔧 **دليل التثبيت:** [INSTALLATION.md](INSTALLATION.md)
- 📧 **الدعم الفني:** <EMAIL>

---

**🎉 مرحباً بك في نظام إدارة معمل الأسنان المتقدم v2.0!**
