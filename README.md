# نظام إدارة معمل الأسنان المتقدم v2.0
## Advanced Dental Lab Management System v2.0

![نظام إدارة معمل الأسنان](https://img.shields.io/badge/Version-2.0.0-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Platform](https://img.shields.io/badge/Platform-Electron-lightgrey.svg)

---

## 📋 نظرة عامة | Overview

نظام إدارة معمل الأسنان المتقدم هو تطبيق شامل ومتطور لإدارة جميع جوانب معمل الأسنان، من تسجيل التركيبات وإدارة الأطباء والموظفين إلى النظام المالي والتقارير المتقدمة.

The Advanced Dental Lab Management System is a comprehensive and modern application for managing all aspects of a dental laboratory, from prosthetics registration and doctor/employee management to financial systems and advanced reporting.

---

## ✨ المميزات الرئيسية | Key Features

### 🦷 إدارة التركيبات | Prosthetics Management
- تسجيل وتتبع جميع أنواع التركيبات
- إدارة حالات التركيبات (قيد التنفيذ، مكتمل، تم التسليم)
- تتبع التكاليف والأسعار
- ربط التركيبات بالأطباء والمرضى

### 👨‍⚕️ إدارة الأطباء | Doctors Management
- قاعدة بيانات شاملة للأطباء
- تتبع التخصصات ومعلومات الاتصال
- إدارة نسب الخصم لكل طبيب
- تقارير أداء الأطباء

### 👥 إدارة الموظفين | Employees Management
- سجلات الموظفين الكاملة
- إدارة الرواتب والعمولات
- تتبع الحضور والغياب
- تقييم الأداء

### 💰 النظام المالي | Financial System
- إدارة الفواتير والمدفوعات
- تتبع المصروفات والإيرادات
- حساب المالك والميزانية
- تقارير مالية مفصلة

### 📊 التقارير والتحليلات | Reports & Analytics
- تقارير شاملة لجميع جوانب العمل
- رسوم بيانية تفاعلية
- تصدير التقارير بصيغ مختلفة (PDF, Excel)
- تحليلات الأداء والربحية

### 📦 إدارة المخزون | Inventory Management
- تتبع المواد والأدوات
- تنبيهات نفاد المخزون
- إدارة الموردين
- تتبع تواريخ الانتهاء

---

## 🛠️ التقنيات المستخدمة | Technologies Used

### Frontend
- **HTML5** - هيكل التطبيق
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - المنطق والتفاعل
- **Chart.js** - الرسوم البيانية
- **Font Awesome** - الأيقونات

### Backend & Storage
- **SQLite** - قاعدة البيانات المحلية
- **LocalStorage** - التخزين المؤقت
- **CryptoJS** - التشفير والأمان

### Framework
- **Electron** - تطبيق سطح المكتب
- **Material Design 3** - نظام التصميم

### Libraries
- **FullCalendar** - إدارة المواعيد
- **XLSX** - تصدير Excel
- **PDF-lib** - إنشاء ملفات PDF
- **Moment.js** - إدارة التواريخ

---

## 📁 هيكل المشروع | Project Structure

```
dental-lab-advanced-v2/
├── index.html                 # الملف الرئيسي
├── package.json              # إعدادات المشروع
├── README.md                 # ملف التوثيق
│
├── css/                      # ملفات التصميم
│   ├── main.css             # الأنماط الرئيسية
│   ├── layout.css           # تخطيط الصفحة
│   └── dashboard.css        # أنماط لوحة التحكم
│
├── js/                       # ملفات JavaScript
│   ├── core/                # الملفات الأساسية
│   │   ├── globals.js       # المتغيرات العامة
│   │   ├── database.js      # إدارة قاعدة البيانات
│   │   ├── auth.js          # نظام المصادقة
│   │   ├── language.js      # نظام اللغات
│   │   ├── theme.js         # نظام الثيمات
│   │   ├── notifications.js # نظام الإشعارات
│   │   └── utils.js         # الأدوات المساعدة
│   │
│   ├── modules/             # وحدات التطبيق
│   │   ├── dashboard.js     # لوحة التحكم
│   │   ├── prosthetics.js   # إدارة التركيبات
│   │   ├── doctors.js       # إدارة الأطباء
│   │   ├── employees.js     # إدارة الموظفين
│   │   ├── financial.js     # النظام المالي
│   │   ├── reports.js       # التقارير
│   │   └── inventory.js     # إدارة المخزون
│   │
│   └── app.js               # التطبيق الرئيسي
│
├── assets/                   # الموارد
│   ├── images/              # الصور
│   ├── icons/               # الأيقونات
│   └── fonts/               # الخطوط
│
└── data/                     # البيانات
    ├── backups/             # النسخ الاحتياطية
    └── exports/             # الملفات المصدرة
```

---

## 🚀 التثبيت والتشغيل | Installation & Setup

### المتطلبات | Requirements
- Node.js (v16 أو أحدث)
- npm أو yarn
- متصفح حديث يدعم ES6+

### خطوات التثبيت | Installation Steps

1. **استنساخ المشروع | Clone the project**
```bash
git clone https://github.com/your-username/dental-lab-advanced-v2.git
cd dental-lab-advanced-v2
```

2. **تثبيت التبعيات | Install dependencies**
```bash
npm install
```

3. **تشغيل التطبيق | Run the application**
```bash
npm start
```

### تشغيل في المتصفح | Run in Browser
يمكنك أيضاً فتح ملف `index.html` مباشرة في المتصفح للتطوير والاختبار.

---

## 👤 بيانات الدخول الافتراضية | Default Login Credentials

### مدير النظام | System Administrator
- **اسم المستخدم | Username:** `dentalmanager`
- **كلمة المرور | Password:** `DentalLab@2025!`

### فني الأسنان | Dental Technician
- **اسم المستخدم | Username:** `dentaltechnician`
- **كلمة المرور | Password:** `Tech@2025!`

---

## 🎨 الثيمات واللغات | Themes & Languages

### الثيمات المدعومة | Supported Themes
- 🌞 الوضع النهاري | Light Mode
- 🌙 الوضع الليلي | Dark Mode
- 🔄 الوضع التلقائي | Auto Mode
- 🎨 ثيم معمل الأسنان | Dental Lab Theme
- ♿ التباين العالي | High Contrast

### اللغات المدعومة | Supported Languages
- 🇸🇦 العربية | Arabic (الافتراضية | Default)
- 🇺🇸 الإنجليزية | English

---

## 📱 الاستجابة | Responsive Design

التطبيق مصمم ليعمل بشكل مثالي على جميع الأجهزة:
- 💻 أجهزة الكمبيوتر المكتبية
- 💻 أجهزة الكمبيوتر المحمولة
- 📱 الأجهزة اللوحية
- 📱 الهواتف الذكية

---

## 🔒 الأمان | Security

- 🔐 تشفير كلمات المرور
- 🛡️ حماية الجلسات
- 🚫 منع محاولات الدخول المتكررة
- 🔒 تشفير البيانات الحساسة
- 📝 تسجيل العمليات

---

## 💾 النسخ الاحتياطي | Backup & Restore

- 🔄 نسخ احتياطية تلقائية
- 📁 تصدير البيانات
- 📥 استيراد البيانات
- 🗂️ إدارة النسخ الاحتياطية

---

## 🤝 المساهمة | Contributing

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

---

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والتواصل | Support & Contact

- 📧 البريد الإلكتروني: <EMAIL>
- 🌐 الموقع الإلكتروني: https://dentallab.com
- 📚 التوثيق: https://docs.dentallab.com
- 🐛 الإبلاغ عن الأخطاء: https://github.com/your-username/dental-lab-advanced-v2/issues

---

## 🙏 شكر وتقدير | Acknowledgments

- شكر خاص لجميع المساهمين في المشروع
- شكر لمجتمع المطورين مفتوح المصدر
- شكر لمعامل الأسنان التي ساعدت في تطوير المتطلبات

---

## 📈 الإصدارات | Versions

### v2.0.0 (الحالي | Current)
- إعادة تصميم كاملة للواجهة
- نظام إدارة متقدم للتركيبات
- تحسينات في الأداء والأمان
- دعم اللغة العربية والإنجليزية
- نظام تقارير متطور

### v1.0.0
- الإصدار الأولي
- الوظائف الأساسية لإدارة معمل الأسنان

---

**© 2025 نظام إدارة معمل الأسنان المتقدم. جميع الحقوق محفوظة.**
