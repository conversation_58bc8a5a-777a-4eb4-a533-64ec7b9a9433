/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   أنماط لوحة التحكم الرئيسية - Dashboard Styles
   ======================================== */

/* ========================================
   حاوي لوحة التحكم - Dashboard Container
   ======================================== */

.dashboard-container {
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
    background: var(--background-primary);
}

/* ========================================
   قسم الترحيب - Welcome Section
   ======================================== */

.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-2);
}

.welcome-content {
    flex: 1;
}

.welcome-title {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: white;
}

.welcome-subtitle {
    font-size: var(--font-size-md);
    opacity: 0.9;
    margin-bottom: 0;
}

.welcome-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.welcome-actions .btn {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.welcome-actions .btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.welcome-actions .btn-primary {
    background-color: white;
    color: var(--primary-color);
    border-color: white;
}

.welcome-actions .btn-primary:hover {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-dark);
}

/* ========================================
   قسم الإحصائيات - Statistics Section
   ======================================== */

.stats-section {
    margin-bottom: var(--spacing-xl);
}

.section-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title::before {
    content: '';
    width: 4px;
    height: 24px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius-sm);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

/* بطاقات الإحصائيات */
.stat-card {
    background-color: var(--bg-paper);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-1);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--shadow-2);
    transform: translateY(-2px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: var(--primary-color);
}

.stat-card-primary::before { background-color: var(--primary-color); }
.stat-card-success::before { background-color: var(--success-color); }
.stat-card-warning::before { background-color: var(--warning-color); }
.stat-card-info::before { background-color: var(--info-color); }
.stat-card-secondary::before { background-color: var(--secondary-color); }

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.stat-card-primary .stat-icon { background-color: var(--primary-color); }
.stat-card-success .stat-icon { background-color: var(--success-color); }
.stat-card-warning .stat-icon { background-color: var(--warning-color); }
.stat-card-info .stat-icon { background-color: var(--info-color); }
.stat-card-secondary .stat-icon { background-color: var(--secondary-color); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stat-title {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.stat-change {
    font-size: var(--font-size-xs);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-change-positive {
    color: var(--success-color);
}

.stat-change-negative {
    color: var(--error-color);
}

.stat-change-neutral {
    color: var(--text-hint);
}

/* ========================================
   قسم الرسوم البيانية - Charts Section
   ======================================== */

.charts-section {
    margin-bottom: var(--spacing-xl);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.chart-card {
    background-color: var(--bg-paper);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-1);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.chart-card:hover {
    box-shadow: var(--shadow-2);
}

.chart-card .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.chart-card .card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.chart-card .card-title i {
    color: var(--primary-color);
}

.chart-card .card-content {
    padding: var(--spacing-lg);
    height: 300px;
    position: relative;
}

.chart-card canvas {
    width: 100% !important;
    height: 100% !important;
}

/* ========================================
   قسم النشاطات الأخيرة - Recent Activities
   ======================================== */

.activities-section {
    margin-bottom: var(--spacing-xl);
}

.activities-list {
    background-color: var(--bg-paper);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-1);
    overflow: hidden;
}

.activity-item {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: background-color var(--transition-fast);
}

.activity-item:hover {
    background-color: var(--bg-secondary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: white;
    flex-shrink: 0;
}

.activity-icon.success { background-color: var(--success-color); }
.activity-icon.warning { background-color: var(--warning-color); }
.activity-icon.info { background-color: var(--info-color); }
.activity-icon.error { background-color: var(--error-color); }

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.activity-description {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-hint);
}

/* ========================================
   حالات فارغة - Empty States
   ======================================== */

.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    color: var(--text-hint);
    margin-bottom: var(--spacing-md);
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    font-size: var(--font-size-sm);
    color: var(--text-hint);
}

/* ========================================
   مؤشرات التحميل - Loading Indicators
   ======================================== */

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: var(--border-radius-full);
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.loading-spinner p {
    font-size: var(--font-size-sm);
    color: var(--text-hint);
    margin: 0;
}

/* ========================================
   حالات الخطأ - Error States
   ======================================== */

.error-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--error-color);
}

.error-state i {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
}

.error-state h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

.error-state p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

/* ========================================
   قيد التطوير - Coming Soon
   ======================================== */

.coming-soon {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.coming-soon i {
    font-size: 64px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.coming-soon h3 {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.coming-soon p {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
}

/* ========================================
   الاستجابة للأجهزة المحمولة - Mobile Responsive
   ======================================== */

@media (max-width: 768px) {
    .dashboard-container {
        padding: var(--spacing-md);
    }
    
    .welcome-section {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-lg);
    }
    
    .welcome-actions {
        width: 100%;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .chart-card .card-content {
        height: 250px;
    }
    
    .stat-card {
        padding: var(--spacing-md);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .stat-value {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 480px) {
    .welcome-title {
        font-size: var(--font-size-xl);
    }
    
    .welcome-subtitle {
        font-size: var(--font-size-sm);
    }
    
    .welcome-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .welcome-actions .btn {
        width: 100%;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-card .card-content {
        height: 200px;
        padding: var(--spacing-md);
    }
    
    .activity-item {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .activity-icon {
        width: 32px;
        height: 32px;
        font-size: var(--font-size-xs);
    }
}

/* ========================================
   تصميمات لوحة التحكم الجديدة - New Dashboard Styles
   ======================================== */

/* المحتوى الرئيسي */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.dashboard-section {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.section-controls {
    display: flex;
    gap: 0.5rem;
}

/* النشاطات الأخيرة */
.recent-activities-section {
    min-height: 500px;
}

.activities-container {
    padding: 1.5rem;
    max-height: 600px;
    overflow-y: auto;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--border-color);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: var(--background-tertiary);
    transform: translateX(4px);
}

.activity-item.activity-success { border-left-color: var(--success-color); }
.activity-item.activity-info { border-left-color: var(--info-color); }
.activity-item.activity-warning { border-left-color: var(--warning-color); }
.activity-item.activity-error { border-left-color: var(--error-color); }

.activity-item .activity-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-full);
    background: var(--primary-light);
    color: var(--primary-color);
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-success .activity-icon {
    background: var(--success-light);
    color: var(--success-color);
}

.activity-info .activity-icon {
    background: var(--info-light);
    color: var(--info-color);
}

.activity-warning .activity-icon {
    background: var(--warning-light);
    color: var(--warning-color);
}

.activity-error .activity-icon {
    background: var(--error-light);
    color: var(--error-color);
}

.activity-content {
    flex: 1;
}

.activity-description {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.activity-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.activity-user,
.activity-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.activity-status {
    flex-shrink: 0;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-info {
    background: var(--info-light);
    color: var(--info-dark);
}

.status-warning {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-error {
    background: var(--error-light);
    color: var(--error-dark);
}

.empty-activities {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-activities i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-tertiary);
}

.empty-activities h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
}

.empty-activities p {
    margin: 0;
    font-size: 0.9rem;
}

/* الإحصائيات السريعة */
.quick-stats-section {
    min-height: 300px;
}

.quick-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
}

.quick-stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.quick-stat-item:hover {
    background: var(--background-tertiary);
    transform: translateY(-2px);
}

.quick-stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius);
    background: var(--primary-light);
    color: var(--primary-color);
    font-size: 1rem;
}

.quick-stat-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.quick-stat-content p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
}

/* مؤشر التحديث التلقائي */
.auto-refresh-indicator {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--success-color);
    color: white;
    border-radius: var(--border-radius-full);
    box-shadow: var(--shadow-lg);
    font-size: 0.85rem;
    font-weight: 500;
    z-index: 1000;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* التجاوب للوحة التحكم الجديدة */
@media (max-width: 1200px) {
    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .quick-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .dashboard-controls {
        width: 100%;
        justify-content: stretch;
    }

    .dashboard-controls .btn {
        flex: 1;
    }

    .dashboard-content {
        gap: 1rem;
    }

    .section-header {
        padding: 1rem 1.5rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .section-controls {
        justify-content: stretch;
    }

    .section-controls .btn {
        flex: 1;
    }

    .activities-container {
        padding: 1rem;
    }

    .activity-item {
        padding: 0.75rem;
    }

    .activity-meta {
        flex-direction: column;
        gap: 0.25rem;
    }

    .quick-stats-grid {
        grid-template-columns: 1fr 1fr;
    }

    .auto-refresh-indicator {
        bottom: 1rem;
        right: 1rem;
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 480px) {
    .dashboard-title {
        font-size: 1.5rem;
    }

    .dashboard-subtitle {
        font-size: 1rem;
    }

    .quick-stats-grid {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .activity-item .activity-icon {
        align-self: flex-start;
    }
}
