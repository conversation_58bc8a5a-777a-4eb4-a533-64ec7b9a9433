/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   تصميم وحدة الأطباء - Doctors Module Styles
   ======================================== */

/* ========================================
   الحاوي الرئيسي - Main Container
   ======================================== */

.doctors-container {
    padding: 1.5rem;
    max-width: 100%;
    margin: 0 auto;
}

/* ========================================
   شريط الأدوات العلوي - Toolbar
   ======================================== */

.doctors-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.toolbar-left .page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 0.5rem 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
}

.toolbar-left .page-subtitle {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.toolbar-right {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ========================================
   إحصائيات سريعة - Quick Statistics
   ======================================== */

.doctors-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card-primary { border-left-color: var(--primary-color); }
.stat-card-warning { border-left-color: var(--warning-color); }
.stat-card-success { border-left-color: var(--success-color); }
.stat-card-info { border-left-color: var(--info-color); }

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: var(--border-radius-lg);
    background: var(--primary-color);
    color: white;
    font-size: 1.25rem;
}

.stat-card-warning .stat-icon { background: var(--warning-color); }
.stat-card-success .stat-icon { background: var(--success-color); }
.stat-card-info .stat-icon { background: var(--info-color); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.stat-title {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ========================================
   التنقل بين الأقسام - Navigation Tabs
   ======================================== */

.doctors-navigation {
    margin-bottom: 2rem;
}

.nav-tabs {
    display: flex;
    gap: 0.5rem;
    background: var(--background-secondary);
    padding: 0.5rem;
    border-radius: var(--border-radius-lg);
    overflow-x: auto;
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.nav-tab:hover {
    background: var(--surface-color);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.nav-tab i {
    font-size: 0.9rem;
}

/* ========================================
   المحتوى الرئيسي - Main Content
   ======================================== */

.doctors-content {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.content-section {
    display: none;
    padding: 1.5rem;
}

.content-section.active {
    display: block;
}

/* ========================================
   شريط البحث والتصفية - Search and Filter Bar
   ======================================== */

.doctors-filters {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group .form-control {
    width: 100%;
}

/* ========================================
   قائمة الأطباء - Doctors List
   ======================================== */

.doctors-list-container {
    overflow-x: auto;
}

.doctors-table {
    width: 100%;
    margin: 0;
}

.doctors-table th {
    background: var(--background-secondary);
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem 0.75rem;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.doctors-table td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.doctor-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.doctor-info small {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.specialty {
    color: var(--primary-color);
    font-weight: 500;
}

.clinic-name {
    color: var(--text-primary);
    font-weight: 500;
}

.phone {
    font-family: var(--font-mono);
    color: var(--text-secondary);
}

.discount {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--warning-color);
}

.balance {
    font-family: var(--font-mono);
    font-weight: 700;
    font-size: 1.05rem;
}

.balance.positive {
    color: var(--success-color);
}

.balance.negative {
    color: var(--error-color);
}

.cases-count {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--info-color);
}

.status-badge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-error {
    background: var(--error-light);
    color: var(--error-dark);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    padding: 0.5rem;
    min-width: auto;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ========================================
   حالة فارغة - Empty State
   ======================================== */

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-tertiary);
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* ========================================
   أقسام المحتوى - Content Sections
   ======================================== */

.statements-header,
.payments-header,
.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.statements-header h3,
.payments-header h3,
.reports-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.statements-placeholder,
.payments-placeholder,
.reports-placeholder {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.statements-placeholder i,
.payments-placeholder i,
.reports-placeholder i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: var(--text-tertiary);
}

.statements-placeholder h3,
.payments-placeholder h3,
.reports-placeholder h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.statements-placeholder p,
.payments-placeholder p,
.reports-placeholder p {
    margin: 0;
    font-size: 1rem;
}

/* ========================================
   النوافذ المنبثقة - Modals
   ======================================== */

.large-modal .modal-content {
    max-width: 1000px;
    width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 1.5rem;
}

/* ========================================
   أقسام النموذج - Form Sections
   ======================================== */

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-light);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: var(--input-background);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

/* ========================================
   التجاوب - Responsive Design
   ======================================== */

@media (max-width: 768px) {
    .doctors-container {
        padding: 1rem;
    }
    
    .doctors-toolbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .toolbar-right {
        width: 100%;
        justify-content: stretch;
    }
    
    .toolbar-right .btn {
        flex: 1;
    }
    
    .doctors-stats {
        grid-template-columns: 1fr;
    }
    
    .doctors-filters {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .nav-tabs {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .nav-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .large-modal .modal-content {
        width: 95vw;
        margin: 1rem;
    }
    
    .action-buttons {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .doctors-table {
        font-size: 0.85rem;
    }
    
    .doctors-table th,
    .doctors-table td {
        padding: 0.5rem 0.25rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .nav-tab {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}

/* ========================================
   نافذة ملف الطبيب - Doctor Profile Modal
   ======================================== */

.doctor-profile-content {
    max-height: 70vh;
    overflow-y: auto;
}

.profile-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.profile-section h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.profile-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.profile-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.profile-item span {
    color: var(--text-primary);
    font-size: 1rem;
}

.notes-text {
    background: var(--background-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin: 0;
    line-height: 1.6;
}

/* ========================================
   نافذة التصدير - Export Modal
   ======================================== */

.export-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkbox-text {
    color: var(--text-primary);
}

/* ========================================
   شارات الحالة - Status Badges
   ======================================== */

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.status-active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

/* ========================================
   تحسينات إضافية للنوافذ - Modal Improvements
   ======================================== */

.large-modal .modal-content {
    max-width: 900px;
    width: 90%;
}

@media (max-width: 768px) {
    .profile-grid {
        grid-template-columns: 1fr;
    }

    .large-modal .modal-content {
        width: 95%;
        max-width: none;
    }

    .doctor-profile-content {
        max-height: 60vh;
    }

    .profile-section {
        padding: 1rem;
    }
}
