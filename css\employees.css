/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   تصميم وحدة الموظفين - Employees Module Styles
   ======================================== */

/* ========================================
   الحاوي الرئيسي - Main Container
   ======================================== */

.employees-container {
    padding: 1.5rem;
    max-width: 100%;
    margin: 0 auto;
}

/* ========================================
   شريط الأدوات العلوي - Toolbar
   ======================================== */

.employees-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.toolbar-left .page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 0.5rem 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
}

.toolbar-left .page-subtitle {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.toolbar-right {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ========================================
   إحصائيات سريعة - Quick Statistics
   ======================================== */

.employees-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card-primary { border-left-color: var(--primary-color); }
.stat-card-warning { border-left-color: var(--warning-color); }
.stat-card-success { border-left-color: var(--success-color); }
.stat-card-info { border-left-color: var(--info-color); }

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: var(--border-radius-lg);
    background: var(--primary-color);
    color: white;
    font-size: 1.25rem;
}

.stat-card-warning .stat-icon { background: var(--warning-color); }
.stat-card-success .stat-icon { background: var(--success-color); }
.stat-card-info .stat-icon { background: var(--info-color); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.stat-title {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ========================================
   التنقل بين الأقسام - Navigation Tabs
   ======================================== */

.employees-navigation {
    margin-bottom: 2rem;
}

.nav-tabs {
    display: flex;
    gap: 0.5rem;
    background: var(--background-secondary);
    padding: 0.5rem;
    border-radius: var(--border-radius-lg);
    overflow-x: auto;
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.nav-tab:hover {
    background: var(--surface-color);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.nav-tab i {
    font-size: 0.9rem;
}

/* ========================================
   المحتوى الرئيسي - Main Content
   ======================================== */

.employees-content {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.content-section {
    display: none;
    padding: 1.5rem;
}

.content-section.active {
    display: block;
}

/* ========================================
   شريط البحث والتصفية - Search and Filter Bar
   ======================================== */

.employees-filters {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group .form-control {
    width: 100%;
}

/* ========================================
   قائمة الموظفين - Employees List
   ======================================== */

.employees-list-container {
    overflow-x: auto;
}

.employees-table {
    width: 100%;
    margin: 0;
}

.employees-table th {
    background: var(--background-secondary);
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem 0.75rem;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.employees-table td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.employee-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.employee-info small {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.position {
    color: var(--primary-color);
    font-weight: 500;
}

.department {
    color: var(--text-primary);
    font-weight: 500;
}

.phone {
    font-family: var(--font-mono);
    color: var(--text-secondary);
}

.salary {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--success-color);
}

.commission-rate {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--warning-color);
}

.hire-date {
    font-family: var(--font-mono);
    color: var(--text-secondary);
}

.status-badge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-error {
    background: var(--error-light);
    color: var(--error-dark);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    padding: 0.5rem;
    min-width: auto;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ========================================
   حالة فارغة - Empty State
   ======================================== */

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-tertiary);
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* ========================================
   أقسام المحتوى - Content Sections
   ======================================== */

.payroll-header,
.commissions-header,
.attendance-header,
.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.payroll-header h3,
.commissions-header h3,
.attendance-header h3,
.reports-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.payroll-placeholder,
.commissions-placeholder,
.attendance-placeholder,
.reports-placeholder {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.payroll-placeholder i,
.commissions-placeholder i,
.attendance-placeholder i,
.reports-placeholder i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: var(--text-tertiary);
}

.payroll-placeholder h3,
.commissions-placeholder h3,
.attendance-placeholder h3,
.reports-placeholder h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.payroll-placeholder p,
.commissions-placeholder p,
.attendance-placeholder p,
.reports-placeholder p {
    margin: 0;
    font-size: 1rem;
}

/* ========================================
   النوافذ المنبثقة - Modals
   ======================================== */

.large-modal .modal-content {
    max-width: 1000px;
    width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 1.5rem;
}

/* ========================================
   أقسام النموذج - Form Sections
   ======================================== */

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-light);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: var(--input-background);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

/* ========================================
   التجاوب - Responsive Design
   ======================================== */

@media (max-width: 768px) {
    .employees-container {
        padding: 1rem;
    }
    
    .employees-toolbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .toolbar-right {
        width: 100%;
        justify-content: stretch;
    }
    
    .toolbar-right .btn {
        flex: 1;
    }
    
    .employees-stats {
        grid-template-columns: 1fr;
    }
    
    .employees-filters {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .nav-tabs {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .nav-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .large-modal .modal-content {
        width: 95vw;
        margin: 1rem;
    }
    
    .action-buttons {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .employees-table {
        font-size: 0.85rem;
    }
    
    .employees-table th,
    .employees-table td {
        padding: 0.5rem 0.25rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .nav-tab {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}
