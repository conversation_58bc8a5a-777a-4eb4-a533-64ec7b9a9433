/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   تصميم وحدة المعامل الخارجية - External Labs Styles
   ======================================== */

/* ========================================
   الحاوي الرئيسي - Main Container
   ======================================== */

.external-labs-container {
    padding: 1.5rem;
    max-width: 100%;
    margin: 0 auto;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* ========================================
   شريط الأدوات العلوي - Toolbar
   ======================================== */

.external-labs-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.toolbar-left .page-title {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.breadcrumb {
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toolbar-right {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* ========================================
   شبكة الإحصائيات - Statistics Grid
   ======================================== */

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* ========================================
   التبويبات - Navigation Tabs
   ======================================== */

.nav-tabs {
    display: flex;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 0.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    gap: 0.5rem;
}

.nav-tab {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 600;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* ========================================
   محتوى التبويبات - Tab Content
   ======================================== */

.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* ========================================
   قائمة المعامل - Labs List
   ======================================== */

.search-filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.filter-options {
    display: flex;
    gap: 1rem;
}

.filter-options select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--surface-color);
    min-width: 150px;
}

/* ========================================
   شبكة المعامل - Labs Grid
   ======================================== */

.labs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.lab-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.lab-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.lab-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.lab-info h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.lab-city {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lab-card-body {
    padding: 1.5rem;
}

.lab-contact p {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lab-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.lab-stats .stat-item {
    text-align: center;
}

.lab-stats .stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.lab-stats .stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.lab-card-footer {
    padding: 1rem 1.5rem;
    background: var(--background-color);
    border-top: 1px solid var(--border-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* ========================================
   شارات الحالة - Status Badges
   ======================================== */

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.status-active {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-badge.status-inactive {
    background: var(--error-light);
    color: var(--error-dark);
}

.status-badge.status-warning {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-badge.status-info {
    background: var(--info-light);
    color: var(--info-dark);
}

.status-badge.status-success {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-badge.status-danger {
    background: var(--error-light);
    color: var(--error-dark);
}

.status-badge.status-secondary {
    background: var(--text-disabled);
    color: white;
}

/* ========================================
   الحالة الفارغة - Empty State
   ======================================== */

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: var(--text-disabled);
}

.empty-state h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.empty-state p {
    margin: 0 0 2rem 0;
    font-size: 1.1rem;
}

/* ========================================
   جدول الطلبات - Orders Table
   ======================================== */

.orders-table-container {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th,
.orders-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.orders-table th {
    background: var(--background-color);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.orders-table tr:hover {
    background: var(--background-color);
}

.orders-table .lab-info strong {
    display: block;
    color: var(--text-primary);
}

.orders-table .lab-info small {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* ========================================
   التقارير - Reports
   ======================================== */

.reports-section {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
}

.reports-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.reports-toolbar h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.report-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.report-card {
    background: var(--background-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.report-card h4 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.report-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.report-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-light);
}

.report-stats .stat-item:last-child {
    border-bottom: none;
}

.report-stats .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.report-stats .stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

.chart-container {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

/* ========================================
   النوافذ المنبثقة - Modals
   ======================================== */

.large-modal .modal-content {
    max-width: 800px;
    width: 90%;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkbox-text {
    color: var(--text-primary);
}

/* ========================================
   التصميم المتجاوب - Responsive Design
   ======================================== */

@media (max-width: 768px) {
    .external-labs-container {
        padding: 1rem;
    }

    .external-labs-toolbar {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .toolbar-left,
    .toolbar-right {
        width: 100%;
    }

    .toolbar-right {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .nav-tabs {
        flex-direction: column;
        gap: 0.25rem;
    }

    .nav-tab {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .search-filter-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-options {
        flex-direction: column;
    }

    .filter-options select {
        min-width: auto;
    }

    .labs-grid {
        grid-template-columns: 1fr;
    }

    .lab-card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .action-buttons {
        flex-wrap: wrap;
    }

    .action-buttons .btn {
        flex: 1;
        min-width: 80px;
    }

    .orders-table-container {
        overflow-x: auto;
    }

    .orders-table {
        min-width: 800px;
    }

    .orders-table th,
    .orders-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }

    .reports-toolbar {
        flex-direction: column;
        text-align: center;
    }

    .report-filters {
        flex-direction: column;
        width: 100%;
    }

    .report-filters input,
    .report-filters button {
        width: 100%;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .large-modal .modal-content {
        width: 95%;
        max-width: none;
        margin: 1rem;
    }
}

@media (max-width: 480px) {
    .external-labs-container {
        padding: 0.5rem;
    }

    .page-title {
        font-size: 1.5rem !important;
    }

    .toolbar-right {
        flex-direction: column;
    }

    .toolbar-right .btn {
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .lab-card-body {
        padding: 1rem;
    }

    .lab-card-footer {
        padding: 0.75rem 1rem;
    }

    .action-buttons .btn {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .modal-content {
        margin: 0.5rem;
        padding: 1rem;
    }

    .modal-header h2 {
        font-size: 1.25rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-size: 0.9rem;
    }

    .form-control {
        padding: 0.75rem;
        font-size: 1rem;
    }
}

/* ========================================
   تحسينات إضافية - Additional Improvements
   ======================================== */

.lab-card-header .status-badge {
    margin-top: 0.25rem;
}

.orders-table .action-buttons {
    gap: 0.25rem;
}

.orders-table .action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    outline: none;
}

.filter-options select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    outline: none;
}

/* تحسينات الطباعة */
@media print {
    .external-labs-toolbar,
    .nav-tabs,
    .action-buttons,
    .modal {
        display: none !important;
    }

    .external-labs-container {
        background: white !important;
        padding: 0 !important;
    }

    .lab-card,
    .orders-table-container,
    .report-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }

    .page-title {
        color: black !important;
        margin-bottom: 2rem;
    }
}

/* تحسينات الوصولية */
.btn:focus,
.nav-tab:focus,
.form-control:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.lab-card:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات الأداء */
.lab-card,
.stat-card,
.report-card {
    will-change: transform;
}

.nav-tab,
.btn {
    will-change: background-color, color;
}

/* تحسينات التباين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .external-labs-container {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }

    .search-box input,
    .filter-options select {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .orders-table th {
        background: #2d3748;
    }

    .orders-table tr:hover {
        background: #2d3748;
    }
}

/* ========================================
   نوافذ التفاصيل - Detail Modals
   ======================================== */

.lab-details-content {
    max-height: 70vh;
    overflow-y: auto;
}

.details-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.details-section h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.detail-item span {
    color: var(--text-primary);
    font-size: 1rem;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-box {
    text-align: center;
    padding: 1rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-box .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-box .stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.notes-text {
    background: var(--surface-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin: 0;
    line-height: 1.6;
    color: var(--text-primary);
}

.recent-orders {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.order-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.order-info span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.order-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.order-meta span:first-child {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* تحسينات للنوافذ التفصيلية على الشاشات الصغيرة */
@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
    }

    .stats-row {
        grid-template-columns: repeat(2, 1fr);
    }

    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .order-meta {
        align-items: flex-start;
        width: 100%;
    }

    .lab-details-content {
        max-height: 60vh;
    }

    .details-section {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .stats-row {
        grid-template-columns: 1fr;
    }

    .stat-box {
        padding: 0.75rem;
    }

    .stat-box .stat-value {
        font-size: 1.25rem;
    }
}
