/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   أنماط التخطيط - Layout Styles
   ======================================== */

/* ========================================
   التخطيط الرئيسي - Main Layout
   ======================================== */

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--bg-default);
    transition: all var(--transition-normal);
}

.app-wrapper {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* ========================================
   الشريط العلوي - Header
   ======================================== */

.app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--header-height);
    padding: 0 var(--spacing-lg);
    background-color: var(--bg-paper);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-1);
    z-index: 1000;
    position: relative;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
    margin: 0 var(--spacing-lg);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* شعار التطبيق */
.app-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
}

.app-logo img {
    width: 32px;
    height: 32px;
}

/* زر القائمة الجانبية */
.sidebar-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: var(--bg-secondary);
}

/* البحث العام */
.global-search {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.global-search input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background-color: var(--bg-secondary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.global-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--bg-paper);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.global-search .search-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-hint);
    pointer-events: none;
}

/* أزرار الشريط العلوي */
.header-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.header-btn:hover {
    background-color: var(--bg-secondary);
}

.header-btn .badge {
    position: absolute;
    top: 6px;
    right: 6px;
    min-width: 18px;
    height: 18px;
    padding: 0 4px;
    background-color: var(--error-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* قائمة المستخدم */
.user-menu {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-info:hover {
    background-color: var(--bg-secondary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-full);
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.2;
}

.user-role {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    line-height: 1.2;
}

/* ========================================
   القائمة الجانبية - Sidebar
   ======================================== */

.app-sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-paper);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: all var(--transition-normal);
    z-index: 999;
    position: relative;
}

.sidebar-collapsed .app-sidebar {
    width: var(--sidebar-collapsed-width);
}

/* رأس القائمة الجانبية */
.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.sidebar-collapsed .sidebar-header {
    padding: var(--spacing-md);
    justify-content: center;
}

.sidebar-logo {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
}

.sidebar-collapsed .sidebar-logo {
    display: none;
}

/* قائمة التنقل */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-md) 0;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 var(--spacing-md) var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
    text-decoration: none;
}

.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-link.active:hover {
    background-color: var(--primary-dark);
    color: white;
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.nav-text {
    flex: 1;
    font-size: var(--font-size-sm);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-collapsed .nav-text {
    display: none;
}

.nav-badge {
    background-color: var(--error-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-collapsed .nav-badge {
    position: absolute;
    top: 8px;
    right: 8px;
}

/* ذيل القائمة الجانبية */
.sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.sidebar-collapsed .sidebar-footer {
    text-align: center;
}

/* ========================================
   المحتوى الرئيسي - Main Content
   ======================================== */

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* شريط التنقل */
.breadcrumb-bar {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-paper);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.breadcrumb-item {
    color: var(--text-secondary);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 500;
}

.breadcrumb-separator {
    color: var(--text-hint);
}

/* منطقة المحتوى */
.content-area {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    background-color: var(--bg-default);
}

/* ========================================
   شريط الحالة - Status Bar
   ======================================== */

.status-bar {
    height: var(--footer-height);
    padding: 0 var(--spacing-lg);
    background-color: var(--bg-paper);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.status-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--error-color);
}

/* ========================================
   شاشة التحميل - Loading Screen
   ======================================== */

.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-paper);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: var(--spacing-xl);
}

.loading-logo {
    width: 80px;
    height: 80px;
    margin-bottom: var(--spacing-lg);
}

.loading-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.loading-subtitle {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.progress-container {
    width: 100%;
    height: 6px;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: var(--border-radius-full);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--text-hint);
}

/* ========================================
   الاستجابة للأجهزة المحمولة - Mobile Responsive
   ======================================== */

@media (max-width: 768px) {
    .app-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        height: 100vh;
        z-index: 1001;
        transition: left var(--transition-normal);
    }
    
    .sidebar-open .app-sidebar {
        left: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }
    
    .sidebar-open .sidebar-overlay {
        opacity: 1;
        visibility: visible;
    }
    
    .main-content {
        width: 100%;
    }
    
    .header-center {
        display: none;
    }
    
    .user-details {
        display: none;
    }
    
    .content-area {
        padding: var(--spacing-md);
    }
    
    .breadcrumb-bar {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .status-bar {
        padding: 0 var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .app-header {
        padding: 0 var(--spacing-md);
    }
    
    .header-right {
        gap: var(--spacing-xs);
    }
    
    .content-area {
        padding: var(--spacing-sm);
    }
    
    .loading-content {
        padding: var(--spacing-lg);
    }
    
    .loading-title {
        font-size: var(--font-size-lg);
    }
    
    .loading-subtitle {
        font-size: var(--font-size-sm);
    }
}
