/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   الأنماط الرئيسية - Main Styles
   ======================================== */

/* ========================================
   المتغيرات العامة - Global Variables
   ======================================== */

:root {
    /* الألوان الأساسية - Primary Colors */
    --primary-color: #1976d2;
    --primary-light: #42a5f5;
    --primary-dark: #1565c0;
    --secondary-color: #00acc1;
    --secondary-light: #4dd0e1;
    --secondary-dark: #00838f;
    
    /* ألوان الحالة - Status Colors */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    
    /* ألوان النص - Text Colors */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #bdbdbd;
    --text-hint: #9e9e9e;
    
    /* ألوان الخلفية - Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-paper: #ffffff;
    --bg-default: #fafafa;
    
    /* ألوان الحدود - Border Colors */
    --border-color: #e0e0e0;
    --border-light: #f0f0f0;
    --border-dark: #d0d0d0;
    
    /* الظلال - Shadows */
    --shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    --shadow-4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
    --shadow-5: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
    
    /* المسافات - Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* الخطوط - Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-ar: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    
    /* الانتقالات - Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* نقاط التوقف - Breakpoints */
    --breakpoint-xs: 480px;
    --breakpoint-sm: 768px;
    --breakpoint-md: 1024px;
    --breakpoint-lg: 1200px;
    --breakpoint-xl: 1440px;
    
    /* أبعاد التخطيط - Layout Dimensions */
    --header-height: 64px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 64px;
    --footer-height: 48px;
    
    /* الزوايا المدورة - Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 50%;
}

/* ========================================
   الثيم الليلي - Dark Theme
   ======================================== */

.theme-dark {
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-disabled: #666666;
    --text-hint: #888888;
    
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-paper: #242424;
    --bg-default: #181818;
    
    --border-color: #333333;
    --border-light: #2a2a2a;
    --border-dark: #404040;
}

/* ========================================
   إعادة تعيين الأنماط - Reset Styles
   ======================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    color: var(--text-primary);
    background-color: var(--bg-default);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* دعم اللغة العربية */
body[data-lang="ar"] {
    font-family: var(--font-family-ar);
    direction: rtl;
}

/* ========================================
   العناصر الأساسية - Basic Elements
   ======================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-xxl); }
h2 { font-size: var(--font-size-xl); }
h3 { font-size: var(--font-size-lg); }
h4 { font-size: var(--font-size-md); }
h5 { font-size: var(--font-size-sm); }
h6 { font-size: var(--font-size-xs); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ========================================
   الأزرار - Buttons
   ======================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
    white-space: nowrap;
    min-height: 40px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* أنواع الأزرار */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    color: white;
    text-decoration: none;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-error {
    background-color: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
}

.btn-ghost {
    background-color: transparent;
    color: var(--text-primary);
    border-color: transparent;
}

.btn-ghost:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    text-decoration: none;
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-height: 32px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
    min-height: 48px;
}

/* ========================================
   النماذج - Forms
   ======================================== */

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-control:disabled {
    background-color: var(--bg-secondary);
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control.is-invalid {
    border-color: var(--error-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.form-text {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-hint);
}

.form-error {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--error-color);
}

/* ========================================
   البطاقات - Cards
   ======================================== */

.card {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-1);
    overflow: hidden;
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-2);
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.card-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* ========================================
   الجداول - Tables
   ======================================== */

.table-container {
    overflow-x: auto;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-paper);
}

.table th,
.table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-light);
}

.table th {
    background-color: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr:hover {
    background-color: var(--bg-secondary);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ========================================
   الشبكات - Grids
   ======================================== */

.grid {
    display: grid;
    gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* ========================================
   الأدوات المساعدة - Utilities
   ======================================== */

/* المسافات */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* النصوص */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* العرض */
.hidden { display: none !important; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

/* المحاذاة */
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* الاتجاه */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

/* الفجوات */
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }

/* الحدود */
.border { border: 1px solid var(--border-color); }
.border-t { border-top: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }

.rounded { border-radius: var(--border-radius-md); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-full { border-radius: var(--border-radius-full); }

/* الظلال */
.shadow { box-shadow: var(--shadow-1); }
.shadow-md { box-shadow: var(--shadow-2); }
.shadow-lg { box-shadow: var(--shadow-3); }
.shadow-xl { box-shadow: var(--shadow-4); }
.shadow-2xl { box-shadow: var(--shadow-5); }

/* ========================================
   الرسوم المتحركة - Animations
   ======================================== */

.animate-fade-in {
    animation: fadeIn var(--transition-normal) ease-in-out;
}

.animate-fade-out {
    animation: fadeOut var(--transition-normal) ease-in-out;
}

.animate-slide-in-right {
    animation: slideInRight var(--transition-normal) ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft var(--transition-normal) ease-out;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-30px,0); }
    70% { transform: translate3d(0,-15px,0); }
    90% { transform: translate3d(0,-4px,0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ========================================
   الاستجابة - Responsive Design
   ======================================== */

@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
        --font-size-xs: 11px;
        --font-size-sm: 13px;
        --font-size-md: 15px;
        --font-size-lg: 17px;
        --font-size-xl: 19px;
        --font-size-xxl: 22px;
    }
    
    .grid-cols-2 { grid-template-columns: 1fr; }
    .grid-cols-3 { grid-template-columns: 1fr; }
    .grid-cols-4 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-5 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-6 { grid-template-columns: repeat(2, 1fr); }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .card-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .grid-cols-4 { grid-template-columns: 1fr; }
    .grid-cols-5 { grid-template-columns: 1fr; }
    .grid-cols-6 { grid-template-columns: 1fr; }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* ========================================
   طباعة - Print Styles
   ======================================== */

@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    .no-print {
        display: none !important;
    }

    .page-break {
        page-break-before: always;
    }

    .page-break-inside {
        page-break-inside: avoid;
    }

    .card {
        border: 1px solid #ddd;
        box-shadow: none;
        page-break-inside: avoid;
    }

    .btn {
        display: none;
    }

    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }

    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: "";
    }
}
