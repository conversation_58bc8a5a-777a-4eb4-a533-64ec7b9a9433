/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   تصميم نظام التركيبات المتقدم - Advanced Prosthetics Styles
   ======================================== */

/* ========================================
   الحاوي الرئيسي - Main Container
   ======================================== */

.advanced-prosthetics-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* ========================================
   شريط الأدوات العلوي - Toolbar
   ======================================== */

.prosthetics-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    color: white;
}

.toolbar-left .page-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.toolbar-left .page-title i {
    font-size: 2.5rem;
    color: #ffd700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.toolbar-left .page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 300;
}

.toolbar-right {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.toolbar-right .btn {
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.toolbar-right .btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.toolbar-right .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6);
}

.toolbar-right .btn-outline {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.toolbar-right .btn-outline:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* ========================================
   إحصائيات سريعة - Quick Statistics
   ======================================== */

.prosthetics-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border-left: 5px solid;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-card-pending {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.stat-card-progress {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #bfdbfe 100%);
}

.stat-card-completed {
    border-left-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #a7f3d0 100%);
}

.stat-card-delivered {
    border-left-color: #8b5cf6;
    background: linear-gradient(135deg, #f3e8ff 0%, #c4b5fd 100%);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.stat-card-pending .stat-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card-progress .stat-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-card-completed .stat-icon {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
}

.stat-card-delivered .stat-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-content .stat-value {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    color: #1f2937;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-content .stat-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 5px 0 0 0;
    color: #6b7280;
}

/* ========================================
   شريط البحث والتصفية - Search and Filter Bar
   ======================================== */

.prosthetics-filters {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    display: grid;
    grid-template-columns: 2fr repeat(5, 1fr);
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.filter-group .form-control {
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.filter-group .form-control:focus {
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.filter-group .form-control[type="search"] {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
}

.filter-group .form-control[type="search"]:focus {
    background: white;
    border-color: #667eea;
}

/* ========================================
   قائمة التركيبات - Prosthetics List
   ======================================== */

.prosthetics-list-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-container {
    overflow-x: auto;
}

.prosthetics-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.prosthetics-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.prosthetics-table th {
    padding: 18px 15px;
    text-align: right;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: relative;
}

.prosthetics-table th:hover {
    background: rgba(255, 255, 255, 0.1);
}

.prosthetics-table th i {
    margin-right: 8px;
    opacity: 0.7;
}

.prosthetics-table tbody tr {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

/* ========================================
   نافذة التركيبة الجديدة - New Prosthetic Modal
   ======================================== */

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    visibility: hidden;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
    width: 90%;
    max-width: 1200px;
    animation: modalSlideIn 0.3s ease;
}

.extra-large-modal {
    max-width: 1400px;
    width: 95%;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    padding: 25px 30px;
    border-bottom: 2px solid #f1f5f9;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.modal-body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 2px solid #f1f5f9;
    background: #f8fafc;
    border-radius: 0 0 20px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* ========================================
   أقسام النموذج - Form Sections
   ======================================== */

.form-section {
    margin-bottom: 35px;
    padding: 25px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.form-section:hover {
    border-color: #cbd5e1;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.section-title i {
    color: #667eea;
    font-size: 1.5rem;
}

.teeth-counter {
    margin-right: auto;
    font-size: 1rem;
    font-weight: 500;
    color: #6b7280;
    background: white;
    padding: 8px 15px;
    border-radius: 20px;
    border: 2px solid #e2e8f0;
}

.teeth-counter span {
    color: #3b82f6;
    font-weight: 700;
}

/* ========================================
   صفوف النموذج - Form Rows
   ======================================== */

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-control {
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-control[readonly] {
    background: #f9fafb;
    color: #6b7280;
}

.form-help {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 5px;
}

/* ========================================
   فئات التركيبات - Prosthetic Categories
   ======================================== */

.prosthetic-categories {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid #e2e8f0;
}

.category-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    overflow-x: auto;
}

.category-tab {
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
}

.category-tab:hover {
    background: #e2e8f0;
    color: #374151;
}

.category-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-color: #ffd700;
}

.category-tab i {
    font-size: 1.2rem;
}

.category-content {
    padding: 25px;
}

.category-panel {
    display: none;
}

.category-panel.active {
    display: block;
}

/* ========================================
   خيارات المواد - Material Options
   ======================================== */

.material-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.material-option {
    cursor: pointer;
    display: block;
}

.material-option input[type="radio"] {
    display: none;
}

.material-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 120px;
    justify-content: center;
}

.material-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
}

.material-option input[type="radio"]:checked + .material-card {
    border-color: #667eea;
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transform: translateY(-3px);
}

.material-card i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #667eea;
}

.material-card strong {
    font-size: 1.1rem;
    color: #1f2937;
    margin-bottom: 5px;
}

.material-card small {
    color: #6b7280;
    font-size: 0.9rem;
}

/* ========================================
   اختيار الأسنان - Teeth Selection
   ======================================== */

.teeth-selector {
    background: white;
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #e2e8f0;
}

.teeth-diagram {
    text-align: center;
}

.jaw {
    margin-bottom: 30px;
}

.jaw-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 15px;
    padding: 10px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 8px;
}

.teeth-row {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.tooth {
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.tooth-visual {
    width: 45px;
    height: 45px;
    border: 2px solid #cbd5e1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tooth-visual::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.tooth:hover .tooth-visual::before {
    transform: translateX(100%);
}

.tooth-number {
    font-size: 0.85rem;
    font-weight: 700;
    color: #374151;
}

.tooth-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 5px;
    font-weight: 500;
}

.tooth:hover .tooth-visual {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tooth.selected .tooth-visual {
    border-color: #10b981;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.tooth.selected .tooth-number {
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tooth.selected .tooth-label {
    color: #10b981;
    font-weight: 600;
}

/* أنواع الأسنان */
.tooth-visual.incisor {
    border-radius: 4px 4px 8px 8px;
}

.tooth-visual.canine {
    border-radius: 50% 50% 8px 8px;
}

.tooth-visual.premolar {
    border-radius: 6px;
}

.tooth-visual.molar {
    border-radius: 8px;
    width: 50px;
    height: 50px;
}

/* ========================================
   أدوات التحكم في الأسنان - Teeth Controls
   ======================================== */

.teeth-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin: 25px 0;
    flex-wrap: wrap;
}

.teeth-controls .btn {
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* ========================================
   قائمة الأسنان المختارة - Selected Teeth List
   ======================================== */

.selected-teeth-list {
    margin-top: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    border: 2px solid #0ea5e9;
}

.selected-teeth-list h5 {
    margin: 0 0 15px 0;
    color: #0c4a6e;
    font-weight: 600;
}

.teeth-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tooth-tag {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.tooth-tag:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    transform: scale(1.05);
}

.tooth-tag i {
    font-size: 0.75rem;
}

.no-teeth {
    color: #6b7280;
    font-style: italic;
    padding: 10px;
    text-align: center;
    background: white;
    border-radius: 8px;
    border: 2px dashed #cbd5e1;
}

/* ========================================
   حساب السعر - Price Calculation
   ======================================== */

.price-calculation {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 2px solid #e2e8f0;
}

.price-summary {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 2px solid #22c55e;
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e5e7eb;
}

.price-row:last-child {
    border-bottom: none;
}

.price-row.total {
    font-size: 1.2rem;
    font-weight: 700;
    color: #059669;
    border-top: 2px solid #22c55e;
    padding-top: 15px;
    margin-top: 10px;
}

.price-row span:first-child {
    color: #374151;
    font-weight: 500;
}

.price-row span:last-child {
    color: #059669;
    font-weight: 600;
}

/* ========================================
   تجاوب النافذة - Modal Responsive
   ======================================== */

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 95vh;
        margin: 10px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .category-tabs {
        flex-direction: column;
    }

    .material-options {
        grid-template-columns: 1fr;
    }

    .teeth-row {
        gap: 4px;
    }

    .tooth-visual {
        width: 35px;
        height: 35px;
    }

    .tooth-visual.molar {
        width: 40px;
        height: 40px;
    }

    .teeth-controls {
        flex-direction: column;
        align-items: center;
    }

    .teeth-controls .btn {
        width: 200px;
    }
}

.prosthetics-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    transform: scale(1.01);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.prosthetics-table td {
    padding: 15px;
    vertical-align: middle;
}

/* ========================================
   شارات الحالة - Status Badges
   ======================================== */

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border: 1px solid #f59e0b;
}

.status-info {
    background: linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%);
    color: #1e40af;
    border: 1px solid #3b82f6;
}

.status-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border: 1px solid #10b981;
}

.status-primary {
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    color: #3730a3;
    border: 1px solid #8b5cf6;
}

.status-error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #ef4444;
}

/* ========================================
   أزرار الإجراءات - Action Buttons
   ======================================== */

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-buttons .btn-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.action-buttons .btn-error:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* ========================================
   حالة فارغة - Empty State
   ======================================== */

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #d1d5db;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #374151;
}

.empty-state p {
    font-size: 1rem;
    margin: 0;
}

/* ========================================
   معلومات إضافية - Additional Info
   ======================================== */

.patient-info strong {
    color: #1f2937;
    font-weight: 600;
}

.patient-info small {
    color: #6b7280;
    font-size: 0.85rem;
}

.doctor-name {
    color: #3b82f6;
    font-weight: 500;
}

.prosthetic-type strong {
    color: #1f2937;
    font-weight: 600;
}

.prosthetic-type small {
    color: #6b7280;
    font-size: 0.85rem;
}

.teeth-count {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    color: #0277bd;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
}

.price {
    color: #059669;
    font-weight: 700;
    font-size: 1.1rem;
}

.delivery-date {
    color: #7c3aed;
    font-weight: 500;
}

/* ========================================
   تجاوب الشاشات - Responsive Design
   ======================================== */

@media (max-width: 1200px) {
    .prosthetics-filters {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .advanced-prosthetics-container {
        padding: 15px;
    }
    
    .prosthetics-toolbar {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .toolbar-right {
        justify-content: center;
    }
    
    .prosthetics-stats {
        grid-template-columns: 1fr;
    }
    
    .prosthetics-filters {
        grid-template-columns: 1fr;
    }
    
    .prosthetics-table {
        font-size: 0.85rem;
    }
    
    .prosthetics-table th,
    .prosthetics-table td {
        padding: 10px 8px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
}
