/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   تصميم وحدة التركيبات - Prosthetics Module Styles
   ======================================== */

/* ========================================
   الحاوي الرئيسي - Main Container
   ======================================== */

.prosthetics-container {
    padding: 1.5rem;
    max-width: 100%;
    margin: 0 auto;
}

/* ========================================
   شريط الأدوات العلوي - Toolbar
   ======================================== */

.prosthetics-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.toolbar-left .page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 0.5rem 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
}

.toolbar-left .page-subtitle {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.toolbar-right {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ========================================
   إحصائيات سريعة - Quick Statistics
   ======================================== */

.prosthetics-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card-primary { border-left-color: var(--primary-color); }
.stat-card-warning { border-left-color: var(--warning-color); }
.stat-card-success { border-left-color: var(--success-color); }
.stat-card-info { border-left-color: var(--info-color); }

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: var(--border-radius-lg);
    background: var(--primary-color);
    color: white;
    font-size: 1.25rem;
}

.stat-card-warning .stat-icon { background: var(--warning-color); }
.stat-card-success .stat-icon { background: var(--success-color); }
.stat-card-info .stat-icon { background: var(--info-color); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.stat-title {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ========================================
   المحتوى الرئيسي - Main Content
   ======================================== */

.prosthetics-content {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

/* ========================================
   شريط البحث والتصفية - Search and Filter Bar
   ======================================== */

.prosthetics-filters {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group .form-control {
    width: 100%;
}

/* ========================================
   قائمة التركيبات - Prosthetics List
   ======================================== */

.prosthetics-list-container {
    overflow-x: auto;
}

.prosthetics-table {
    width: 100%;
    margin: 0;
}

.prosthetics-table th {
    background: var(--background-secondary);
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem 0.75rem;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.prosthetics-table td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.prosthetic-id {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--primary-color);
}

.patient-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.patient-info small {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.doctor-name {
    color: var(--text-primary);
    font-weight: 500;
}

.prosthetic-type strong {
    color: var(--text-primary);
    font-weight: 600;
}

.prosthetic-type small {
    color: var(--text-secondary);
    font-size: 0.85rem;
    text-transform: capitalize;
}

.teeth-count {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--info-color);
}

.price {
    font-family: var(--font-mono);
    font-weight: 700;
    color: var(--success-color);
    font-size: 1.05rem;
}

.status-badge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-warning {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-info {
    background: var(--info-light);
    color: var(--info-dark);
}

.status-success {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-error {
    background: var(--error-light);
    color: var(--error-dark);
}

.delivery-date {
    font-family: var(--font-mono);
    color: var(--text-secondary);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-buttons .btn {
    padding: 0.5rem;
    min-width: auto;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ========================================
   حالة فارغة - Empty State
   ======================================== */

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-tertiary);
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* ========================================
   النوافذ المنبثقة - Modals
   ======================================== */

.large-modal .modal-content {
    max-width: 1200px;
    width: 95vw;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 1.5rem;
}

/* ========================================
   أقسام النموذج - Form Sections
   ======================================== */

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-light);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: var(--input-background);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-control:read-only {
    background: var(--background-secondary);
    color: var(--text-secondary);
}

/* ========================================
   نظام اختيار الأسنان - Teeth Selection System
   ======================================== */

.teeth-selection-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 1rem;
}

.teeth-chart-container {
    background: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    border: 2px solid var(--border-color);
}

.teeth-chart-title {
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.05rem;
}

.teeth-row {
    margin-bottom: 2rem;
}

.teeth-row:last-child {
    margin-bottom: 1rem;
}

.teeth-label {
    text-align: center;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.teeth-container {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.tooth {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--surface-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.tooth:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
    transform: scale(1.1);
    z-index: 10;
}

.tooth.selected {
    background: var(--primary-color);
    border-color: var(--primary-dark);
    color: white;
    transform: scale(1.05);
}

.tooth-incisor { border-radius: var(--border-radius) var(--border-radius) 0 0; }
.tooth-canine { border-radius: 50% 50% 0 0; }
.tooth-premolar { border-radius: var(--border-radius); }
.tooth-molar { 
    border-radius: var(--border-radius);
    width: 3rem;
    height: 3rem;
}

.tooth-number {
    font-size: 0.75rem;
    font-weight: 700;
}

.teeth-controls {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.teeth-controls .btn {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

/* ========================================
   معلومات الأسنان المختارة - Selected Teeth Info
   ======================================== */

.selected-teeth-info {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    border: 2px solid var(--border-color);
    height: fit-content;
}

.selected-teeth-info h4 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
}

.no-teeth-selected {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    margin: 2rem 0;
}

.selected-teeth-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.selected-tooth-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.selected-tooth-item .tooth-number {
    font-family: var(--font-mono);
    font-weight: 700;
    color: var(--primary-color);
    background: var(--primary-light);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    min-width: 2rem;
    text-align: center;
}

.selected-tooth-item .tooth-name {
    flex: 1;
    margin: 0 0.75rem;
    font-size: 0.85rem;
    color: var(--text-primary);
}

.remove-tooth {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.7rem;
}

.remove-tooth:hover {
    background: var(--error-dark);
    transform: scale(1.1);
}

.selected-teeth-summary {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
    color: var(--primary-color);
    font-size: 0.9rem;
}

/* ========================================
   حساب السعر - Price Calculation
   ======================================== */

.price-calculation {
    background: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    border: 2px solid var(--border-color);
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.price-row:last-child {
    border-bottom: none;
}

.price-row.total-row {
    border-top: 2px solid var(--primary-color);
    margin-top: 0.5rem;
    padding-top: 1rem;
    font-weight: 700;
    font-size: 1.1rem;
}

.price-label {
    color: var(--text-primary);
    font-weight: 500;
}

.price-value {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--success-color);
}

.total-row .price-value {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* ========================================
   التجاوب - Responsive Design
   ======================================== */

@media (max-width: 768px) {
    .prosthetics-container {
        padding: 1rem;
    }
    
    .prosthetics-toolbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .toolbar-right {
        width: 100%;
        justify-content: stretch;
    }
    
    .toolbar-right .btn {
        flex: 1;
    }
    
    .prosthetics-stats {
        grid-template-columns: 1fr;
    }
    
    .prosthetics-filters {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .teeth-selection-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .teeth-container {
        gap: 0.125rem;
    }
    
    .tooth {
        width: 2rem;
        height: 2rem;
        font-size: 0.7rem;
    }
    
    .tooth-molar {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .large-modal .modal-content {
        width: 95vw;
        margin: 1rem;
    }
    
    .action-buttons {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .prosthetics-table {
        font-size: 0.85rem;
    }
    
    .prosthetics-table th,
    .prosthetics-table td {
        padding: 0.5rem 0.25rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .teeth-controls .btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
}
