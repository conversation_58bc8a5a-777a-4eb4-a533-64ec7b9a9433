<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="نظام إدارة معمل الأسنان المتقدم v2.0 - تطبيق احترافي لإدارة معامل الأسنان">
    <meta name="keywords" content="معمل أسنان, إدارة, تركيبات, أطباء, موظفين, تقارير">
    <meta name="author" content="Dental Lab Systems">
    
    <title>نظام إدارة معمل الأسنان المتقدم v2.0</title>
    
    <!-- الأيقونة -->
    <link rel="icon" type="image/png" href="assets/icon.png">
    
    <!-- الخطوط -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- ملفات التصميم -->
    <link rel="stylesheet" href="styles/variables.css">
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/modules.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link rel="stylesheet" href="styles/modern-login.css">
    <link rel="stylesheet" href="styles/modern-teeth.css">
    <link rel="stylesheet" href="css/prosthetics.css">
    <link rel="stylesheet" href="css/prosthetics-advanced.css">
    <link rel="stylesheet" href="css/doctors.css">
    <link rel="stylesheet" href="css/employees.css">
    <link rel="stylesheet" href="css/external-labs.css">
    
    <!-- مكتبات خارجية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.30.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.30.1/locale/ar.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.2.0/crypto-js.min.js"></script>
    
    <!-- FullCalendar -->
    <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@6.1.10/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@6.1.10/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@6.1.10/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@6.1.10/index.global.min.js"></script>
    
    <!-- XLSX -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- PDF-lib -->
    <script src="https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js"></script>
</head>
<body class="theme-light" data-lang="ar">
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-tooth"></i>
            </div>
            <div class="loading-text">
                <h2>نظام إدارة معمل الأسنان المتقدم</h2>
                <p>الإصدار 2.0</p>
            </div>
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p class="progress-text" id="progress-text">جاري التحميل...</p>
            </div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div id="app-container" class="app-container hidden">
        <!-- شريط العنوان -->
        <header class="app-header">
            <div class="header-left">
                <div class="app-logo">
                    <i class="fas fa-tooth"></i>
                    <span class="app-title">نظام إدارة معمل الأسنان المتقدم</span>
                    <span class="app-version">v2.0</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" id="global-search" class="search-input" placeholder="بحث شامل...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-controls">
                    <!-- تبديل الوضع الليلي/النهاري -->
                    <button class="control-btn theme-toggle" id="theme-toggle" title="تبديل الوضع">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <!-- تبديل اللغة -->
                    <button class="control-btn language-toggle" id="language-toggle" title="تبديل اللغة">
                        <i class="fas fa-language"></i>
                        <span class="lang-text">EN</span>
                    </button>
                    
                    <!-- الإشعارات -->
                    <button class="control-btn notifications" id="notifications-btn" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-count">0</span>
                    </button>
                    
                    <!-- معلومات المستخدم -->
                    <div class="user-info" id="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <span class="user-name" id="user-name">مدير النظام</span>
                            <span class="user-role" id="user-role">مدير</span>
                        </div>
                        <button class="user-menu-btn" id="user-menu-btn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- شريط التنقل الجانبي -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">القوائم الرئيسية</h3>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="sidebar-content">
                <ul class="nav-menu" id="nav-menu">
                    <!-- سيتم إنشاء عناصر القائمة ديناميكياً -->
                </ul>
            </div>
            
            <div class="sidebar-footer">
                <div class="system-status">
                    <div class="status-item">
                        <i class="fas fa-database"></i>
                        <span>قاعدة البيانات: متصلة</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-wifi"></i>
                        <span>الشبكة: متصلة</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content" id="main-content">
            <!-- شريط التنقل العلوي -->
            <div class="breadcrumb" id="breadcrumb">
                <span class="breadcrumb-item active">لوحة التحكم</span>
            </div>
            
            <!-- منطقة المحتوى -->
            <div class="content-area" id="content-area">
                <!-- سيتم تحميل المحتوى هنا ديناميكياً -->
            </div>
        </main>

        <!-- شريط الحالة -->
        <footer class="status-bar" id="status-bar">
            <div class="status-left">
                <span class="status-item">
                    <i class="fas fa-circle status-online"></i>
                    متصل
                </span>
                <span class="status-item" id="current-time">
                    <i class="fas fa-clock"></i>
                    <span id="time-display"></span>
                </span>
            </div>
            
            <div class="status-center">
                <span class="status-item" id="current-module">
                    <i class="fas fa-home"></i>
                    لوحة التحكم
                </span>
            </div>
            
            <div class="status-right">
                <span class="status-item" id="system-info">
                    <i class="fas fa-info-circle"></i>
                    النظام يعمل بشكل طبيعي
                </span>
            </div>
        </footer>
    </div>

    <!-- نافذة تسجيل الدخول العصرية -->
    <div id="login-modal" class="modern-login-modal hidden">
        <div class="login-background">
            <div class="login-particles"></div>
            <div class="login-gradient-overlay"></div>
        </div>

        <div class="modern-login-container">
            <!-- الجانب الأيسر - معلومات النظام -->
            <div class="login-info-panel">
                <div class="login-brand">
                    <div class="brand-logo">
                        <div class="logo-icon">
                            <i class="fas fa-tooth"></i>
                        </div>
                        <div class="logo-rings">
                            <div class="ring ring-1"></div>
                            <div class="ring ring-2"></div>
                            <div class="ring ring-3"></div>
                        </div>
                    </div>
                    <h1 class="brand-title">نظام إدارة معمل الأسنان</h1>
                    <p class="brand-subtitle">الإصدار المتقدم v2.0</p>
                </div>

                <div class="login-features">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-text">
                            <h3>أمان متقدم</h3>
                            <p>حماية شاملة لبياناتك</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="feature-text">
                            <h3>تقارير ذكية</h3>
                            <p>تحليلات متقدمة للأداء</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="feature-text">
                            <h3>نسخ احتياطي تلقائي</h3>
                            <p>حفظ آمن للبيانات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
            <div class="login-form-panel">
                <div class="login-form-container">
                    <div class="login-header">
                        <h2>مرحباً بك</h2>
                        <p>قم بتسجيل الدخول للوصول إلى النظام</p>
                    </div>

                    <form id="login-form" class="modern-login-form">
                        <div class="form-group">
                            <div class="input-container">
                                <input type="text" id="username" class="modern-input" required>
                                <label for="username" class="modern-label">اسم المستخدم</label>
                                <div class="input-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="input-container">
                                <input type="password" id="password" class="modern-input" required>
                                <label for="password" class="modern-label">كلمة المرور</label>
                                <div class="input-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <button type="button" class="password-toggle" id="password-toggle">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <div class="input-underline"></div>
                            </div>
                        </div>

                        <div class="form-options">
                            <label class="modern-checkbox">
                                <input type="checkbox" id="remember-me">
                                <span class="checkbox-mark"></span>
                                <span class="checkbox-text">تذكرني</span>
                            </label>

                            <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                        </div>

                        <button type="submit" class="modern-login-btn" id="login-btn">
                            <span class="btn-text">تسجيل الدخول</span>
                            <div class="btn-loader">
                                <div class="loader-spinner"></div>
                            </div>
                            <div class="btn-success">
                                <i class="fas fa-check"></i>
                            </div>
                        </button>
                    </form>

                    <div class="login-footer">
                        <div class="security-info">
                            <i class="fas fa-info-circle"></i>
                            <span>للحصول على بيانات الدخول، يرجى التواصل مع مدير النظام</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نوافذ منبثقة للإشعارات -->
    <div id="notification-container" class="notification-container"></div>

    <!-- ملفات JavaScript -->
    <script src="js/core/globals.js"></script>
    <script src="js/core/database.js"></script>
    <script src="js/core/auth.js"></script>
    <script src="js/core/language.js"></script>
    <script src="js/core/theme.js"></script>
    <script src="js/core/notifications.js"></script>
    <script src="js/core/utils.js"></script>
    <script src="js/modern-login.js"></script>
    <script src="js/modern-teeth-effects.js"></script>
    
    <script src="js/modules/dashboard.js"></script>
    <script src="js/modules/prosthetics.js"></script>
    <script src="js/modules/prosthetics-advanced.js"></script>
    <script src="js/modules/partial-bridge-calculator.js"></script>
    <script src="js/modules/doctors.js"></script>
    <script src="js/modules/doctor-price-manager.js"></script>
    <script src="js/modules/doctor-statement-manager.js"></script>
    <script src="js/modules/doctor-reports-manager.js"></script>
    <script src="js/modules/doctor-print-manager.js"></script>
    <script src="js/modules/employees.js"></script>
    <script src="js/modules/external-labs.js"></script>
    <script src="js/modules/commission-calculator.js"></script>
    <script src="js/modules/employees.js"></script>
    <script src="js/modules/financial.js"></script>
    <script src="js/modules/reports.js"></script>
    <script src="js/modules/inventory.js"></script>
    <script src="js/modules/patients.js"></script>
    <script src="js/modules/appointments.js"></script>
    <script src="js/modules/invoicing.js"></script>
    
    <script src="js/app.js"></script>
</body>
</html>
