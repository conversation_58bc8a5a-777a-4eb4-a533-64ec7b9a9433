// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// التطبيق الرئيسي - Main Application
// ========================================

console.log('🚀 بدء تشغيل نظام إدارة معمل الأسنان المتقدم v2.0');

// ========================================
// فئة التطبيق الرئيسي - Main Application Class
// ========================================

class DentalLabApp {
    constructor() {
        this.isInitialized = false;
        this.currentModule = null;
        this.loadingProgress = 0;
        this.initializationSteps = [
            { name: 'تحميل قاعدة البيانات', weight: 20 },
            { name: 'تهيئة نظام المصادقة', weight: 15 },
            { name: 'تحميل نظام اللغات', weight: 10 },
            { name: 'تطبيق الثيمات', weight: 10 },
            { name: 'تهيئة الإشعارات', weight: 10 },
            { name: 'تحميل الوحدات', weight: 20 },
            { name: 'إعداد الواجهة', weight: 15 }
        ];
    }

    // ========================================
    // تهيئة التطبيق - Initialize Application
    // ========================================

    async init() {
        try {
            console.log('🔄 بدء تهيئة التطبيق...');
            
            // إظهار شاشة التحميل
            this.showLoadingScreen();
            
            // تنفيذ خطوات التهيئة
            await this.runInitializationSteps();
            
            // إخفاء شاشة التحميل
            await this.hideLoadingScreen();
            
            // التحقق من تسجيل الدخول
            await this.checkAuthentication();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة التطبيق بنجاح');
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة التطبيق:', error);
            this.showErrorScreen(error);
            return false;
        }
    }

    // ========================================
    // تنفيذ خطوات التهيئة - Run Initialization Steps
    // ========================================

    async runInitializationSteps() {
        let completedWeight = 0;
        
        for (const step of this.initializationSteps) {
            try {
                this.updateLoadingProgress(completedWeight, step.name);
                
                switch (step.name) {
                    case 'تحميل قاعدة البيانات':
                        await initializeDatabase();
                        break;
                    case 'تهيئة نظام المصادقة':
                        await initializeAuth();
                        break;
                    case 'تحميل نظام اللغات':
                        await initializeLanguage();
                        break;
                    case 'تطبيق الثيمات':
                        await initializeTheme();
                        break;
                    case 'تهيئة الإشعارات':
                        await initializeNotifications();
                        break;
                    case 'تحميل الوحدات':
                        await this.loadModules();
                        break;
                    case 'إعداد الواجهة':
                        await this.setupUI();
                        break;
                }
                
                completedWeight += step.weight;
                await delay(200); // تأخير بسيط لإظهار التقدم
                
            } catch (error) {
                console.error(`❌ خطأ في ${step.name}:`, error);
                throw new Error(`فشل في ${step.name}: ${error.message}`);
            }
        }
        
        this.updateLoadingProgress(100, 'اكتمل التحميل');
    }

    // ========================================
    // تحديث شريط التقدم - Update Loading Progress
    // ========================================

    updateLoadingProgress(percentage, message) {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = message;
        }
        
        this.loadingProgress = percentage;
    }

    // ========================================
    // إظهار شاشة التحميل - Show Loading Screen
    // ========================================

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
            loadingScreen.classList.add('animate-fade-in');
        }
    }

    // ========================================
    // إخفاء شاشة التحميل - Hide Loading Screen
    // ========================================

    async hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const appContainer = document.getElementById('app-container');
        
        if (loadingScreen && appContainer) {
            // تأخير قصير لإظهار اكتمال التحميل
            await delay(1000);
            
            loadingScreen.classList.add('animate-fade-out');
            
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                appContainer.classList.remove('hidden');
                appContainer.classList.add('animate-fade-in');
            }, 500);
        }
    }

    // ========================================
    // إظهار شاشة الخطأ - Show Error Screen
    // ========================================

    showErrorScreen(error) {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div class="error-content">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h2>خطأ في تشغيل التطبيق</h2>
                    <p>${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }

    // ========================================
    // تحميل الوحدات - Load Modules
    // ========================================

    async loadModules() {
        // تحميل وحدات التطبيق
        const modules = [
            'dashboard',
            'prosthetics', 
            'doctors',
            'employees',
            'financial',
            'reports',
            'inventory',
            'patients',
            'appointments',
            'invoicing'
        ];

        for (const moduleName of modules) {
            try {
                // تحميل الوحدة إذا كانت متوفرة
                if (typeof window[`${moduleName}Manager`] === 'function') {
                    moduleManagers[moduleName] = new window[`${moduleName}Manager`]();
                    await moduleManagers[moduleName].init();
                }
            } catch (error) {
                console.warn(`تحذير: فشل في تحميل وحدة ${moduleName}:`, error);
            }
        }
    }

    // ========================================
    // إعداد الواجهة - Setup UI
    // ========================================

    async setupUI() {
        // حفظ مراجع عناصر DOM
        this.cacheDOM();
        
        // إعداد القائمة الجانبية
        this.setupSidebar();
        
        // إعداد الشريط العلوي
        this.setupHeader();
        
        // إعداد الأحداث
        this.setupEventListeners();
        
        // تحميل الوحدة الافتراضية
        this.loadDefaultModule();
    }

    // ========================================
    // حفظ مراجع DOM - Cache DOM References
    // ========================================

    cacheDOM() {
        domElements.app = document.getElementById('app-container');
        domElements.header = document.querySelector('.app-header');
        domElements.sidebar = document.getElementById('sidebar');
        domElements.content = document.getElementById('content-area');
        domElements.statusBar = document.getElementById('status-bar');
        domElements.loadingScreen = document.getElementById('loading-screen');
        domElements.loginModal = document.getElementById('login-modal');
        domElements.notificationContainer = document.getElementById('notification-container');
    }

    // ========================================
    // إعداد القائمة الجانبية - Setup Sidebar
    // ========================================

    setupSidebar() {
        const navMenu = document.getElementById('nav-menu');
        if (!navMenu) return;

        // مسح القائمة الحالية
        navMenu.innerHTML = '';

        // إنشاء عناصر القائمة
        NAVIGATION_MENU.forEach(item => {
            if (this.canAccessMenuItem(item)) {
                const menuItem = this.createMenuItem(item);
                navMenu.appendChild(menuItem);
            }
        });
    }

    // ========================================
    // إنشاء عنصر قائمة - Create Menu Item
    // ========================================

    createMenuItem(item) {
        const li = document.createElement('li');
        li.className = 'nav-item';

        const link = document.createElement('a');
        link.className = 'nav-link';
        link.href = '#';
        link.setAttribute('data-module', item.id);
        
        link.innerHTML = `
            <div class="nav-icon">
                <i class="${item.icon}"></i>
            </div>
            <span class="nav-text" data-translate="${item.id}">${getLocalizedName(item)}</span>
            ${item.badge ? `<span class="nav-badge">${item.badge}</span>` : ''}
        `;

        link.addEventListener('click', (e) => {
            e.preventDefault();
            this.navigateToModule(item.id);
        });

        li.appendChild(link);
        return li;
    }

    // ========================================
    // التحقق من صلاحية الوصول - Check Access Permission
    // ========================================

    canAccessMenuItem(item) {
        if (!item.permissions || item.permissions.includes('all')) {
            return true;
        }

        return item.permissions.some(permission => 
            canAccessModule(permission)
        );
    }

    // ========================================
    // إعداد الشريط العلوي - Setup Header
    // ========================================

    setupHeader() {
        // تحديث معلومات المستخدم
        this.updateUserInfo();
        
        // إعداد البحث العام
        this.setupGlobalSearch();
    }

    // ========================================
    // تحديث معلومات المستخدم - Update User Info
    // ========================================

    updateUserInfo() {
        const userNameElement = document.getElementById('user-name');
        const userRoleElement = document.getElementById('user-role');
        
        const currentUser = getCurrentUser();
        if (currentUser && userNameElement && userRoleElement) {
            userNameElement.textContent = currentUser.name;
            userRoleElement.textContent = currentUser.role === 'admin' ? 'مدير' : 'مستخدم';
        }
    }

    // ========================================
    // إعداد البحث العام - Setup Global Search
    // ========================================

    setupGlobalSearch() {
        const searchInput = document.getElementById('global-search');
        if (searchInput) {
            const debouncedSearch = debounce((query) => {
                this.performGlobalSearch(query);
            }, 300);

            searchInput.addEventListener('input', (e) => {
                debouncedSearch(e.target.value);
            });
        }
    }

    // ========================================
    // تنفيذ البحث العام - Perform Global Search
    // ========================================

    async performGlobalSearch(query) {
        if (!query || query.length < 2) return;

        try {
            // البحث في جميع الجداول
            const results = await this.searchAllTables(query);
            this.displaySearchResults(results);
        } catch (error) {
            console.error('خطأ في البحث:', error);
        }
    }

    // ========================================
    // البحث في جميع الجداول - Search All Tables
    // ========================================

    async searchAllTables(query) {
        const tables = ['doctors', 'employees', 'patients', 'prosthetics'];
        const results = [];

        for (const table of tables) {
            try {
                const data = await db.findMany(table, {
                    name: { $like: query }
                });
                
                results.push({
                    table,
                    data: data.slice(0, 5) // أول 5 نتائج فقط
                });
            } catch (error) {
                console.warn(`خطأ في البحث في جدول ${table}:`, error);
            }
        }

        return results;
    }

    // ========================================
    // إعداد أحداث التطبيق - Setup Event Listeners
    // ========================================

    setupEventListeners() {
        // زر تبديل القائمة الجانبية
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // زر تبديل اللغة
        const languageToggle = document.getElementById('language-toggle');
        if (languageToggle) {
            languageToggle.addEventListener('click', () => {
                toggleLanguage();
                this.refreshCurrentModule();
            });
        }

        // زر تبديل الثيم
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                toggleTheme();
            });
        }

        // قائمة المستخدم
        const userMenuBtn = document.getElementById('user-menu-btn');
        if (userMenuBtn) {
            userMenuBtn.addEventListener('click', () => {
                this.toggleUserMenu();
            });
        }

        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', throttle(() => {
            this.handleWindowResize();
        }, 250));

        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            appState.isOnline = true;
            this.updateConnectionStatus();
        });

        window.addEventListener('offline', () => {
            appState.isOnline = false;
            this.updateConnectionStatus();
        });
    }

    // ========================================
    // التنقل بين الوحدات - Navigate Between Modules
    // ========================================

    async navigateToModule(moduleId) {
        try {
            // التحقق من الصلاحيات
            if (!canAccessModule(moduleId)) {
                showError('ليس لديك صلاحية للوصول إلى هذه الوحدة');
                return;
            }

            // إزالة التحديد من العنصر السابق
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // تحديد العنصر الحالي
            const currentLink = document.querySelector(`[data-module="${moduleId}"]`);
            if (currentLink) {
                currentLink.classList.add('active');
            }

            // تحديث مسار التنقل
            this.updateBreadcrumb(moduleId);

            // تحميل الوحدة
            await this.loadModule(moduleId);

            // تحديث حالة التطبيق
            appState.currentModule = moduleId;
            this.currentModule = moduleId;

            // تحديث شريط الحالة
            this.updateStatusBar(moduleId);

        } catch (error) {
            console.error(`خطأ في التنقل إلى وحدة ${moduleId}:`, error);
            showError('فشل في تحميل الوحدة');
        }
    }

    // ========================================
    // تحميل وحدة - Load Module
    // ========================================

    async loadModule(moduleId) {
        const contentArea = domElements.content;
        if (!contentArea) return;

        // إظهار مؤشر التحميل
        contentArea.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>جاري تحميل الوحدة...</p>
            </div>
        `;

        try {
            let moduleHTML = '';

            // تحميل محتوى الوحدة
            switch (moduleId) {
                case 'dashboard':
                    moduleHTML = await this.getDashboardHTML();
                    break;
                case 'prosthetics':
                    moduleHTML = await this.getProstheticsHTML();
                    break;
                case 'doctors':
                    moduleHTML = await this.getDoctorsHTML();
                    break;
                case 'employees':
                    moduleHTML = await this.getEmployeesHTML();
                    break;
                case 'financial':
                    moduleHTML = await this.getFinancialHTML();
                    break;
                case 'reports':
                    moduleHTML = await this.getReportsHTML();
                    break;
                default:
                    moduleHTML = this.getComingSoonHTML(moduleId);
            }

            // عرض المحتوى
            contentArea.innerHTML = moduleHTML;
            contentArea.classList.add('animate-fade-in');

            // تهيئة الوحدة
            await this.initializeModule(moduleId);

        } catch (error) {
            console.error(`خطأ في تحميل وحدة ${moduleId}:`, error);
            contentArea.innerHTML = this.getErrorHTML(error.message);
        }
    }

    // ========================================
    // التحقق من المصادقة - Check Authentication
    // ========================================

    async checkAuthentication() {
        const isAuthenticated = await authManager.validateSession();
        
        if (!isAuthenticated) {
            this.showLoginModal();
        } else {
            this.updateUserInfo();
        }
    }

    // ========================================
    // إظهار نافذة تسجيل الدخول - Show Login Modal
    // ========================================

    showLoginModal() {
        const loginModal = domElements.loginModal;
        if (loginModal) {
            loginModal.classList.remove('hidden');
            loginModal.classList.add('show');
            
            // إعداد نموذج تسجيل الدخول
            this.setupLoginForm();
        }
    }

    // ========================================
    // إعداد نموذج تسجيل الدخول - Setup Login Form
    // ========================================

    setupLoginForm() {
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleLogin();
            });
        }

        // زر إظهار/إخفاء كلمة المرور
        const passwordToggle = document.getElementById('password-toggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                this.togglePasswordVisibility();
            });
        }
    }

    // ========================================
    // معالجة تسجيل الدخول - Handle Login
    // ========================================

    async handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        if (!username || !password) {
            showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        const loginBtn = document.getElementById('login-btn');
        const originalText = loginBtn.innerHTML;
        
        try {
            // تعطيل الزر وإظهار مؤشر التحميل
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';

            const result = await login(username, password, rememberMe);

            if (result.success) {
                // إخفاء نافذة تسجيل الدخول
                domElements.loginModal.classList.remove('show');
                domElements.loginModal.classList.add('hidden');
                
                // تحديث معلومات المستخدم
                this.updateUserInfo();
                
                // تحميل الوحدة الافتراضية
                this.loadDefaultModule();
                
                showSuccess('مرحباً بك في النظام');
            } else {
                showError(result.error);
            }

        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            showError('حدث خطأ في تسجيل الدخول');
        } finally {
            // استعادة الزر
            loginBtn.disabled = false;
            loginBtn.innerHTML = originalText;
        }
    }

    // ========================================
    // تحميل الوحدة الافتراضية - Load Default Module
    // ========================================

    loadDefaultModule() {
        this.navigateToModule('dashboard');
    }

    // ========================================
    // دوال مساعدة أخرى - Other Helper Functions
    // ========================================

    toggleSidebar() {
        domElements.app.classList.toggle('sidebar-collapsed');
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#password-toggle i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.replace('fa-eye', 'fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.replace('fa-eye-slash', 'fa-eye');
        }
    }

    updateBreadcrumb(moduleId) {
        const breadcrumb = document.getElementById('breadcrumb');
        if (breadcrumb) {
            const menuItem = NAVIGATION_MENU.find(item => item.id === moduleId);
            if (menuItem) {
                breadcrumb.innerHTML = `
                    <span class="breadcrumb-item active">${getLocalizedName(menuItem)}</span>
                `;
            }
        }
    }

    updateStatusBar(moduleId) {
        const currentModuleElement = document.getElementById('current-module');
        if (currentModuleElement) {
            const menuItem = NAVIGATION_MENU.find(item => item.id === moduleId);
            if (menuItem) {
                currentModuleElement.innerHTML = `
                    <i class="${menuItem.icon}"></i>
                    ${getLocalizedName(menuItem)}
                `;
            }
        }
    }

    updateConnectionStatus() {
        const statusElement = document.querySelector('.status-online');
        if (statusElement) {
            if (appState.isOnline) {
                statusElement.className = 'fas fa-circle status-online';
                statusElement.parentElement.innerHTML = '<i class="fas fa-circle status-online"></i> متصل';
            } else {
                statusElement.className = 'fas fa-circle status-offline';
                statusElement.parentElement.innerHTML = '<i class="fas fa-circle status-offline"></i> غير متصل';
            }
        }
    }

    handleWindowResize() {
        // تحديث التخطيط حسب حجم النافذة
        if (window.innerWidth < 768) {
            domElements.app.classList.add('mobile-view');
        } else {
            domElements.app.classList.remove('mobile-view');
        }
    }

    refreshCurrentModule() {
        if (this.currentModule) {
            this.loadModule(this.currentModule);
        }
    }

    // دوال HTML للوحدات (ستكون بسيطة هنا)
    async getDashboardHTML() {
        return '<div class="dashboard-placeholder">لوحة التحكم - قيد التطوير</div>';
    }

    async getProstheticsHTML() {
        // إرجاع HTML فارغ لأن وحدة التركيبات ستقوم بإنشاء المحتوى بنفسها
        return '<div id="prosthetics-module-container"></div>';
    }

    async getDoctorsHTML() {
        // إرجاع HTML فارغ لأن وحدة الأطباء ستقوم بإنشاء المحتوى بنفسها
        return '<div id="doctors-module-container"></div>';
    }

    async getEmployeesHTML() {
        // إرجاع HTML فارغ لأن وحدة الموظفين ستقوم بإنشاء المحتوى بنفسها
        return '<div id="employees-module-container"></div>';
    }

    async getFinancialHTML() {
        return '<div class="financial-placeholder">النظام المالي - قيد التطوير</div>';
    }

    async getReportsHTML() {
        return '<div class="reports-placeholder">التقارير - قيد التطوير</div>';
    }

    getComingSoonHTML(moduleId) {
        return `
            <div class="coming-soon">
                <i class="fas fa-tools"></i>
                <h3>قيد التطوير</h3>
                <p>وحدة ${moduleId} قيد التطوير وستكون متاحة قريباً</p>
            </div>
        `;
    }

    getErrorHTML(message) {
        return `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>خطأ في التحميل</h3>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">إعادة المحاولة</button>
            </div>
        `;
    }

    async initializeModule(moduleId) {
        // تهيئة الوحدة بعد تحميلها
        try {
            switch (moduleId) {
                case 'dashboard':
                    if (typeof dashboardManager !== 'undefined') {
                        await dashboardManager.render();
                    }
                    break;

                case 'prosthetics':
                    if (typeof advancedProstheticsManager !== 'undefined') {
                        await advancedProstheticsManager.render();
                    }
                    break;

                case 'doctors':
                    if (typeof doctorsManager !== 'undefined') {
                        await doctorsManager.render();
                    }
                    break;

                case 'employees':
                    if (typeof employeesManager !== 'undefined') {
                        await employeesManager.render();
                    }
                    break;

                default:
                    // محاولة تحميل الوحدة من moduleManagers
                    if (moduleManagers[moduleId] && typeof moduleManagers[moduleId].render === 'function') {
                        await moduleManagers[moduleId].render();
                    }
                    break;
            }
        } catch (error) {
            console.error(`خطأ في تهيئة وحدة ${moduleId}:`, error);
        }
    }
}

// ========================================
// تشغيل التطبيق - Start Application
// ========================================

// إنشاء مثيل من التطبيق
const dentalLabApp = new DentalLabApp();

// بدء التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    console.log('📄 تم تحميل DOM، بدء تشغيل التطبيق...');
    
    try {
        const success = await dentalLabApp.init();
        if (success) {
            console.log('🎉 تم تشغيل التطبيق بنجاح!');
        } else {
            console.error('❌ فشل في تشغيل التطبيق');
        }
    } catch (error) {
        console.error('❌ خطأ حرج في تشغيل التطبيق:', error);
    }
});

// تصدير التطبيق للاستخدام العام
window.dentalLabApp = dentalLabApp;

console.log('✅ تم تحميل التطبيق الرئيسي بنجاح');
