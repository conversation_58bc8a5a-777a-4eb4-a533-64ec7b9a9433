// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// المصادقة والأمان - Authentication & Security
// ========================================

console.log('🔐 تحميل نظام المصادقة والأمان...');

// ========================================
// فئة إدارة المصادقة - Authentication Manager
// ========================================

class AuthenticationManager {
    constructor() {
        this.currentUser = null;
        this.sessionToken = null;
        this.loginAttempts = new Map();
        this.isLoggedIn = false;
        this.sessionTimeout = SYSTEM_CONFIG.security.sessionTimeout;
        this.maxLoginAttempts = SYSTEM_CONFIG.security.maxLoginAttempts;
        this.lockoutDuration = SYSTEM_CONFIG.security.lockoutDuration;
    }

    // ========================================
    // تسجيل الدخول - Login
    // ========================================

    async login(username, password, rememberMe = false) {
        try {
            console.log('🔄 محاولة تسجيل الدخول...');

            // التحقق من القفل
            if (this.isAccountLocked(username)) {
                throw new Error('الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متعددة');
            }

            // التحقق من صحة البيانات
            if (!username || !password) {
                throw new Error('يرجى إدخال اسم المستخدم وكلمة المرور');
            }

            // التحقق من بيانات المستخدم
            const user = await db.authenticateUser(username, password);
            
            if (!user) {
                this.recordFailedAttempt(username);
                throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
            }

            // تسجيل دخول ناجح
            this.currentUser = user;
            this.isLoggedIn = true;
            this.sessionToken = this.generateSessionToken();
            
            // حفظ الجلسة
            this.saveSession(rememberMe);
            
            // مسح محاولات الدخول الفاشلة
            this.clearFailedAttempts(username);
            
            // تحديث حالة التطبيق
            appState.currentUser = user;
            updateLastActivity();

            console.log('✅ تم تسجيل الدخول بنجاح');
            return {
                success: true,
                user: user,
                token: this.sessionToken
            };

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // ========================================
    // تسجيل الخروج - Logout
    // ========================================

    async logout() {
        try {
            console.log('🔄 تسجيل الخروج...');

            // مسح بيانات الجلسة
            this.currentUser = null;
            this.isLoggedIn = false;
            this.sessionToken = null;
            
            // مسح الجلسة المحفوظة
            this.clearSession();
            
            // تحديث حالة التطبيق
            appState.currentUser = null;

            console.log('✅ تم تسجيل الخروج بنجاح');
            return { success: true };

        } catch (error) {
            console.error('❌ خطأ في تسجيل الخروج:', error);
            return { success: false, error: error.message };
        }
    }

    // ========================================
    // التحقق من الجلسة - Session Validation
    // ========================================

    async validateSession() {
        try {
            // التحقق من وجود جلسة محفوظة
            const savedSession = this.getSavedSession();
            if (!savedSession) {
                return false;
            }

            // التحقق من صحة الرمز المميز
            if (!this.validateSessionToken(savedSession.token)) {
                this.clearSession();
                return false;
            }

            // التحقق من انتهاء صلاحية الجلسة
            if (this.isSessionExpired(savedSession.timestamp)) {
                this.clearSession();
                return false;
            }

            // استعادة بيانات المستخدم
            const user = await db.findOne('users', { id: savedSession.userId });
            if (!user || !user.isActive) {
                this.clearSession();
                return false;
            }

            // استعادة الجلسة
            this.currentUser = user;
            this.isLoggedIn = true;
            this.sessionToken = savedSession.token;
            appState.currentUser = user;

            console.log('✅ تم التحقق من الجلسة بنجاح');
            return true;

        } catch (error) {
            console.error('❌ خطأ في التحقق من الجلسة:', error);
            this.clearSession();
            return false;
        }
    }

    // ========================================
    // إدارة الصلاحيات - Permissions Management
    // ========================================

    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        try {
            const userPermissions = JSON.parse(this.currentUser.permissions || '[]');
            return userPermissions.includes('all') || userPermissions.includes(permission);
        } catch (error) {
            console.error('خطأ في التحقق من الصلاحيات:', error);
            return false;
        }
    }

    isAdmin() {
        return this.currentUser && this.currentUser.role === 'admin';
    }

    canAccess(module) {
        if (!this.isLoggedIn) return false;
        if (this.isAdmin()) return true;
        
        return this.hasPermission(module);
    }

    // ========================================
    // إدارة محاولات الدخول - Login Attempts Management
    // ========================================

    recordFailedAttempt(username) {
        const now = Date.now();
        const attempts = this.loginAttempts.get(username) || [];
        
        // إضافة المحاولة الجديدة
        attempts.push(now);
        
        // الاحتفاظ بالمحاولات الأخيرة فقط
        const recentAttempts = attempts.filter(
            timestamp => (now - timestamp) < this.lockoutDuration
        );
        
        this.loginAttempts.set(username, recentAttempts);
        
        // حفظ في localStorage
        const allAttempts = Object.fromEntries(this.loginAttempts);
        localStorage.setItem('login_attempts', JSON.stringify(allAttempts));
    }

    clearFailedAttempts(username) {
        this.loginAttempts.delete(username);
        const allAttempts = Object.fromEntries(this.loginAttempts);
        localStorage.setItem('login_attempts', JSON.stringify(allAttempts));
    }

    isAccountLocked(username) {
        const attempts = this.loginAttempts.get(username) || [];
        const now = Date.now();
        
        // تصفية المحاولات الحديثة
        const recentAttempts = attempts.filter(
            timestamp => (now - timestamp) < this.lockoutDuration
        );
        
        return recentAttempts.length >= this.maxLoginAttempts;
    }

    getRemainingLockoutTime(username) {
        if (!this.isAccountLocked(username)) return 0;
        
        const attempts = this.loginAttempts.get(username) || [];
        const oldestAttempt = Math.min(...attempts);
        const unlockTime = oldestAttempt + this.lockoutDuration;
        
        return Math.max(0, unlockTime - Date.now());
    }

    // ========================================
    // إدارة الجلسات - Session Management
    // ========================================

    generateSessionToken() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        const userInfo = this.currentUser ? this.currentUser.id : 'anonymous';
        
        return btoa(`${timestamp}-${random}-${userInfo}`);
    }

    validateSessionToken(token) {
        try {
            const decoded = atob(token);
            const parts = decoded.split('-');
            return parts.length === 3 && !isNaN(parseInt(parts[0]));
        } catch (error) {
            return false;
        }
    }

    saveSession(rememberMe = false) {
        const sessionData = {
            userId: this.currentUser.id,
            token: this.sessionToken,
            timestamp: Date.now(),
            rememberMe: rememberMe
        };

        if (rememberMe) {
            localStorage.setItem('user_session', JSON.stringify(sessionData));
        } else {
            sessionStorage.setItem('user_session', JSON.stringify(sessionData));
        }
    }

    getSavedSession() {
        try {
            // البحث في localStorage أولاً (تذكرني)
            let sessionData = localStorage.getItem('user_session');
            if (sessionData) {
                return JSON.parse(sessionData);
            }

            // البحث في sessionStorage
            sessionData = sessionStorage.getItem('user_session');
            if (sessionData) {
                return JSON.parse(sessionData);
            }

            return null;
        } catch (error) {
            console.error('خطأ في قراءة بيانات الجلسة:', error);
            return null;
        }
    }

    clearSession() {
        localStorage.removeItem('user_session');
        sessionStorage.removeItem('user_session');
    }

    isSessionExpired(timestamp) {
        const now = Date.now();
        return (now - timestamp) > this.sessionTimeout;
    }

    // ========================================
    // تغيير كلمة المرور - Change Password
    // ========================================

    async changePassword(currentPassword, newPassword) {
        try {
            if (!this.currentUser) {
                throw new Error('يجب تسجيل الدخول أولاً');
            }

            // التحقق من كلمة المرور الحالية
            const user = await db.findOne('users', { id: this.currentUser.id });
            if (!db.verifyPassword(currentPassword, user.password)) {
                throw new Error('كلمة المرور الحالية غير صحيحة');
            }

            // التحقق من قوة كلمة المرور الجديدة
            if (!this.isStrongPassword(newPassword)) {
                throw new Error('كلمة المرور الجديدة ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل وتشمل أحرف كبيرة وصغيرة وأرقام ورموز');
            }

            // تحديث كلمة المرور
            const hashedPassword = db.encryptPassword(newPassword);
            await db.update('users', this.currentUser.id, { password: hashedPassword });

            console.log('✅ تم تغيير كلمة المرور بنجاح');
            return { success: true };

        } catch (error) {
            console.error('❌ خطأ في تغيير كلمة المرور:', error);
            return { success: false, error: error.message };
        }
    }

    // ========================================
    // التحقق من قوة كلمة المرور - Password Strength
    // ========================================

    isStrongPassword(password) {
        if (!SYSTEM_CONFIG.security.requireStrongPassword) {
            return password.length >= SYSTEM_CONFIG.security.passwordMinLength;
        }

        return CONSTANTS.PASSWORD_REGEX.test(password);
    }

    getPasswordStrength(password) {
        let score = 0;
        const feedback = [];

        // الطول
        if (password.length >= 8) score += 1;
        else feedback.push('يجب أن تكون 8 أحرف على الأقل');

        // الأحرف الكبيرة
        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('يجب أن تحتوي على حرف كبير');

        // الأحرف الصغيرة
        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('يجب أن تحتوي على حرف صغير');

        // الأرقام
        if (/\d/.test(password)) score += 1;
        else feedback.push('يجب أن تحتوي على رقم');

        // الرموز
        if (/[@$!%*?&]/.test(password)) score += 1;
        else feedback.push('يجب أن تحتوي على رمز خاص');

        const strength = ['ضعيفة جداً', 'ضعيفة', 'متوسطة', 'قوية', 'قوية جداً'][score];
        
        return {
            score,
            strength,
            feedback,
            isStrong: score >= 4
        };
    }

    // ========================================
    // تهيئة النظام - Initialize System
    // ========================================

    async init() {
        try {
            // استعادة محاولات الدخول المحفوظة
            const savedAttempts = localStorage.getItem('login_attempts');
            if (savedAttempts) {
                const attempts = JSON.parse(savedAttempts);
                this.loginAttempts = new Map(Object.entries(attempts));
            }

            // التحقق من الجلسة المحفوظة
            await this.validateSession();

            console.log('✅ تم تهيئة نظام المصادقة بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام المصادقة:', error);
            return false;
        }
    }
}

// ========================================
// إنشاء مثيل من مدير المصادقة
// ========================================

const authManager = new AuthenticationManager();

// ========================================
// دوال المساعدة العامة - Global Helper Functions
// ========================================

// تسجيل الدخول
async function login(username, password, rememberMe = false) {
    return await authManager.login(username, password, rememberMe);
}

// تسجيل الخروج
async function logout() {
    return await authManager.logout();
}

// التحقق من تسجيل الدخول
function isLoggedIn() {
    return authManager.isLoggedIn;
}

// الحصول على المستخدم الحالي
function getCurrentUser() {
    return authManager.currentUser;
}

// التحقق من الصلاحيات
function hasPermission(permission) {
    return authManager.hasPermission(permission);
}

// التحقق من صلاحيات الإدارة
function isAdmin() {
    return authManager.isAdmin();
}

// التحقق من إمكانية الوصول للوحدة
function canAccessModule(module) {
    return authManager.canAccess(module);
}

// تغيير كلمة المرور
async function changePassword(currentPassword, newPassword) {
    return await authManager.changePassword(currentPassword, newPassword);
}

// التحقق من قوة كلمة المرور
function checkPasswordStrength(password) {
    return authManager.getPasswordStrength(password);
}

// تهيئة نظام المصادقة
async function initializeAuth() {
    return await authManager.init();
}

console.log('✅ تم تحميل نظام المصادقة والأمان بنجاح');
