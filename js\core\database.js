// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// قاعدة البيانات - Database Management
// ========================================

console.log('🗄️ تحميل نظام قاعدة البيانات...');

// ========================================
// فئة إدارة قاعدة البيانات - Database Manager
// ========================================

class DatabaseManager {
    constructor() {
        this.dbName = SYSTEM_CONFIG.database.name;
        this.dbVersion = SYSTEM_CONFIG.database.version;
        this.isInitialized = false;
        this.tables = {};
        this.encryptionKey = this.generateEncryptionKey();
    }

    // ========================================
    // تهيئة قاعدة البيانات - Initialize Database
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة قاعدة البيانات...');
            
            // إنشاء الجداول الأساسية
            await this.createTables();
            
            // إدراج البيانات الافتراضية
            await this.insertDefaultData();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            throw error;
        }
    }

    // ========================================
    // إنشاء الجداول - Create Tables
    // ========================================

    async createTables() {
        const tables = {
            // جدول المستخدمين
            users: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                username: 'TEXT UNIQUE NOT NULL',
                password: 'TEXT NOT NULL',
                name: 'TEXT NOT NULL',
                email: 'TEXT',
                role: 'TEXT NOT NULL DEFAULT "user"',
                permissions: 'TEXT',
                avatar: 'TEXT',
                isActive: 'BOOLEAN DEFAULT 1',
                lastLogin: 'DATETIME',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول الأطباء المحدث
            doctors: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                name: 'TEXT NOT NULL',
                specialty: 'TEXT',
                phone: 'TEXT',
                email: 'TEXT',
                address: 'TEXT',
                clinic_name: 'TEXT',
                license_number: 'TEXT',
                tax_number: 'TEXT',
                discount_percentage: 'REAL DEFAULT 0',
                commission_percentage: 'REAL DEFAULT 0',
                payment_terms: 'TEXT DEFAULT "monthly"', // monthly, weekly, per_case
                credit_limit: 'REAL DEFAULT 0',
                current_balance: 'REAL DEFAULT 0',
                total_cases: 'INTEGER DEFAULT 0',
                total_revenue: 'REAL DEFAULT 0',
                last_statement_date: 'DATE',
                preferred_payment_method: 'TEXT',
                bank_account: 'TEXT',
                notes: 'TEXT',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول قوائم أسعار الأطباء
            doctor_price_lists: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                doctor_id: 'INTEGER NOT NULL',
                prosthetic_type_id: 'INTEGER NOT NULL',
                custom_price: 'REAL NOT NULL',
                discount_percentage: 'REAL DEFAULT 0',
                minimum_quantity: 'INTEGER DEFAULT 1',
                effective_date: 'DATE NOT NULL',
                expiry_date: 'DATE',
                notes: 'TEXT',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (doctor_id)': 'REFERENCES doctors(id) ON DELETE CASCADE',
                'FOREIGN KEY (prosthetic_type_id)': 'REFERENCES prosthetic_types(id)'
            },

            // جدول كشوف حساب الأطباء
            doctor_statements: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                doctor_id: 'INTEGER NOT NULL',
                statement_number: 'TEXT UNIQUE NOT NULL',
                statement_date: 'DATE NOT NULL',
                period_from: 'DATE NOT NULL',
                period_to: 'DATE NOT NULL',
                total_cases: 'INTEGER DEFAULT 0',
                total_amount: 'REAL DEFAULT 0',
                total_discount: 'REAL DEFAULT 0',
                net_amount: 'REAL DEFAULT 0',
                previous_balance: 'REAL DEFAULT 0',
                payments_received: 'REAL DEFAULT 0',
                current_balance: 'REAL DEFAULT 0',
                status: 'TEXT DEFAULT "draft"', // draft, sent, paid, overdue
                due_date: 'DATE',
                payment_date: 'DATE',
                payment_method: 'TEXT',
                payment_reference: 'TEXT',
                notes: 'TEXT',
                generated_by: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (doctor_id)': 'REFERENCES doctors(id)'
            },

            // جدول تفاصيل كشوف الحساب
            doctor_statement_items: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                statement_id: 'INTEGER NOT NULL',
                prosthetic_id: 'INTEGER NOT NULL',
                item_date: 'DATE NOT NULL',
                patient_name: 'TEXT NOT NULL',
                prosthetic_type: 'TEXT NOT NULL',
                quantity: 'INTEGER NOT NULL',
                unit_price: 'REAL NOT NULL',
                total_price: 'REAL NOT NULL',
                discount_percentage: 'REAL DEFAULT 0',
                discount_amount: 'REAL DEFAULT 0',
                net_amount: 'REAL NOT NULL',
                notes: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (statement_id)': 'REFERENCES doctor_statements(id) ON DELETE CASCADE',
                'FOREIGN KEY (prosthetic_id)': 'REFERENCES prosthetics(id)'
            },

            // جدول مدفوعات الأطباء
            doctor_payments: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                doctor_id: 'INTEGER NOT NULL',
                statement_id: 'INTEGER',
                payment_number: 'TEXT UNIQUE NOT NULL',
                payment_date: 'DATE NOT NULL',
                amount: 'REAL NOT NULL',
                payment_method: 'TEXT NOT NULL',
                reference_number: 'TEXT',
                bank_name: 'TEXT',
                check_number: 'TEXT',
                notes: 'TEXT',
                received_by: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (doctor_id)': 'REFERENCES doctors(id)',
                'FOREIGN KEY (statement_id)': 'REFERENCES doctor_statements(id)'
            },

            // جدول الموظفين المحدث
            employees: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                employee_number: 'TEXT UNIQUE NOT NULL',
                name: 'TEXT NOT NULL',
                position: 'TEXT NOT NULL',
                department: 'TEXT',
                phone: 'TEXT',
                email: 'TEXT',
                address: 'TEXT',
                national_id: 'TEXT',
                passport_number: 'TEXT',
                birth_date: 'DATE',
                hire_date: 'DATE NOT NULL',
                contract_type: 'TEXT DEFAULT "permanent"', // permanent, temporary, part_time
                basic_salary: 'REAL DEFAULT 0',
                housing_allowance: 'REAL DEFAULT 0',
                transport_allowance: 'REAL DEFAULT 0',
                other_allowances: 'REAL DEFAULT 0',
                commission_rate: 'REAL DEFAULT 0',
                overtime_rate: 'REAL DEFAULT 0',
                bank_account: 'TEXT',
                emergency_contact: 'TEXT',
                emergency_phone: 'TEXT',
                notes: 'TEXT',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول أنواع العمولات
            commission_types: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                type_name: 'TEXT NOT NULL',
                type_code: 'TEXT UNIQUE NOT NULL',
                description: 'TEXT',
                base_rate: 'REAL DEFAULT 0',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول عمولات الموظفين
            employee_commissions: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                employee_id: 'INTEGER NOT NULL',
                commission_type_id: 'INTEGER NOT NULL',
                rate_percentage: 'REAL NOT NULL',
                effective_date: 'DATE NOT NULL',
                expiry_date: 'DATE',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (employee_id)': 'REFERENCES employees(id) ON DELETE CASCADE',
                'FOREIGN KEY (commission_type_id)': 'REFERENCES commission_types(id)'
            },

            // جدول كشوف المرتبات
            payroll_records: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                employee_id: 'INTEGER NOT NULL',
                payroll_number: 'TEXT UNIQUE NOT NULL',
                pay_period_start: 'DATE NOT NULL',
                pay_period_end: 'DATE NOT NULL',
                basic_salary: 'REAL DEFAULT 0',
                allowances: 'REAL DEFAULT 0',
                commissions: 'REAL DEFAULT 0',
                overtime_pay: 'REAL DEFAULT 0',
                gross_salary: 'REAL DEFAULT 0',
                deductions: 'REAL DEFAULT 0',
                absence_deduction: 'REAL DEFAULT 0',
                net_salary: 'REAL DEFAULT 0',
                payment_date: 'DATE',
                payment_method: 'TEXT',
                status: 'TEXT DEFAULT "draft"', // draft, approved, paid
                notes: 'TEXT',
                generated_by: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (employee_id)': 'REFERENCES employees(id)'
            },

            // جدول تفاصيل العمولات
            commission_details: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                payroll_id: 'INTEGER NOT NULL',
                prosthetic_id: 'INTEGER NOT NULL',
                commission_type: 'TEXT NOT NULL',
                quantity: 'INTEGER DEFAULT 1',
                unit_commission: 'REAL NOT NULL',
                total_commission: 'REAL NOT NULL',
                calculation_date: 'DATE NOT NULL',
                notes: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (payroll_id)': 'REFERENCES payroll_records(id) ON DELETE CASCADE',
                'FOREIGN KEY (prosthetic_id)': 'REFERENCES prosthetics(id)'
            },

            // جدول الحضور والغياب
            attendance_records: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                employee_id: 'INTEGER NOT NULL',
                attendance_date: 'DATE NOT NULL',
                check_in_time: 'TIME',
                check_out_time: 'TIME',
                total_hours: 'REAL DEFAULT 0',
                overtime_hours: 'REAL DEFAULT 0',
                status: 'TEXT DEFAULT "present"', // present, absent, late, half_day, sick_leave, vacation
                notes: 'TEXT',
                recorded_by: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (employee_id)': 'REFERENCES employees(id)',
                'UNIQUE(employee_id, attendance_date)': ''
            },

            // جدول النشاطات والسجلات
            activity_logs: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                activity_type: 'TEXT NOT NULL', // prosthetic_created, prosthetic_updated, doctor_added, employee_added, payment_received, etc.
                description: 'TEXT NOT NULL',
                entity_type: 'TEXT', // prosthetics, doctors, employees, payments, etc.
                entity_id: 'INTEGER',
                user_name: 'TEXT',
                user_role: 'TEXT',
                details: 'TEXT', // JSON string for additional details
                priority: 'TEXT DEFAULT "normal"', // low, normal, high, critical
                status: 'TEXT DEFAULT "info"', // info, success, warning, error
                ip_address: 'TEXT',
                user_agent: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول إعدادات النظام
            system_settings: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                setting_key: 'TEXT UNIQUE NOT NULL',
                setting_value: 'TEXT',
                setting_type: 'TEXT DEFAULT "string"', // string, number, boolean, json
                category: 'TEXT DEFAULT "general"', // general, dashboard, prosthetics, doctors, employees, etc.
                description: 'TEXT',
                is_editable: 'BOOLEAN DEFAULT 1',
                created_by: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول المرضى
            patients: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                name: 'TEXT NOT NULL',
                phone: 'TEXT',
                email: 'TEXT',
                address: 'TEXT',
                dateOfBirth: 'DATE',
                gender: 'TEXT',
                medicalHistory: 'TEXT',
                insuranceInfo: 'TEXT',
                notes: 'TEXT',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول التركيبات المحدث
            prosthetics: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                case_number: 'TEXT UNIQUE NOT NULL',
                patient_name: 'TEXT NOT NULL',
                patient_phone: 'TEXT',
                doctor_id: 'INTEGER NOT NULL',
                prosthetic_category: 'TEXT NOT NULL', // porcelain, zirconia, metal, dentures, orthodontics, additional
                prosthetic_type: 'TEXT NOT NULL',
                prosthetic_subtype: 'TEXT',
                material: 'TEXT',
                color_shade: 'TEXT',
                selected_teeth: 'TEXT', // JSON array of selected teeth numbers
                teeth_positions: 'TEXT', // JSON object with position details
                teeth_count: 'INTEGER DEFAULT 0',
                quantity: 'INTEGER DEFAULT 1',
                unit_price: 'REAL NOT NULL',
                total_price: 'REAL NOT NULL',
                discount_percentage: 'REAL DEFAULT 0',
                discount_amount: 'REAL DEFAULT 0',
                final_price: 'REAL NOT NULL',
                special_calculation: 'TEXT', // JSON for special calculations like partial denture
                status: 'TEXT DEFAULT "pending"', // pending, in_progress, completed, delivered, cancelled
                priority: 'TEXT DEFAULT "normal"', // low, normal, high, urgent
                start_date: 'DATE',
                completion_date: 'DATE',
                delivery_date: 'DATE',
                expected_delivery: 'DATE',
                notes: 'TEXT',
                special_instructions: 'TEXT',
                lab_notes: 'TEXT',
                technician_id: 'INTEGER',
                work_started_date: 'DATE',
                work_completed_date: 'DATE',
                delivered_date: 'DATE',
                invoice_number: 'TEXT',
                is_invoiced: 'BOOLEAN DEFAULT 0',
                payment_status: 'TEXT DEFAULT "pending"', // pending, partial, paid
                paid_amount: 'REAL DEFAULT 0',
                remaining_amount: 'REAL DEFAULT 0',
                created_by: 'TEXT',
                updated_by: 'TEXT',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (doctor_id)': 'REFERENCES doctors(id)',
                'FOREIGN KEY (technician_id)': 'REFERENCES employees(id)'
            },

            // جدول أنواع التركيبات والأسعار
            prosthetic_types: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                category: 'TEXT NOT NULL', // porcelain, zirconia, metal, dentures, orthodontics, additional
                type_name: 'TEXT NOT NULL',
                subtype: 'TEXT',
                material: 'TEXT',
                unit_price: 'REAL NOT NULL',
                description: 'TEXT',
                is_per_tooth: 'BOOLEAN DEFAULT 1',
                special_calculation: 'BOOLEAN DEFAULT 0', // for partial dentures
                min_teeth: 'INTEGER DEFAULT 1',
                max_teeth: 'INTEGER DEFAULT 32',
                color_options: 'TEXT', // JSON array of available colors
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول تفاصيل الأسنان المختارة
            prosthetic_teeth: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                prosthetic_id: 'INTEGER NOT NULL',
                tooth_number: 'INTEGER NOT NULL',
                tooth_position: 'TEXT', // upper_right, upper_left, lower_right, lower_left
                tooth_type: 'TEXT', // incisor, canine, premolar, molar
                tooth_name: 'TEXT', // Arabic name of the tooth
                special_notes: 'TEXT',
                individual_price: 'REAL',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (prosthetic_id)': 'REFERENCES prosthetics(id) ON DELETE CASCADE'
            },

            // جدول المواعيد
            appointments: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                patientId: 'INTEGER',
                doctorId: 'INTEGER',
                prostheticId: 'INTEGER',
                title: 'TEXT NOT NULL',
                description: 'TEXT',
                appointmentDate: 'DATETIME NOT NULL',
                duration: 'INTEGER DEFAULT 60',
                status: 'TEXT DEFAULT "scheduled"',
                notes: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (patientId)': 'REFERENCES patients(id)',
                'FOREIGN KEY (doctorId)': 'REFERENCES doctors(id)',
                'FOREIGN KEY (prostheticId)': 'REFERENCES prosthetics(id)'
            },

            // جدول الفواتير
            invoices: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                invoiceNumber: 'TEXT UNIQUE NOT NULL',
                patientId: 'INTEGER',
                doctorId: 'INTEGER',
                prostheticId: 'INTEGER',
                amount: 'REAL NOT NULL',
                tax: 'REAL DEFAULT 0',
                discount: 'REAL DEFAULT 0',
                totalAmount: 'REAL NOT NULL',
                status: 'TEXT DEFAULT "pending"',
                issueDate: 'DATE NOT NULL',
                dueDate: 'DATE',
                paidDate: 'DATE',
                paymentMethod: 'TEXT',
                notes: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (patientId)': 'REFERENCES patients(id)',
                'FOREIGN KEY (doctorId)': 'REFERENCES doctors(id)',
                'FOREIGN KEY (prostheticId)': 'REFERENCES prosthetics(id)'
            },

            // جدول المدفوعات
            payments: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                invoiceId: 'INTEGER',
                amount: 'REAL NOT NULL',
                paymentMethod: 'TEXT NOT NULL',
                paymentDate: 'DATE NOT NULL',
                reference: 'TEXT',
                notes: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (invoiceId)': 'REFERENCES invoices(id)'
            },

            // جدول المصروفات
            expenses: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                category: 'TEXT NOT NULL',
                description: 'TEXT NOT NULL',
                amount: 'REAL NOT NULL',
                paymentMethod: 'TEXT',
                expenseDate: 'DATE NOT NULL',
                receipt: 'TEXT',
                notes: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول المخزون
            inventory: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                name: 'TEXT NOT NULL',
                category: 'TEXT',
                description: 'TEXT',
                quantity: 'INTEGER DEFAULT 0',
                minQuantity: 'INTEGER DEFAULT 0',
                unit: 'TEXT',
                unitPrice: 'REAL DEFAULT 0',
                supplier: 'TEXT',
                lastRestockDate: 'DATE',
                expiryDate: 'DATE',
                notes: 'TEXT',
                isActive: 'BOOLEAN DEFAULT 1',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول حركات المخزون
            inventory_movements: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                inventoryId: 'INTEGER NOT NULL',
                type: 'TEXT NOT NULL', // in, out, adjustment
                quantity: 'INTEGER NOT NULL',
                reason: 'TEXT',
                reference: 'TEXT',
                movementDate: 'DATE NOT NULL',
                notes: 'TEXT',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'FOREIGN KEY (inventoryId)': 'REFERENCES inventory(id)'
            },

            // جدول الإعدادات
            settings: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                key: 'TEXT UNIQUE NOT NULL',
                value: 'TEXT',
                category: 'TEXT',
                description: 'TEXT',
                updatedAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            },

            // جدول النسخ الاحتياطية
            backups: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                filename: 'TEXT NOT NULL',
                size: 'INTEGER',
                type: 'TEXT DEFAULT "manual"',
                status: 'TEXT DEFAULT "completed"',
                createdAt: 'DATETIME DEFAULT CURRENT_TIMESTAMP'
            }
        };

        // إنشاء الجداول
        for (const [tableName, columns] of Object.entries(tables)) {
            await this.createTable(tableName, columns);
        }
    }

    // ========================================
    // إنشاء جدول واحد - Create Single Table
    // ========================================

    async createTable(tableName, columns) {
        try {
            const columnDefinitions = Object.entries(columns)
                .map(([name, definition]) => `${name} ${definition}`)
                .join(', ');

            const sql = `CREATE TABLE IF NOT EXISTS ${tableName} (${columnDefinitions})`;
            
            // في بيئة Electron، نستخدم localStorage كبديل مؤقت
            if (!localStorage.getItem(`table_${tableName}`)) {
                localStorage.setItem(`table_${tableName}`, JSON.stringify([]));
            }

            this.tables[tableName] = [];
            console.log(`✅ تم إنشاء جدول ${tableName}`);
        } catch (error) {
            console.error(`❌ خطأ في إنشاء جدول ${tableName}:`, error);
            throw error;
        }
    }

    // ========================================
    // إدراج البيانات الافتراضية - Insert Default Data
    // ========================================

    async insertDefaultData() {
        try {
            // إدراج المستخدمين الافتراضيين
            for (const user of DEFAULT_USERS) {
                await this.insertUser({
                    ...user,
                    password: this.encryptPassword(user.password),
                    permissions: JSON.stringify(user.permissions)
                });
            }

            // إدراج بيانات تجريبية للأطباء
            const sampleDoctors = [
                { name: 'د. أحمد محمد', specialty: 'طب الأسنان العام', phone: '**********', discountRate: 10 },
                { name: 'د. فاطمة علي', specialty: 'تقويم الأسنان', phone: '**********', discountRate: 15 },
                { name: 'د. محمد سعد', specialty: 'جراحة الفم والأسنان', phone: '0509876543', discountRate: 12 }
            ];

            for (const doctor of sampleDoctors) {
                await this.insert('doctors', doctor);
            }

            // إدراج بيانات تجريبية للموظفين
            const sampleEmployees = [
                { name: 'أحمد محمود', jobTitle: 'فني أسنان أول', phone: '0551234567', salary: 5000, commissionRate: 5 },
                { name: 'سارة أحمد', jobTitle: 'مساعدة طبيب أسنان', phone: '**********', salary: 3500, commissionRate: 3 }
            ];

            for (const employee of sampleEmployees) {
                await this.insert('employees', employee);
            }

            // إدراج أنواع التركيبات الافتراضية
            await this.insertDefaultProstheticTypes();

            // إدراج الأطباء الافتراضيين
            await this.insertDefaultDoctors();

            // إدراج أنواع العمولات الافتراضية
            await this.insertDefaultCommissionTypes();

            // إدراج الموظفين الافتراضيين
            await this.insertDefaultEmployees();

            // إدراج إعدادات النظام الافتراضية
            await this.insertDefaultSystemSettings();

            // إدراج النشاطات الافتراضية
            await this.insertDefaultActivityLogs();

            console.log('✅ تم إدراج البيانات الافتراضية');
        } catch (error) {
            console.error('❌ خطأ في إدراج البيانات الافتراضية:', error);
        }
    }

    // ========================================
    // إدراج أنواع التركيبات الافتراضية - Insert Default Prosthetic Types
    // ========================================

    async insertDefaultProstheticTypes() {
        try {
            const prostheticTypes = [
                // تركيبات البورسلين
                { category: 'porcelain', type_name: 'فيتا', subtype: 'Crown', material: 'Vita Porcelain', unit_price: 350, description: 'تاج بورسلين فيتا عالي الجودة', color_options: JSON.stringify(['A1', 'A2', 'A3', 'B1', 'B2', 'C1', 'C2', 'D2']) },
                { category: 'porcelain', type_name: 'جى سرام', subtype: 'Crown', material: 'GC Ceramic', unit_price: 400, description: 'تاج سيراميك جى سرام متطور', color_options: JSON.stringify(['A1', 'A2', 'A3', 'B1', 'B2', 'C1', 'C2', 'D2']) },
                { category: 'porcelain', type_name: 'فيس', subtype: 'Veneer', material: 'Feldspathic Porcelain', unit_price: 500, description: 'قشرة بورسلين فيس للأسنان الأمامية', color_options: JSON.stringify(['A1', 'A2', 'A3', 'B1', 'B2']) },

                // تركيبات الزيركون
                { category: 'zirconia', type_name: 'Full Anatomy', subtype: 'Crown', material: 'Zirconia', unit_price: 450, description: 'تاج زيركون كامل التشريح', color_options: JSON.stringify(['A1', 'A2', 'A3', 'B1', 'B2', 'C1', 'C2']) },
                { category: 'zirconia', type_name: 'Copy + Porcelain', subtype: 'Crown', material: 'Zirconia + Porcelain', unit_price: 550, description: 'تاج زيركون مع طبقة بورسلين', color_options: JSON.stringify(['A1', 'A2', 'A3', 'B1', 'B2', 'C1', 'C2']) },
                { category: 'zirconia', type_name: 'Onlay', subtype: 'Onlay', material: 'Zirconia', unit_price: 380, description: 'حشوة زيركون خارجية', color_options: JSON.stringify(['A1', 'A2', 'A3', 'B1', 'B2']) },

                // التركيبات المعدنية
                { category: 'metal', type_name: 'معدن', subtype: 'Crown', material: 'Metal Alloy', unit_price: 200, description: 'تاج معدني تقليدي', color_options: JSON.stringify(['Gold', 'Silver']) },
                { category: 'metal', type_name: 'Vitallium', subtype: 'Crown', material: 'Vitallium Alloy', unit_price: 250, description: 'تاج فيتاليوم عالي المقاومة', color_options: JSON.stringify(['Silver']) },

                // الأطقم المتحركة
                { category: 'dentures', type_name: 'طقم كامل', subtype: 'Complete', material: 'Acrylic Resin', unit_price: 800, description: 'طقم أسنان كامل علوي أو سفلي', is_per_tooth: false, min_teeth: 14, max_teeth: 14 },
                { category: 'dentures', type_name: 'طقم جزئي', subtype: 'Partial', material: 'Acrylic + Metal', unit_price: 600, description: 'طقم أسنان جزئي', is_per_tooth: false, special_calculation: true, min_teeth: 1, max_teeth: 13 },
                { category: 'dentures', type_name: 'برشل جزئي', subtype: 'Partial Bridge', material: 'Metal Framework', unit_price: 150, description: 'برشل جزئي معدني', is_per_tooth: true, special_calculation: true, min_teeth: 2, max_teeth: 10 },

                // أجهزة التقويم والحفظ
                { category: 'orthodontics', type_name: 'جهاز تقويم متحرك', subtype: 'Removable', material: 'Acrylic + Wire', unit_price: 400, description: 'جهاز تقويم متحرك', is_per_tooth: false },
                { category: 'orthodontics', type_name: 'حافظ مكان', subtype: 'Space Maintainer', material: 'Metal Wire', unit_price: 300, description: 'جهاز حفظ المكان للأطفال', is_per_tooth: false },
                { category: 'orthodontics', type_name: 'واقي أسنان', subtype: 'Night Guard', material: 'Soft Acrylic', unit_price: 250, description: 'واقي أسنان ليلي', is_per_tooth: false },

                // أعمال إضافية متخصصة
                { category: 'additional', type_name: 'إصلاح كسر', subtype: 'Repair', material: 'Various', unit_price: 100, description: 'إصلاح كسر في التركيبة', is_per_tooth: false },
                { category: 'additional', type_name: 'تعديل وضبط', subtype: 'Adjustment', material: 'Various', unit_price: 50, description: 'تعديل وضبط التركيبة', is_per_tooth: false },
                { category: 'additional', type_name: 'إعادة تبطين', subtype: 'Reline', material: 'Acrylic', unit_price: 200, description: 'إعادة تبطين الطقم', is_per_tooth: false },
                { category: 'additional', type_name: 'تلميع وتنظيف', subtype: 'Polish', material: 'Polish Compound', unit_price: 30, description: 'تلميع وتنظيف التركيبة', is_per_tooth: false }
            ];

            for (const type of prostheticTypes) {
                await this.insert('prosthetic_types', type);
            }

            console.log('✅ تم إدراج أنواع التركيبات الافتراضية');
        } catch (error) {
            console.error('❌ خطأ في إدراج أنواع التركيبات:', error);
        }
    }

    // ========================================
    // إدراج الأطباء الافتراضيين - Insert Default Doctors
    // ========================================

    async insertDefaultDoctors() {
        try {
            const defaultDoctors = [
                {
                    name: 'د. أحمد محمد السعيد',
                    specialty: 'طب الأسنان العام',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الرياض - حي النخيل - شارع الملك فهد',
                    clinic_name: 'عيادة النخيل لطب الأسنان',
                    license_number: 'DL-2024-001',
                    tax_number: '*********',
                    discount_percentage: 15,
                    commission_percentage: 0,
                    payment_terms: 'monthly',
                    credit_limit: 50000,
                    preferred_payment_method: 'bank_transfer',
                    bank_account: 'SA12********************',
                    notes: 'طبيب متميز مع خبرة 15 سنة'
                },
                {
                    name: 'د. فاطمة عبدالله الزهراني',
                    specialty: 'تقويم الأسنان',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'جدة - حي الروضة - طريق الأمير سلطان',
                    clinic_name: 'مركز الزهراني لتقويم الأسنان',
                    license_number: 'DL-2024-002',
                    tax_number: '*********',
                    discount_percentage: 20,
                    commission_percentage: 5,
                    payment_terms: 'weekly',
                    credit_limit: 75000,
                    preferred_payment_method: 'cash',
                    notes: 'متخصصة في تقويم الأسنان للأطفال والكبار'
                },
                {
                    name: 'د. خالد عبدالعزيز القحطاني',
                    specialty: 'جراحة الفم والأسنان',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الدمام - حي الفيصلية - شارع الخليج',
                    clinic_name: 'مجمع القحطاني الطبي',
                    license_number: 'DL-2024-003',
                    tax_number: '*********',
                    discount_percentage: 10,
                    commission_percentage: 0,
                    payment_terms: 'per_case',
                    credit_limit: 100000,
                    preferred_payment_method: 'check',
                    bank_account: '************************',
                    notes: 'جراح متخصص في زراعة الأسنان'
                },
                {
                    name: 'د. نورا سعد الغامدي',
                    specialty: 'طب أسنان الأطفال',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'مكة المكرمة - حي العزيزية - شارع إبراهيم الخليل',
                    clinic_name: 'عيادة براعم الأسنان',
                    license_number: 'DL-2024-004',
                    tax_number: '*********',
                    discount_percentage: 25,
                    commission_percentage: 10,
                    payment_terms: 'monthly',
                    credit_limit: 30000,
                    preferred_payment_method: 'bank_transfer',
                    notes: 'متخصصة في علاج أسنان الأطفال'
                },
                {
                    name: 'د. محمد علي الشهري',
                    specialty: 'تركيبات الأسنان',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'أبها - حي المنهل - طريق الملك عبدالعزيز',
                    clinic_name: 'مركز الشهري للتركيبات',
                    license_number: 'DL-2024-005',
                    tax_number: '*********',
                    discount_percentage: 12,
                    commission_percentage: 3,
                    payment_terms: 'monthly',
                    credit_limit: 60000,
                    preferred_payment_method: 'bank_transfer',
                    bank_account: '************************',
                    notes: 'خبير في التركيبات الثابتة والمتحركة'
                }
            ];

            for (const doctor of defaultDoctors) {
                await this.insert('doctors', doctor);
            }

            console.log('✅ تم إدراج الأطباء الافتراضيين');
        } catch (error) {
            console.error('❌ خطأ في إدراج الأطباء الافتراضيين:', error);
        }
    }

    // ========================================
    // إدراج أنواع العمولات الافتراضية - Insert Default Commission Types
    // ========================================

    async insertDefaultCommissionTypes() {
        try {
            const commissionTypes = [
                {
                    type_name: 'عمولة البورسلين',
                    type_code: 'PORCELAIN',
                    description: 'عمولة على تركيبات البورسلين (فيتا، جى سرام، فيس)',
                    base_rate: 5.0
                },
                {
                    type_name: 'عمولة الزيركون',
                    type_code: 'ZIRCONIA',
                    description: 'عمولة على تركيبات الزيركون (Full Anatomy، Copy + Porcelain، Onlay)',
                    base_rate: 7.0
                },
                {
                    type_name: 'عمولة التقويم',
                    type_code: 'ORTHODONTICS',
                    description: 'عمولة على أجهزة التقويم والحفظ',
                    base_rate: 10.0
                },
                {
                    type_name: 'عمولة الأطقم المتحركة',
                    type_code: 'DENTURES',
                    description: 'عمولة على الأطقم الكاملة والجزئية والبرشل',
                    base_rate: 8.0
                },
                {
                    type_name: 'عمولة المعدن',
                    type_code: 'METAL',
                    description: 'عمولة على التركيبات المعدنية',
                    base_rate: 4.0
                },
                {
                    type_name: 'عمولة الأعمال الإضافية',
                    type_code: 'ADDITIONAL',
                    description: 'عمولة على الأعمال الإضافية والإصلاحات',
                    base_rate: 15.0
                }
            ];

            for (const type of commissionTypes) {
                await this.insert('commission_types', type);
            }

            console.log('✅ تم إدراج أنواع العمولات الافتراضية');
        } catch (error) {
            console.error('❌ خطأ في إدراج أنواع العمولات:', error);
        }
    }

    // ========================================
    // إدراج الموظفين الافتراضيين - Insert Default Employees
    // ========================================

    async insertDefaultEmployees() {
        try {
            const defaultEmployees = [
                {
                    employee_number: 'EMP-001',
                    name: 'أحمد محمود الأحمد',
                    position: 'فني أسنان أول',
                    department: 'الإنتاج',
                    phone: '0551234567',
                    email: '<EMAIL>',
                    address: 'الرياض - حي الملز - شارع التحلية',
                    national_id: '**********',
                    birth_date: '1985-03-15',
                    hire_date: '2020-01-15',
                    contract_type: 'permanent',
                    basic_salary: 6000,
                    housing_allowance: 1000,
                    transport_allowance: 500,
                    other_allowances: 300,
                    commission_rate: 5.0,
                    overtime_rate: 25.0,
                    bank_account: '************************',
                    emergency_contact: 'فاطمة الأحمد',
                    emergency_phone: '**********',
                    notes: 'فني متميز مع خبرة 10 سنوات في تركيبات البورسلين والزيركون'
                },
                {
                    employee_number: 'EMP-002',
                    name: 'سارة أحمد العلي',
                    position: 'فنية أسنان',
                    department: 'الإنتاج',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الرياض - حي النرجس - شارع الأمير محمد بن عبدالعزيز',
                    national_id: '**********',
                    birth_date: '1990-07-22',
                    hire_date: '2021-06-01',
                    contract_type: 'permanent',
                    basic_salary: 4500,
                    housing_allowance: 800,
                    transport_allowance: 400,
                    other_allowances: 200,
                    commission_rate: 4.0,
                    overtime_rate: 20.0,
                    bank_account: '************************',
                    emergency_contact: 'محمد العلي',
                    emergency_phone: '**********',
                    notes: 'متخصصة في الأطقم المتحركة وأجهزة التقويم'
                },
                {
                    employee_number: 'EMP-003',
                    name: 'محمد سعد الغامدي',
                    position: 'مساعد فني',
                    department: 'الإنتاج',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الرياض - حي الياسمين - شارع الملك فهد',
                    national_id: '**********',
                    birth_date: '1995-11-10',
                    hire_date: '2022-03-01',
                    contract_type: 'permanent',
                    basic_salary: 3500,
                    housing_allowance: 600,
                    transport_allowance: 300,
                    other_allowances: 150,
                    commission_rate: 3.0,
                    overtime_rate: 15.0,
                    bank_account: '************************',
                    emergency_contact: 'نورا الغامدي',
                    emergency_phone: '**********',
                    notes: 'موظف جديد مع إمكانيات جيدة للتطوير'
                },
                {
                    employee_number: 'EMP-004',
                    name: 'فاطمة علي الشهري',
                    position: 'موظفة استقبال',
                    department: 'الإدارة',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الرياض - حي العليا - شارع العروبة',
                    national_id: '**********',
                    birth_date: '1988-05-18',
                    hire_date: '2019-09-01',
                    contract_type: 'permanent',
                    basic_salary: 4000,
                    housing_allowance: 700,
                    transport_allowance: 350,
                    other_allowances: 200,
                    commission_rate: 2.0,
                    overtime_rate: 18.0,
                    bank_account: '************************',
                    emergency_contact: 'خالد الشهري',
                    emergency_phone: '**********',
                    notes: 'مسؤولة عن استقبال الأطباء ومتابعة الطلبات'
                },
                {
                    employee_number: 'EMP-005',
                    name: 'عبدالله محمد القحطاني',
                    position: 'مدير الإنتاج',
                    department: 'الإدارة',
                    phone: '**********',
                    email: '<EMAIL>',
                    address: 'الرياض - حي الربوة - شارع الملك عبدالله',
                    national_id: '**********',
                    birth_date: '1980-12-05',
                    hire_date: '2018-01-01',
                    contract_type: 'permanent',
                    basic_salary: 8000,
                    housing_allowance: 1500,
                    transport_allowance: 700,
                    other_allowances: 500,
                    commission_rate: 8.0,
                    overtime_rate: 35.0,
                    bank_account: '************************',
                    emergency_contact: 'مريم القحطاني',
                    emergency_phone: '**********',
                    notes: 'مدير الإنتاج مع خبرة 15 سنة في إدارة معامل الأسنان'
                }
            ];

            for (const employee of defaultEmployees) {
                await this.insert('employees', employee);
            }

            console.log('✅ تم إدراج الموظفين الافتراضيين');
        } catch (error) {
            console.error('❌ خطأ في إدراج الموظفين الافتراضيين:', error);
        }
    }

    // ========================================
    // إدراج إعدادات النظام الافتراضية - Insert Default System Settings
    // ========================================

    async insertDefaultSystemSettings() {
        try {
            const systemSettings = [
                {
                    setting_key: 'next_case_number',
                    setting_value: '1',
                    setting_type: 'number',
                    category: 'prosthetics',
                    description: 'رقم الحالة التالي للتركيبات (يتم زيادته تلقائياً)',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'case_number_format',
                    setting_value: 'YYYYMM-###',
                    setting_type: 'string',
                    category: 'prosthetics',
                    description: 'تنسيق رقم الحالة (YYYY=السنة، MM=الشهر، ###=الرقم التسلسلي)',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'case_number_prefix',
                    setting_value: '',
                    setting_type: 'string',
                    category: 'prosthetics',
                    description: 'بادئة رقم الحالة (اختيارية)',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'partial_denture_first_tooth_price',
                    setting_value: '150',
                    setting_type: 'number',
                    category: 'prosthetics',
                    description: 'سعر أول سن في البرشل الجزئي',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'partial_denture_additional_tooth_price',
                    setting_value: '90',
                    setting_type: 'number',
                    category: 'prosthetics',
                    description: 'سعر كل سن إضافي في البرشل الجزئي',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'lab_name',
                    setting_value: 'معمل الأسنان المتقدم',
                    setting_type: 'string',
                    category: 'general',
                    description: 'اسم المعمل',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'lab_phone',
                    setting_value: '0112345678',
                    setting_type: 'string',
                    category: 'general',
                    description: 'هاتف المعمل',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'lab_address',
                    setting_value: 'الرياض - حي الملز - شارع التحلية',
                    setting_type: 'string',
                    category: 'general',
                    description: 'عنوان المعمل',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'dashboard_refresh_interval',
                    setting_value: '30',
                    setting_type: 'number',
                    category: 'dashboard',
                    description: 'فترة تحديث لوحة التحكم بالثواني',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'max_activity_logs',
                    setting_value: '1000',
                    setting_type: 'number',
                    category: 'dashboard',
                    description: 'الحد الأقصى لسجلات النشاطات',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'working_hours_start',
                    setting_value: '08:00',
                    setting_type: 'string',
                    category: 'general',
                    description: 'بداية ساعات العمل',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'working_hours_end',
                    setting_value: '17:00',
                    setting_type: 'string',
                    category: 'general',
                    description: 'نهاية ساعات العمل',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'currency_symbol',
                    setting_value: 'ريال',
                    setting_type: 'string',
                    category: 'general',
                    description: 'رمز العملة',
                    created_by: 'النظام'
                },
                {
                    setting_key: 'auto_backup_enabled',
                    setting_value: 'true',
                    setting_type: 'boolean',
                    category: 'system',
                    description: 'تفعيل النسخ الاحتياطي التلقائي',
                    created_by: 'النظام'
                }
            ];

            for (const setting of systemSettings) {
                await this.insert('system_settings', setting);
            }

            console.log('✅ تم إدراج إعدادات النظام الافتراضية');
        } catch (error) {
            console.error('❌ خطأ في إدراج إعدادات النظام:', error);
        }
    }

    // ========================================
    // إدراج النشاطات الافتراضية - Insert Default Activity Logs
    // ========================================

    async insertDefaultActivityLogs() {
        try {
            const currentTime = new Date();
            const activityLogs = [
                {
                    activity_type: 'system_initialized',
                    description: 'تم تهيئة النظام وإنشاء قاعدة البيانات',
                    entity_type: 'system',
                    entity_id: null,
                    user_name: 'النظام',
                    user_role: 'system',
                    details: JSON.stringify({ version: '2.0', database_created: true }),
                    priority: 'high',
                    status: 'success',
                    createdAt: new Date(currentTime.getTime() - 10 * 60000).toISOString() // 10 دقائق مضت
                },
                {
                    activity_type: 'default_data_inserted',
                    description: 'تم إدراج البيانات الافتراضية للنظام',
                    entity_type: 'system',
                    entity_id: null,
                    user_name: 'النظام',
                    user_role: 'system',
                    details: JSON.stringify({
                        prosthetic_types: 15,
                        doctors: 5,
                        employees: 5,
                        commission_types: 6
                    }),
                    priority: 'normal',
                    status: 'success',
                    createdAt: new Date(currentTime.getTime() - 8 * 60000).toISOString() // 8 دقائق مضت
                },
                {
                    activity_type: 'doctor_added',
                    description: 'تم إضافة طبيب جديد: د. أحمد محمد العلي',
                    entity_type: 'doctors',
                    entity_id: 1,
                    user_name: 'مدير النظام',
                    user_role: 'admin',
                    details: JSON.stringify({
                        doctor_name: 'د. أحمد محمد العلي',
                        specialty: 'طب الأسنان العام',
                        clinic: 'عيادة الأسنان المتقدمة'
                    }),
                    priority: 'normal',
                    status: 'success',
                    createdAt: new Date(currentTime.getTime() - 6 * 60000).toISOString() // 6 دقائق مضت
                },
                {
                    activity_type: 'employee_added',
                    description: 'تم إضافة موظف جديد: أحمد محمود الأحمد',
                    entity_type: 'employees',
                    entity_id: 1,
                    user_name: 'مدير النظام',
                    user_role: 'admin',
                    details: JSON.stringify({
                        employee_name: 'أحمد محمود الأحمد',
                        position: 'فني أسنان أول',
                        department: 'الإنتاج'
                    }),
                    priority: 'normal',
                    status: 'success',
                    createdAt: new Date(currentTime.getTime() - 5 * 60000).toISOString() // 5 دقائق مضت
                },
                {
                    activity_type: 'prosthetic_created',
                    description: 'تم إنشاء حالة جديدة: تاج زيركون للمريض محمد علي أحمد',
                    entity_type: 'prosthetics',
                    entity_id: 1,
                    user_name: 'موظف الاستقبال',
                    user_role: 'receptionist',
                    details: JSON.stringify({
                        patient_name: 'محمد علي أحمد',
                        prosthetic_type: 'تاج زيركون',
                        doctor: 'د. أحمد محمد العلي',
                        case_number: 'CASE-001'
                    }),
                    priority: 'normal',
                    status: 'success',
                    createdAt: new Date(currentTime.getTime() - 4 * 60000).toISOString() // 4 دقائق مضت
                },
                {
                    activity_type: 'prosthetic_status_updated',
                    description: 'تم تحديث حالة التركيبة إلى "قيد التنفيذ"',
                    entity_type: 'prosthetics',
                    entity_id: 1,
                    user_name: 'أحمد محمود الأحمد',
                    user_role: 'technician',
                    details: JSON.stringify({
                        old_status: 'pending',
                        new_status: 'in_progress',
                        case_number: 'CASE-001'
                    }),
                    priority: 'normal',
                    status: 'info',
                    createdAt: new Date(currentTime.getTime() - 3 * 60000).toISOString() // 3 دقائق مضت
                },
                {
                    activity_type: 'payment_received',
                    description: 'تم استلام دفعة مالية بقيمة 500 ريال من د. أحمد محمد العلي',
                    entity_type: 'payments',
                    entity_id: 1,
                    user_name: 'موظف الاستقبال',
                    user_role: 'receptionist',
                    details: JSON.stringify({
                        amount: 500,
                        currency: 'SAR',
                        doctor: 'د. أحمد محمد العلي',
                        payment_method: 'نقدي'
                    }),
                    priority: 'normal',
                    status: 'success',
                    createdAt: new Date(currentTime.getTime() - 2 * 60000).toISOString() // دقيقتان مضت
                },
                {
                    activity_type: 'backup_created',
                    description: 'تم إنشاء نسخة احتياطية من قاعدة البيانات',
                    entity_type: 'system',
                    entity_id: null,
                    user_name: 'النظام',
                    user_role: 'system',
                    details: JSON.stringify({
                        backup_size: '2.5 MB',
                        backup_location: 'backups/dental_lab_backup_' + new Date().toISOString().split('T')[0] + '.db'
                    }),
                    priority: 'low',
                    status: 'success',
                    createdAt: new Date(currentTime.getTime() - 1 * 60000).toISOString() // دقيقة مضت
                },
                {
                    activity_type: 'user_login',
                    description: 'تم تسجيل دخول مدير النظام',
                    entity_type: 'users',
                    entity_id: 1,
                    user_name: 'مدير النظام',
                    user_role: 'admin',
                    details: JSON.stringify({
                        login_time: new Date().toISOString(),
                        session_id: 'sess_' + Math.random().toString(36).substr(2, 9)
                    }),
                    priority: 'low',
                    status: 'info',
                    createdAt: new Date(currentTime.getTime() - 30000).toISOString() // 30 ثانية مضت
                },
                {
                    activity_type: 'dashboard_accessed',
                    description: 'تم الوصول إلى لوحة التحكم الرئيسية',
                    entity_type: 'dashboard',
                    entity_id: null,
                    user_name: 'مدير النظام',
                    user_role: 'admin',
                    details: JSON.stringify({
                        access_time: new Date().toISOString(),
                        page: 'dashboard'
                    }),
                    priority: 'low',
                    status: 'info',
                    createdAt: currentTime.toISOString() // الآن
                }
            ];

            for (const log of activityLogs) {
                await this.insert('activity_logs', log);
            }

            console.log('✅ تم إدراج النشاطات الافتراضية');
        } catch (error) {
            console.error('❌ خطأ في إدراج النشاطات الافتراضية:', error);
        }
    }

    // إدراج بيانات
    async insert(tableName, data) {
        try {
            const table = this.getTable(tableName);
            const id = table.length > 0 ? Math.max(...table.map(item => item.id || 0)) + 1 : 1;
            
            const record = {
                id,
                ...data,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            table.push(record);
            this.saveTable(tableName, table);
            
            return record;
        } catch (error) {
            console.error(`خطأ في إدراج البيانات في جدول ${tableName}:`, error);
            throw error;
        }
    }

    // تحديث بيانات
    async update(tableName, id, data) {
        try {
            const table = this.getTable(tableName);
            const index = table.findIndex(item => item.id === id);
            
            if (index === -1) {
                throw new Error('السجل غير موجود');
            }

            table[index] = {
                ...table[index],
                ...data,
                updatedAt: new Date().toISOString()
            };

            this.saveTable(tableName, table);
            return table[index];
        } catch (error) {
            console.error(`خطأ في تحديث البيانات في جدول ${tableName}:`, error);
            throw error;
        }
    }

    // حذف بيانات
    async delete(tableName, id) {
        try {
            const table = this.getTable(tableName);
            const index = table.findIndex(item => item.id === id);
            
            if (index === -1) {
                throw new Error('السجل غير موجود');
            }

            const deletedRecord = table.splice(index, 1)[0];
            this.saveTable(tableName, table);
            
            return deletedRecord;
        } catch (error) {
            console.error(`خطأ في حذف البيانات من جدول ${tableName}:`, error);
            throw error;
        }
    }

    // البحث عن سجل واحد
    async findOne(tableName, criteria) {
        try {
            const table = this.getTable(tableName);
            return table.find(item => this.matchesCriteria(item, criteria)) || null;
        } catch (error) {
            console.error(`خطأ في البحث في جدول ${tableName}:`, error);
            throw error;
        }
    }

    // البحث عن عدة سجلات
    async findMany(tableName, criteria = {}, options = {}) {
        try {
            const table = this.getTable(tableName);
            let results = table.filter(item => this.matchesCriteria(item, criteria));

            // ترتيب النتائج
            if (options.orderBy) {
                const [field, direction = 'asc'] = options.orderBy.split(' ');
                results.sort((a, b) => {
                    const aVal = a[field];
                    const bVal = b[field];
                    const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
                    return direction === 'desc' ? -comparison : comparison;
                });
            }

            // تحديد النتائج
            if (options.limit) {
                const offset = options.offset || 0;
                results = results.slice(offset, offset + options.limit);
            }

            return results;
        } catch (error) {
            console.error(`خطأ في البحث في جدول ${tableName}:`, error);
            throw error;
        }
    }

    // الحصول على جميع السجلات
    async findAll(tableName) {
        return this.findMany(tableName);
    }

    // ========================================
    // دوال المساعدة - Helper Functions
    // ========================================

    getTable(tableName) {
        if (!this.tables[tableName]) {
            const stored = localStorage.getItem(`table_${tableName}`);
            this.tables[tableName] = stored ? JSON.parse(stored) : [];
        }
        return this.tables[tableName];
    }

    saveTable(tableName, data) {
        this.tables[tableName] = data;
        localStorage.setItem(`table_${tableName}`, JSON.stringify(data));
    }

    matchesCriteria(item, criteria) {
        return Object.entries(criteria).every(([key, value]) => {
            if (typeof value === 'object' && value !== null) {
                // دعم العمليات المتقدمة مثل $gt, $lt, $like
                return Object.entries(value).every(([op, val]) => {
                    switch (op) {
                        case '$gt': return item[key] > val;
                        case '$gte': return item[key] >= val;
                        case '$lt': return item[key] < val;
                        case '$lte': return item[key] <= val;
                        case '$like': return String(item[key]).toLowerCase().includes(String(val).toLowerCase());
                        case '$in': return Array.isArray(val) && val.includes(item[key]);
                        default: return item[key] === val;
                    }
                });
            }
            return item[key] === value;
        });
    }

    // تشفير كلمة المرور
    encryptPassword(password) {
        // استخدام CryptoJS للتشفير
        if (typeof CryptoJS !== 'undefined') {
            return CryptoJS.SHA256(password + this.encryptionKey).toString();
        }
        // fallback بسيط
        return btoa(password + this.encryptionKey);
    }

    // التحقق من كلمة المرور
    verifyPassword(password, hashedPassword) {
        return this.encryptPassword(password) === hashedPassword;
    }

    // إنشاء مفتاح التشفير
    generateEncryptionKey() {
        const stored = localStorage.getItem('encryption_key');
        if (stored) return stored;
        
        const key = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        localStorage.setItem('encryption_key', key);
        return key;
    }

    // إدراج مستخدم جديد
    async insertUser(userData) {
        return this.insert('users', userData);
    }

    // البحث عن مستخدم بالاسم
    async findUserByUsername(username) {
        return this.findOne('users', { username });
    }

    // التحقق من بيانات تسجيل الدخول
    async authenticateUser(username, password) {
        const user = await this.findUserByUsername(username);
        if (!user || !user.isActive) return null;
        
        if (this.verifyPassword(password, user.password)) {
            // تحديث آخر تسجيل دخول
            await this.update('users', user.id, { lastLogin: new Date().toISOString() });
            return { ...user, password: undefined }; // إزالة كلمة المرور من النتيجة
        }
        
        return null;
    }
}

// ========================================
// تهيئة قاعدة البيانات العامة
// ========================================

// إنشاء مثيل من مدير قاعدة البيانات
const databaseManager = new DatabaseManager();

// دالة تهيئة قاعدة البيانات
async function initializeDatabase() {
    try {
        await databaseManager.init();
        db = databaseManager; // تعيين المتغير العام
        return true;
    } catch (error) {
        console.error('❌ فشل في تهيئة قاعدة البيانات:', error);
        return false;
    }
}

console.log('✅ تم تحميل نظام قاعدة البيانات بنجاح');
