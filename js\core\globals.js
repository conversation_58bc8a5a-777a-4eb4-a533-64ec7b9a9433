// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// المتغيرات العامة - Global Variables
// ========================================

console.log('🌐 تحميل المتغيرات العامة...');

// ========================================
// معلومات التطبيق - Application Info
// ========================================

const APP_INFO = {
    name: 'نظام إدارة معمل الأسنان المتقدم',
    version: '2.0.0',
    build: '2025.01.01',
    author: 'Dental Lab Systems',
    description: 'تطبيق احترافي لإدارة معامل الأسنان',
    website: 'https://dentallab.com',
    support: 'https://dentallab.com/support',
    documentation: 'https://dentallab.com/docs'
};

// ========================================
// إعدادات النظام - System Settings
// ========================================

const SYSTEM_CONFIG = {
    // إعدادات قاعدة البيانات
    database: {
        name: 'dental_lab_v2.db',
        version: 2,
        encryption: true,
        backup: {
            enabled: true,
            interval: 24 * 60 * 60 * 1000, // 24 ساعة
            maxBackups: 7
        }
    },
    
    // إعدادات الواجهة
    ui: {
        theme: 'light', // light, dark, auto
        language: 'ar', // ar, en
        animations: true,
        sounds: false,
        notifications: true,
        autoSave: true,
        autoSaveInterval: 30000 // 30 ثانية
    },
    
    // إعدادات الأمان
    security: {
        sessionTimeout: 8 * 60 * 60 * 1000, // 8 ساعات
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 دقيقة
        passwordMinLength: 8,
        requireStrongPassword: true
    },
    
    // إعدادات التقارير
    reports: {
        defaultFormat: 'pdf',
        includeCharts: true,
        includeStatistics: true,
        watermark: true,
        compression: true
    },
    
    // إعدادات التصدير
    export: {
        defaultFormat: 'xlsx',
        includeHeaders: true,
        dateFormat: 'YYYY-MM-DD',
        encoding: 'utf-8'
    }
};

// ========================================
// المتغيرات العامة - Global Variables
// ========================================

// حالة التطبيق
let appState = {
    isInitialized: false,
    isLoading: false,
    currentUser: null,
    currentModule: 'dashboard',
    currentLanguage: 'ar',
    currentTheme: 'light',
    isOnline: navigator.onLine,
    lastActivity: Date.now()
};

// قاعدة البيانات
let db = null;
let dbConnection = null;

// مديري الوحدات
let moduleManagers = {
    dashboard: null,
    prosthetics: null,
    doctors: null,
    employees: null,
    financial: null,
    reports: null,
    inventory: null,
    patients: null,
    appointments: null,
    invoicing: null,
    backup: null,
    users: null,
    settings: null,
    salaries: null,
    external: null,
    expenses: null,
    payments: null,
    owner: null,
    budget: null
};

// عناصر DOM الرئيسية
let domElements = {
    app: null,
    header: null,
    sidebar: null,
    content: null,
    statusBar: null,
    loadingScreen: null,
    loginModal: null,
    notificationContainer: null
};

// ========================================
// بيانات المستخدم الافتراضية - Default User Data
// ========================================

const DEFAULT_USERS = [
    {
        id: 1,
        username: 'dentalmanager',
        password: 'DentalLab@2025!', // سيتم تشفيرها
        name: 'مدير النظام',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['all'],
        avatar: null,
        isActive: true,
        lastLogin: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: 2,
        username: 'dentaltechnician',
        password: 'Tech@2025!',
        name: 'فني الأسنان',
        email: '<EMAIL>',
        role: 'technician',
        permissions: ['prosthetics', 'inventory', 'reports'],
        avatar: null,
        isActive: true,
        lastLogin: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];

// ========================================
// قوائم التنقل - Navigation Menus
// ========================================

const NAVIGATION_MENU = [
    {
        id: 'dashboard',
        name: 'لوحة التحكم',
        nameEn: 'Dashboard',
        icon: 'fas fa-home',
        url: '#dashboard',
        permissions: ['all'],
        badge: null,
        children: []
    },
    {
        id: 'prosthetics',
        name: 'تسجيل التركيبات',
        nameEn: 'Prosthetics Registration',
        icon: 'fas fa-tooth',
        url: '#prosthetics',
        permissions: ['prosthetics', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'doctors',
        name: 'إدارة الأطباء',
        nameEn: 'Doctors Management',
        icon: 'fas fa-user-md',
        url: '#doctors',
        permissions: ['doctors', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'employees',
        name: 'إدارة الموظفين',
        nameEn: 'Employees Management',
        icon: 'fas fa-user-tie',
        url: '#employees',
        permissions: ['employees', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'external',
        name: 'المعامل الخارجية',
        nameEn: 'External Labs',
        icon: 'fas fa-building',
        url: '#external',
        permissions: ['external', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'financial',
        name: 'الحسابات المالية',
        nameEn: 'Financial Accounts',
        icon: 'fas fa-chart-line',
        url: '#financial',
        permissions: ['financial', 'admin'],
        badge: null,
        children: [
            {
                id: 'expenses',
                name: 'إدارة المصروفات',
                nameEn: 'Expenses Management',
                icon: 'fas fa-money-bill-wave',
                url: '#expenses'
            },
            {
                id: 'payments',
                name: 'تسجيل المدفوعات',
                nameEn: 'Payments Registration',
                icon: 'fas fa-credit-card',
                url: '#payments'
            },
            {
                id: 'owner',
                name: 'حساب المالك',
                nameEn: 'Owner Account',
                icon: 'fas fa-user-crown',
                url: '#owner'
            },
            {
                id: 'budget',
                name: 'الميزانية',
                nameEn: 'Budget',
                icon: 'fas fa-calculator',
                url: '#budget'
            }
        ]
    },
    {
        id: 'reports',
        name: 'التقارير',
        nameEn: 'Reports',
        icon: 'fas fa-chart-bar',
        url: '#reports',
        permissions: ['reports', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'backup',
        name: 'النسخ الاحتياطي',
        nameEn: 'Backup',
        icon: 'fas fa-database',
        url: '#backup',
        permissions: ['backup', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'users',
        name: 'إدارة المستخدمين',
        nameEn: 'Users Management',
        icon: 'fas fa-users',
        url: '#users',
        permissions: ['admin'],
        badge: null,
        children: []
    },
    {
        id: 'settings',
        name: 'إعدادات المعمل',
        nameEn: 'Lab Settings',
        icon: 'fas fa-cog',
        url: '#settings',
        permissions: ['settings', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'salaries',
        name: 'مرتبات الموظفين',
        nameEn: 'Employee Salaries',
        icon: 'fas fa-money-check',
        url: '#salaries',
        permissions: ['salaries', 'admin'],
        badge: null,
        children: []
    },
    {
        id: 'inventory',
        name: 'إدارة المخزون',
        nameEn: 'Inventory Management',
        icon: 'fas fa-boxes',
        url: '#inventory',
        permissions: ['inventory', 'admin'],
        badge: null,
        children: []
    }
];

// ========================================
// أنواع التركيبات - Prosthetic Types
// ========================================

const PROSTHETIC_TYPES = [
    { id: 'crown', name: 'تاج', nameEn: 'Crown', price: 800 },
    { id: 'bridge', name: 'جسر', nameEn: 'Bridge', price: 1500 },
    { id: 'denture', name: 'طقم أسنان', nameEn: 'Denture', price: 2200 },
    { id: 'partial', name: 'طقم جزئي', nameEn: 'Partial Denture', price: 1800 },
    { id: 'implant', name: 'زراعة', nameEn: 'Implant', price: 3000 },
    { id: 'veneer', name: 'عدسة', nameEn: 'Veneer', price: 600 },
    { id: 'inlay', name: 'حشوة تجميلية', nameEn: 'Inlay', price: 400 },
    { id: 'onlay', name: 'حشوة كبيرة', nameEn: 'Onlay', price: 500 }
];

// ========================================
// حالات التركيبات - Prosthetic Statuses
// ========================================

const PROSTHETIC_STATUSES = [
    { id: 'pending', name: 'قيد التنفيذ', nameEn: 'Pending', color: '#ff9800' },
    { id: 'in_progress', name: 'جاري العمل', nameEn: 'In Progress', color: '#2196f3' },
    { id: 'completed', name: 'مكتمل', nameEn: 'Completed', color: '#4caf50' },
    { id: 'delivered', name: 'تم التسليم', nameEn: 'Delivered', color: '#9c27b0' },
    { id: 'cancelled', name: 'ملغي', nameEn: 'Cancelled', color: '#f44336' }
];

// ========================================
// طرق الدفع - Payment Methods
// ========================================

const PAYMENT_METHODS = [
    { id: 'cash', name: 'نقد', nameEn: 'Cash', icon: 'fas fa-money-bill' },
    { id: 'card', name: 'بطاقة ائتمان', nameEn: 'Credit Card', icon: 'fas fa-credit-card' },
    { id: 'transfer', name: 'تحويل بنكي', nameEn: 'Bank Transfer', icon: 'fas fa-university' },
    { id: 'check', name: 'شيك', nameEn: 'Check', icon: 'fas fa-money-check' },
    { id: 'installment', name: 'تقسيط', nameEn: 'Installment', icon: 'fas fa-calendar-alt' }
];

// ========================================
// فئات المصروفات - Expense Categories
// ========================================

const EXPENSE_CATEGORIES = [
    { id: 'materials', name: 'مواد خام', nameEn: 'Raw Materials', icon: 'fas fa-boxes' },
    { id: 'equipment', name: 'معدات', nameEn: 'Equipment', icon: 'fas fa-tools' },
    { id: 'utilities', name: 'مرافق', nameEn: 'Utilities', icon: 'fas fa-bolt' },
    { id: 'maintenance', name: 'صيانة', nameEn: 'Maintenance', icon: 'fas fa-wrench' },
    { id: 'salaries', name: 'رواتب', nameEn: 'Salaries', icon: 'fas fa-money-check' },
    { id: 'rent', name: 'إيجار', nameEn: 'Rent', icon: 'fas fa-home' },
    { id: 'marketing', name: 'تسويق', nameEn: 'Marketing', icon: 'fas fa-bullhorn' },
    { id: 'other', name: 'أخرى', nameEn: 'Other', icon: 'fas fa-ellipsis-h' }
];

// ========================================
// الثوابت العامة - General Constants
// ========================================

const CONSTANTS = {
    // أحجام الملفات
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10 MB
    MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5 MB
    
    // أنواع الملفات المسموحة
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    
    // تنسيقات التاريخ
    DATE_FORMAT: 'YYYY-MM-DD',
    DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
    DISPLAY_DATE_FORMAT: 'DD/MM/YYYY',
    DISPLAY_DATETIME_FORMAT: 'DD/MM/YYYY HH:mm',
    
    // العملة
    CURRENCY: 'ر.س',
    CURRENCY_EN: 'SAR',
    
    // أرقام الهواتف
    PHONE_REGEX: /^(05|5)[0-9]{8}$/,
    
    // البريد الإلكتروني
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    
    // كلمة المرور
    PASSWORD_REGEX: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
};

// ========================================
// رسائل النظام - System Messages
// ========================================

const MESSAGES = {
    ar: {
        success: {
            save: 'تم الحفظ بنجاح',
            update: 'تم التحديث بنجاح',
            delete: 'تم الحذف بنجاح',
            login: 'تم تسجيل الدخول بنجاح',
            logout: 'تم تسجيل الخروج بنجاح',
            export: 'تم التصدير بنجاح',
            import: 'تم الاستيراد بنجاح',
            backup: 'تم إنشاء النسخة الاحتياطية بنجاح'
        },
        error: {
            general: 'حدث خطأ غير متوقع',
            network: 'خطأ في الاتصال',
            validation: 'يرجى التحقق من البيانات المدخلة',
            permission: 'ليس لديك صلاحية للوصول',
            notFound: 'العنصر المطلوب غير موجود',
            duplicate: 'البيانات موجودة مسبقاً',
            required: 'هذا الحقل مطلوب',
            invalid: 'البيانات غير صحيحة'
        },
        warning: {
            unsaved: 'لديك تغييرات غير محفوظة',
            delete: 'هل أنت متأكد من الحذف؟',
            logout: 'هل تريد تسجيل الخروج؟',
            overwrite: 'سيتم استبدال البيانات الموجودة'
        },
        info: {
            loading: 'جاري التحميل...',
            processing: 'جاري المعالجة...',
            saving: 'جاري الحفظ...',
            empty: 'لا توجد بيانات للعرض',
            welcome: 'مرحباً بك في نظام إدارة معمل الأسنان'
        }
    },
    en: {
        success: {
            save: 'Saved successfully',
            update: 'Updated successfully',
            delete: 'Deleted successfully',
            login: 'Logged in successfully',
            logout: 'Logged out successfully',
            export: 'Exported successfully',
            import: 'Imported successfully',
            backup: 'Backup created successfully'
        },
        error: {
            general: 'An unexpected error occurred',
            network: 'Network error',
            validation: 'Please check the entered data',
            permission: 'You do not have permission to access',
            notFound: 'Requested item not found',
            duplicate: 'Data already exists',
            required: 'This field is required',
            invalid: 'Invalid data'
        },
        warning: {
            unsaved: 'You have unsaved changes',
            delete: 'Are you sure you want to delete?',
            logout: 'Do you want to log out?',
            overwrite: 'Existing data will be replaced'
        },
        info: {
            loading: 'Loading...',
            processing: 'Processing...',
            saving: 'Saving...',
            empty: 'No data to display',
            welcome: 'Welcome to Dental Lab Management System'
        }
    }
};

// ========================================
// دوال المساعدة العامة - Global Helper Functions
// ========================================

// الحصول على رسالة بناءً على اللغة الحالية
function getMessage(category, key) {
    const lang = appState.currentLanguage || 'ar';
    return MESSAGES[lang]?.[category]?.[key] || key;
}

// الحصول على اسم العنصر بناءً على اللغة
function getLocalizedName(item, fallback = '') {
    const lang = appState.currentLanguage || 'ar';
    if (lang === 'en' && item.nameEn) {
        return item.nameEn;
    }
    return item.name || fallback;
}

// تحديث آخر نشاط
function updateLastActivity() {
    appState.lastActivity = Date.now();
}

// التحقق من انتهاء الجلسة
function isSessionExpired() {
    const now = Date.now();
    const sessionTimeout = SYSTEM_CONFIG.security.sessionTimeout;
    return (now - appState.lastActivity) > sessionTimeout;
}

console.log('✅ تم تحميل المتغيرات العامة بنجاح');
