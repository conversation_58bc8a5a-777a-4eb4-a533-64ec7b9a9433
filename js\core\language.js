// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// نظام اللغات - Language System
// ========================================

console.log('🌐 تحميل نظام اللغات...');

// ========================================
// فئة إدارة اللغات - Language Manager
// ========================================

class LanguageManager {
    constructor() {
        this.currentLanguage = 'ar';
        this.supportedLanguages = ['ar', 'en'];
        this.translations = {};
        this.isRTL = true;
        this.dateLocale = 'ar-SA';
    }

    // ========================================
    // تهيئة النظام - Initialize System
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة نظام اللغات...');

            // تحميل الترجمات
            await this.loadTranslations();

            // استعادة اللغة المحفوظة
            this.restoreSavedLanguage();

            // تطبيق اللغة الحالية
            this.applyLanguage(this.currentLanguage);

            console.log('✅ تم تهيئة نظام اللغات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام اللغات:', error);
            return false;
        }
    }

    // ========================================
    // تحميل الترجمات - Load Translations
    // ========================================

    async loadTranslations() {
        // الترجمات العربية
        this.translations.ar = {
            // العناوين الرئيسية
            appTitle: 'نظام إدارة معمل الأسنان المتقدم',
            dashboard: 'لوحة التحكم',
            prosthetics: 'تسجيل التركيبات',
            doctors: 'إدارة الأطباء',
            employees: 'إدارة الموظفين',
            external: 'المعامل الخارجية',
            financial: 'الحسابات المالية',
            expenses: 'إدارة المصروفات',
            payments: 'تسجيل المدفوعات',
            owner: 'حساب المالك',
            budget: 'الميزانية',
            reports: 'التقارير',
            backup: 'النسخ الاحتياطي',
            users: 'إدارة المستخدمين',
            settings: 'إعدادات المعمل',
            salaries: 'مرتبات الموظفين',
            inventory: 'إدارة المخزون',

            // أزرار عامة
            add: 'إضافة',
            edit: 'تعديل',
            delete: 'حذف',
            save: 'حفظ',
            cancel: 'إلغاء',
            close: 'إغلاق',
            search: 'بحث',
            filter: 'تصفية',
            export: 'تصدير',
            import: 'استيراد',
            print: 'طباعة',
            refresh: 'تحديث',
            back: 'رجوع',
            next: 'التالي',
            previous: 'السابق',
            submit: 'إرسال',
            reset: 'إعادة تعيين',

            // نموذج تسجيل الدخول
            login: 'تسجيل الدخول',
            logout: 'تسجيل الخروج',
            username: 'اسم المستخدم',
            password: 'كلمة المرور',
            rememberMe: 'تذكرني',
            forgotPassword: 'نسيت كلمة المرور؟',
            loginButton: 'دخول',

            // الحقول العامة
            name: 'الاسم',
            email: 'البريد الإلكتروني',
            phone: 'الهاتف',
            address: 'العنوان',
            date: 'التاريخ',
            time: 'الوقت',
            status: 'الحالة',
            type: 'النوع',
            category: 'الفئة',
            description: 'الوصف',
            notes: 'ملاحظات',
            price: 'السعر',
            amount: 'المبلغ',
            quantity: 'الكمية',
            total: 'الإجمالي',

            // حالات التركيبات
            pending: 'قيد التنفيذ',
            inProgress: 'جاري العمل',
            completed: 'مكتمل',
            delivered: 'تم التسليم',
            cancelled: 'ملغي',

            // طرق الدفع
            cash: 'نقد',
            card: 'بطاقة ائتمان',
            transfer: 'تحويل بنكي',
            check: 'شيك',
            installment: 'تقسيط',

            // الرسائل
            loading: 'جاري التحميل...',
            saving: 'جاري الحفظ...',
            processing: 'جاري المعالجة...',
            success: 'تم بنجاح',
            error: 'حدث خطأ',
            warning: 'تحذير',
            info: 'معلومات',
            confirm: 'تأكيد',
            noData: 'لا توجد بيانات',
            selectOption: 'اختر خياراً',

            // التواريخ والأوقات
            today: 'اليوم',
            yesterday: 'أمس',
            tomorrow: 'غداً',
            thisWeek: 'هذا الأسبوع',
            thisMonth: 'هذا الشهر',
            thisYear: 'هذا العام',

            // أيام الأسبوع
            sunday: 'الأحد',
            monday: 'الاثنين',
            tuesday: 'الثلاثاء',
            wednesday: 'الأربعاء',
            thursday: 'الخميس',
            friday: 'الجمعة',
            saturday: 'السبت',

            // الشهور
            january: 'يناير',
            february: 'فبراير',
            march: 'مارس',
            april: 'أبريل',
            may: 'مايو',
            june: 'يونيو',
            july: 'يوليو',
            august: 'أغسطس',
            september: 'سبتمبر',
            october: 'أكتوبر',
            november: 'نوفمبر',
            december: 'ديسمبر'
        };

        // الترجمات الإنجليزية
        this.translations.en = {
            // Main Titles
            appTitle: 'Advanced Dental Lab Management System',
            dashboard: 'Dashboard',
            prosthetics: 'Prosthetics Registration',
            doctors: 'Doctors Management',
            employees: 'Employees Management',
            external: 'External Labs',
            financial: 'Financial Accounts',
            expenses: 'Expenses Management',
            payments: 'Payments Registration',
            owner: 'Owner Account',
            budget: 'Budget',
            reports: 'Reports',
            backup: 'Backup',
            users: 'Users Management',
            settings: 'Lab Settings',
            salaries: 'Employee Salaries',
            inventory: 'Inventory Management',

            // General Buttons
            add: 'Add',
            edit: 'Edit',
            delete: 'Delete',
            save: 'Save',
            cancel: 'Cancel',
            close: 'Close',
            search: 'Search',
            filter: 'Filter',
            export: 'Export',
            import: 'Import',
            print: 'Print',
            refresh: 'Refresh',
            back: 'Back',
            next: 'Next',
            previous: 'Previous',
            submit: 'Submit',
            reset: 'Reset',

            // Login Form
            login: 'Login',
            logout: 'Logout',
            username: 'Username',
            password: 'Password',
            rememberMe: 'Remember Me',
            forgotPassword: 'Forgot Password?',
            loginButton: 'Sign In',

            // General Fields
            name: 'Name',
            email: 'Email',
            phone: 'Phone',
            address: 'Address',
            date: 'Date',
            time: 'Time',
            status: 'Status',
            type: 'Type',
            category: 'Category',
            description: 'Description',
            notes: 'Notes',
            price: 'Price',
            amount: 'Amount',
            quantity: 'Quantity',
            total: 'Total',

            // Prosthetic Statuses
            pending: 'Pending',
            inProgress: 'In Progress',
            completed: 'Completed',
            delivered: 'Delivered',
            cancelled: 'Cancelled',

            // Payment Methods
            cash: 'Cash',
            card: 'Credit Card',
            transfer: 'Bank Transfer',
            check: 'Check',
            installment: 'Installment',

            // Messages
            loading: 'Loading...',
            saving: 'Saving...',
            processing: 'Processing...',
            success: 'Success',
            error: 'Error',
            warning: 'Warning',
            info: 'Information',
            confirm: 'Confirm',
            noData: 'No Data Available',
            selectOption: 'Select Option',

            // Dates and Times
            today: 'Today',
            yesterday: 'Yesterday',
            tomorrow: 'Tomorrow',
            thisWeek: 'This Week',
            thisMonth: 'This Month',
            thisYear: 'This Year',

            // Days of Week
            sunday: 'Sunday',
            monday: 'Monday',
            tuesday: 'Tuesday',
            wednesday: 'Wednesday',
            thursday: 'Thursday',
            friday: 'Friday',
            saturday: 'Saturday',

            // Months
            january: 'January',
            february: 'February',
            march: 'March',
            april: 'April',
            may: 'May',
            june: 'June',
            july: 'July',
            august: 'August',
            september: 'September',
            october: 'October',
            november: 'November',
            december: 'December'
        };
    }

    // ========================================
    // تطبيق اللغة - Apply Language
    // ========================================

    applyLanguage(languageCode) {
        if (!this.supportedLanguages.includes(languageCode)) {
            console.warn(`اللغة ${languageCode} غير مدعومة`);
            return false;
        }

        try {
            this.currentLanguage = languageCode;
            this.isRTL = languageCode === 'ar';
            this.dateLocale = languageCode === 'ar' ? 'ar-SA' : 'en-US';

            // تحديث خصائص HTML
            document.documentElement.lang = languageCode;
            document.documentElement.dir = this.isRTL ? 'rtl' : 'ltr';
            document.body.setAttribute('data-lang', languageCode);

            // تحديث حالة التطبيق
            appState.currentLanguage = languageCode;

            // تحديث النصوص
            this.updateTexts();

            // تحديث التواريخ
            this.updateDates();

            // حفظ اللغة
            this.saveLanguage(languageCode);

            // تحديث زر تبديل اللغة
            this.updateLanguageToggle();

            console.log(`✅ تم تطبيق اللغة: ${languageCode}`);
            return true;

        } catch (error) {
            console.error('❌ خطأ في تطبيق اللغة:', error);
            return false;
        }
    }

    // ========================================
    // تحديث النصوص - Update Texts
    // ========================================

    updateTexts() {
        // تحديث العناصر التي تحتوي على data-translate
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.translate(key);
            
            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'search')) {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        });

        // تحديث العناوين
        const titleElements = document.querySelectorAll('[data-translate-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-translate-title');
            element.title = this.translate(key);
        });
    }

    // ========================================
    // تحديث التواريخ - Update Dates
    // ========================================

    updateDates() {
        // تحديث عرض الوقت الحالي
        const timeDisplay = document.getElementById('time-display');
        if (timeDisplay) {
            this.updateCurrentTime();
        }

        // تحديث جميع التواريخ في الصفحة
        const dateElements = document.querySelectorAll('[data-date]');
        dateElements.forEach(element => {
            const dateValue = element.getAttribute('data-date');
            if (dateValue) {
                element.textContent = this.formatDate(new Date(dateValue));
            }
        });
    }

    // ========================================
    // الترجمة - Translation
    // ========================================

    translate(key, params = {}) {
        const translation = this.translations[this.currentLanguage]?.[key] || key;
        
        // استبدال المعاملات
        let result = translation;
        Object.entries(params).forEach(([param, value]) => {
            result = result.replace(`{${param}}`, value);
        });
        
        return result;
    }

    // اختصار للترجمة
    t(key, params = {}) {
        return this.translate(key, params);
    }

    // ========================================
    // تنسيق التواريخ - Date Formatting
    // ========================================

    formatDate(date, options = {}) {
        if (!date) return '';
        
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        
        try {
            return new Intl.DateTimeFormat(this.dateLocale, formatOptions).format(new Date(date));
        } catch (error) {
            console.error('خطأ في تنسيق التاريخ:', error);
            return date.toString();
        }
    }

    formatTime(date, options = {}) {
        if (!date) return '';
        
        const defaultOptions = {
            hour: '2-digit',
            minute: '2-digit'
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        
        try {
            return new Intl.DateTimeFormat(this.dateLocale, formatOptions).format(new Date(date));
        } catch (error) {
            console.error('خطأ في تنسيق الوقت:', error);
            return date.toString();
        }
    }

    formatDateTime(date, options = {}) {
        if (!date) return '';
        
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        
        try {
            return new Intl.DateTimeFormat(this.dateLocale, formatOptions).format(new Date(date));
        } catch (error) {
            console.error('خطأ في تنسيق التاريخ والوقت:', error);
            return date.toString();
        }
    }

    // ========================================
    // تحديث الوقت الحالي - Update Current Time
    // ========================================

    updateCurrentTime() {
        const timeDisplay = document.getElementById('time-display');
        if (timeDisplay) {
            const now = new Date();
            timeDisplay.textContent = this.formatDateTime(now, {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }

    // ========================================
    // إدارة اللغة المحفوظة - Saved Language Management
    // ========================================

    saveLanguage(languageCode) {
        localStorage.setItem('preferred_language', languageCode);
    }

    restoreSavedLanguage() {
        const savedLanguage = localStorage.getItem('preferred_language');
        if (savedLanguage && this.supportedLanguages.includes(savedLanguage)) {
            this.currentLanguage = savedLanguage;
        }
    }

    // ========================================
    // تحديث زر تبديل اللغة - Update Language Toggle
    // ========================================

    updateLanguageToggle() {
        const languageToggle = document.getElementById('language-toggle');
        if (languageToggle) {
            const langText = languageToggle.querySelector('.lang-text');
            if (langText) {
                langText.textContent = this.currentLanguage === 'ar' ? 'EN' : 'ع';
            }
        }
    }

    // ========================================
    // تبديل اللغة - Toggle Language
    // ========================================

    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        return this.applyLanguage(newLanguage);
    }

    // ========================================
    // الحصول على معلومات اللغة - Get Language Info
    // ========================================

    getCurrentLanguage() {
        return this.currentLanguage;
    }

    isRightToLeft() {
        return this.isRTL;
    }

    getSupportedLanguages() {
        return [...this.supportedLanguages];
    }

    getDateLocale() {
        return this.dateLocale;
    }
}

// ========================================
// إنشاء مثيل من مدير اللغات
// ========================================

const languageManager = new LanguageManager();

// ========================================
// دوال المساعدة العامة - Global Helper Functions
// ========================================

// الترجمة
function translate(key, params = {}) {
    return languageManager.translate(key, params);
}

// اختصار للترجمة
function t(key, params = {}) {
    return languageManager.t(key, params);
}

// تطبيق اللغة
function setLanguage(languageCode) {
    return languageManager.applyLanguage(languageCode);
}

// تبديل اللغة
function toggleLanguage() {
    return languageManager.toggleLanguage();
}

// تنسيق التاريخ
function formatDate(date, options = {}) {
    return languageManager.formatDate(date, options);
}

// تنسيق الوقت
function formatTime(date, options = {}) {
    return languageManager.formatTime(date, options);
}

// تنسيق التاريخ والوقت
function formatDateTime(date, options = {}) {
    return languageManager.formatDateTime(date, options);
}

// الحصول على اللغة الحالية
function getCurrentLanguage() {
    return languageManager.getCurrentLanguage();
}

// التحقق من اتجاه النص
function isRTL() {
    return languageManager.isRightToLeft();
}

// تهيئة نظام اللغات
async function initializeLanguage() {
    return await languageManager.init();
}

// تحديث الوقت كل ثانية
setInterval(() => {
    languageManager.updateCurrentTime();
}, 1000);

console.log('✅ تم تحميل نظام اللغات بنجاح');
