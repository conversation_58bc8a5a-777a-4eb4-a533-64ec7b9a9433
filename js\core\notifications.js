// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// نظام الإشعارات - Notifications System
// ========================================

console.log('🔔 تحميل نظام الإشعارات...');

// ========================================
// فئة إدارة الإشعارات - Notifications Manager
// ========================================

class NotificationsManager {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5 ثواني
        this.soundEnabled = SYSTEM_CONFIG.ui.sounds;
        this.notificationsEnabled = SYSTEM_CONFIG.ui.notifications;
        this.counter = 0;
    }

    // ========================================
    // تهيئة النظام - Initialize System
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة نظام الإشعارات...');

            // إنشاء حاوي الإشعارات
            this.createContainer();

            // تحديث عداد الإشعارات
            this.updateNotificationBadge();

            // إعداد أحداث الإشعارات
            this.setupEventListeners();

            console.log('✅ تم تهيئة نظام الإشعارات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام الإشعارات:', error);
            return false;
        }
    }

    // ========================================
    // إنشاء حاوي الإشعارات - Create Container
    // ========================================

    createContainer() {
        this.container = document.getElementById('notification-container');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        }
    }

    // ========================================
    // إظهار إشعار - Show Notification
    // ========================================

    show(message, type = 'info', options = {}) {
        if (!this.notificationsEnabled) {
            console.log(`إشعار ${type}: ${message}`);
            return null;
        }

        try {
            const notification = this.createNotification(message, type, options);
            this.addNotification(notification);
            this.updateNotificationBadge();
            
            // تشغيل الصوت إذا كان مفعلاً
            if (this.soundEnabled && options.sound !== false) {
                this.playNotificationSound(type);
            }

            return notification;
        } catch (error) {
            console.error('❌ خطأ في إظهار الإشعار:', error);
            return null;
        }
    }

    // ========================================
    // إنشاء إشعار - Create Notification
    // ========================================

    createNotification(message, type, options) {
        const id = `notification-${Date.now()}-${++this.counter}`;
        const duration = options.duration !== undefined ? options.duration : this.defaultDuration;
        const title = options.title || this.getDefaultTitle(type);
        const actions = options.actions || [];
        const persistent = options.persistent || false;

        const notification = {
            id,
            message,
            type,
            title,
            actions,
            persistent,
            duration,
            timestamp: new Date(),
            element: null,
            timeout: null
        };

        // إنشاء عنصر DOM
        notification.element = this.createNotificationElement(notification);

        return notification;
    }

    // ========================================
    // إنشاء عنصر الإشعار - Create Notification Element
    // ========================================

    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification notification-${notification.type}`;
        element.setAttribute('data-notification-id', notification.id);

        // أيقونة الإشعار
        const icon = this.getNotificationIcon(notification.type);
        
        // محتوى الإشعار
        element.innerHTML = `
            <div class="notification-icon">
                <i class="${icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                ${notification.actions.length > 0 ? this.createActionsHTML(notification.actions) : ''}
            </div>
            <button class="notification-close" onclick="notificationsManager.dismiss('${notification.id}')">
                <i class="fas fa-times"></i>
            </button>
        `;

        // إضافة أحداث الإجراءات
        if (notification.actions.length > 0) {
            this.setupNotificationActions(element, notification);
        }

        return element;
    }

    // ========================================
    // إضافة الإشعار - Add Notification
    // ========================================

    addNotification(notification) {
        // إضافة للقائمة
        this.notifications.unshift(notification);

        // إزالة الإشعارات الزائدة
        if (this.notifications.length > this.maxNotifications) {
            const oldNotifications = this.notifications.splice(this.maxNotifications);
            oldNotifications.forEach(n => this.removeNotificationElement(n));
        }

        // إضافة للحاوي
        this.container.insertBefore(notification.element, this.container.firstChild);

        // تطبيق الرسوم المتحركة
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);

        // إعداد الإزالة التلقائية
        if (!notification.persistent && notification.duration > 0) {
            notification.timeout = setTimeout(() => {
                this.dismiss(notification.id);
            }, notification.duration);
        }
    }

    // ========================================
    // إزالة الإشعار - Dismiss Notification
    // ========================================

    dismiss(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;

        try {
            // إلغاء المؤقت
            if (notification.timeout) {
                clearTimeout(notification.timeout);
            }

            // إزالة الرسوم المتحركة
            notification.element.classList.remove('show');
            notification.element.classList.add('hide');

            // إزالة من DOM بعد انتهاء الرسوم المتحركة
            setTimeout(() => {
                this.removeNotificationElement(notification);
            }, 300);

            // إزالة من القائمة
            const index = this.notifications.findIndex(n => n.id === notificationId);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }

            this.updateNotificationBadge();

        } catch (error) {
            console.error('❌ خطأ في إزالة الإشعار:', error);
        }
    }

    // ========================================
    // إزالة عنصر الإشعار - Remove Notification Element
    // ========================================

    removeNotificationElement(notification) {
        if (notification.element && notification.element.parentNode) {
            notification.element.parentNode.removeChild(notification.element);
        }
    }

    // ========================================
    // إزالة جميع الإشعارات - Clear All Notifications
    // ========================================

    clearAll() {
        try {
            this.notifications.forEach(notification => {
                if (notification.timeout) {
                    clearTimeout(notification.timeout);
                }
                this.removeNotificationElement(notification);
            });

            this.notifications = [];
            this.updateNotificationBadge();

            console.log('✅ تم مسح جميع الإشعارات');
        } catch (error) {
            console.error('❌ خطأ في مسح الإشعارات:', error);
        }
    }

    // ========================================
    // دوال الإشعارات المختصرة - Shortcut Functions
    // ========================================

    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', { ...options, duration: 8000 });
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', { ...options, duration: 6000 });
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    // ========================================
    // دوال المساعدة - Helper Functions
    // ========================================

    getDefaultTitle(type) {
        const titles = {
            success: translate('success'),
            error: translate('error'),
            warning: translate('warning'),
            info: translate('info')
        };
        return titles[type] || translate('info');
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || 'fas fa-info-circle';
    }

    createActionsHTML(actions) {
        return `
            <div class="notification-actions">
                ${actions.map(action => `
                    <button class="notification-action" data-action="${action.id}">
                        ${action.icon ? `<i class="${action.icon}"></i>` : ''}
                        ${action.label}
                    </button>
                `).join('')}
            </div>
        `;
    }

    setupNotificationActions(element, notification) {
        const actionButtons = element.querySelectorAll('.notification-action');
        actionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const actionId = e.target.getAttribute('data-action');
                const action = notification.actions.find(a => a.id === actionId);
                
                if (action && typeof action.callback === 'function') {
                    action.callback(notification);
                }

                // إزالة الإشعار بعد تنفيذ الإجراء (إلا إذا كان مستمراً)
                if (!action.keepOpen) {
                    this.dismiss(notification.id);
                }
            });
        });
    }

    // ========================================
    // تحديث شارة الإشعارات - Update Notification Badge
    // ========================================

    updateNotificationBadge() {
        const badge = document.getElementById('notification-count');
        if (badge) {
            const count = this.notifications.length;
            badge.textContent = count;
            badge.style.display = count > 0 ? 'block' : 'none';
        }
    }

    // ========================================
    // تشغيل أصوات الإشعارات - Play Notification Sounds
    // ========================================

    playNotificationSound(type) {
        if (!this.soundEnabled) return;

        try {
            // إنشاء صوت بسيط باستخدام Web Audio API
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // تحديد تردد الصوت حسب نوع الإشعار
            const frequencies = {
                success: 800,
                error: 400,
                warning: 600,
                info: 500
            };

            oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);

        } catch (error) {
            console.warn('تعذر تشغيل صوت الإشعار:', error);
        }
    }

    // ========================================
    // إعداد أحداث الإشعارات - Setup Event Listeners
    // ========================================

    setupEventListeners() {
        // زر الإشعارات في الشريط العلوي
        const notificationBtn = document.getElementById('notifications-btn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.toggleNotificationsPanel();
            });
        }

        // إشعارات النظام
        document.addEventListener('system-notification', (event) => {
            const { message, type, options } = event.detail;
            this.show(message, type, options);
        });
    }

    // ========================================
    // لوحة الإشعارات - Notifications Panel
    // ========================================

    toggleNotificationsPanel() {
        // إنشاء أو إظهار/إخفاء لوحة الإشعارات
        let panel = document.getElementById('notifications-panel');
        
        if (!panel) {
            panel = this.createNotificationsPanel();
            document.body.appendChild(panel);
        }

        panel.classList.toggle('show');
    }

    createNotificationsPanel() {
        const panel = document.createElement('div');
        panel.id = 'notifications-panel';
        panel.className = 'notifications-panel';
        
        panel.innerHTML = `
            <div class="panel-header">
                <h3>${translate('notifications')}</h3>
                <button class="btn btn-sm" onclick="notificationsManager.clearAll()">
                    ${translate('clearAll')}
                </button>
            </div>
            <div class="panel-content">
                <div class="notifications-list" id="notifications-list">
                    ${this.notifications.length === 0 ? 
                        `<div class="empty-state">${translate('noNotifications')}</div>` :
                        this.notifications.map(n => this.createNotificationListItem(n)).join('')
                    }
                </div>
            </div>
        `;

        return panel;
    }

    createNotificationListItem(notification) {
        return `
            <div class="notification-item">
                <div class="notification-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${formatDateTime(notification.timestamp)}</div>
                </div>
                <button class="notification-dismiss" onclick="notificationsManager.dismiss('${notification.id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }

    // ========================================
    // إعدادات الإشعارات - Notification Settings
    // ========================================

    enableNotifications() {
        this.notificationsEnabled = true;
        localStorage.setItem('notifications_enabled', 'true');
    }

    disableNotifications() {
        this.notificationsEnabled = false;
        localStorage.setItem('notifications_enabled', 'false');
    }

    enableSounds() {
        this.soundEnabled = true;
        localStorage.setItem('notification_sounds', 'true');
    }

    disableSounds() {
        this.soundEnabled = false;
        localStorage.setItem('notification_sounds', 'false');
    }

    setMaxNotifications(max) {
        this.maxNotifications = Math.max(1, Math.min(10, max));
        localStorage.setItem('max_notifications', this.maxNotifications.toString());
    }

    setDefaultDuration(duration) {
        this.defaultDuration = Math.max(1000, duration);
        localStorage.setItem('notification_duration', this.defaultDuration.toString());
    }

    // ========================================
    // استعادة الإعدادات - Restore Settings
    // ========================================

    restoreSettings() {
        const notificationsEnabled = localStorage.getItem('notifications_enabled');
        if (notificationsEnabled !== null) {
            this.notificationsEnabled = notificationsEnabled === 'true';
        }

        const soundEnabled = localStorage.getItem('notification_sounds');
        if (soundEnabled !== null) {
            this.soundEnabled = soundEnabled === 'true';
        }

        const maxNotifications = localStorage.getItem('max_notifications');
        if (maxNotifications) {
            this.maxNotifications = parseInt(maxNotifications);
        }

        const duration = localStorage.getItem('notification_duration');
        if (duration) {
            this.defaultDuration = parseInt(duration);
        }
    }
}

// ========================================
// إنشاء مثيل من مدير الإشعارات
// ========================================

const notificationsManager = new NotificationsManager();

// ========================================
// دوال المساعدة العامة - Global Helper Functions
// ========================================

// إظهار إشعار عام
function showNotification(message, type = 'info', options = {}) {
    return notificationsManager.show(message, type, options);
}

// إشعارات مختصرة
function showSuccess(message, options = {}) {
    return notificationsManager.success(message, options);
}

function showError(message, options = {}) {
    return notificationsManager.error(message, options);
}

function showWarning(message, options = {}) {
    return notificationsManager.warning(message, options);
}

function showInfo(message, options = {}) {
    return notificationsManager.info(message, options);
}

// إزالة إشعار
function dismissNotification(id) {
    return notificationsManager.dismiss(id);
}

// مسح جميع الإشعارات
function clearAllNotifications() {
    return notificationsManager.clearAll();
}

// تهيئة نظام الإشعارات
async function initializeNotifications() {
    notificationsManager.restoreSettings();
    return await notificationsManager.init();
}

console.log('✅ تم تحميل نظام الإشعارات بنجاح');
