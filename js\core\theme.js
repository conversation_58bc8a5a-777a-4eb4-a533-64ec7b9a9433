// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// نظام الثيمات - Theme System
// ========================================

console.log('🎨 تحميل نظام الثيمات...');

// ========================================
// فئة إدارة الثيمات - Theme Manager
// ========================================

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.supportedThemes = ['light', 'dark', 'auto', 'high-contrast', 'dental'];
        this.systemPreference = 'light';
        this.isAutoMode = false;
        this.mediaQuery = null;
    }

    // ========================================
    // تهيئة النظام - Initialize System
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة نظام الثيمات...');

            // إعداد مراقب تفضيلات النظام
            this.setupSystemPreferenceWatcher();

            // استعادة الثيم المحفوظ
            this.restoreSavedTheme();

            // تطبيق الثيم الحالي
            this.applyTheme(this.currentTheme);

            // إعداد أحداث التبديل
            this.setupThemeToggle();

            console.log('✅ تم تهيئة نظام الثيمات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام الثيمات:', error);
            return false;
        }
    }

    // ========================================
    // تطبيق الثيم - Apply Theme
    // ========================================

    applyTheme(themeName) {
        if (!this.supportedThemes.includes(themeName)) {
            console.warn(`الثيم ${themeName} غير مدعوم`);
            return false;
        }

        try {
            this.currentTheme = themeName;

            // إزالة جميع فئات الثيمات السابقة
            document.body.classList.remove(...this.supportedThemes.map(theme => `theme-${theme}`));

            // تحديد الثيم الفعلي المطبق
            let actualTheme = themeName;
            
            if (themeName === 'auto') {
                this.isAutoMode = true;
                actualTheme = this.systemPreference;
            } else {
                this.isAutoMode = false;
            }

            // تطبيق فئة الثيم
            document.body.classList.add(`theme-${actualTheme}`);
            document.body.setAttribute('data-theme', actualTheme);

            // تحديث حالة التطبيق
            appState.currentTheme = actualTheme;

            // تحديث متغيرات CSS المخصصة
            this.updateCSSVariables(actualTheme);

            // تحديث أيقونة التبديل
            this.updateThemeToggleIcon(actualTheme);

            // حفظ الثيم
            this.saveTheme(themeName);

            // إطلاق حدث تغيير الثيم
            this.dispatchThemeChangeEvent(actualTheme);

            console.log(`✅ تم تطبيق الثيم: ${actualTheme}`);
            return true;

        } catch (error) {
            console.error('❌ خطأ في تطبيق الثيم:', error);
            return false;
        }
    }

    // ========================================
    // تحديث متغيرات CSS - Update CSS Variables
    // ========================================

    updateCSSVariables(theme) {
        const root = document.documentElement;

        // متغيرات خاصة بكل ثيم
        const themeVariables = {
            light: {
                '--app-background': '#f8fafc',
                '--app-surface': '#ffffff',
                '--app-text-primary': '#1e293b',
                '--app-text-secondary': '#64748b',
                '--app-border': '#e2e8f0',
                '--app-shadow': 'rgba(0, 0, 0, 0.1)'
            },
            dark: {
                '--app-background': '#0f172a',
                '--app-surface': '#1e293b',
                '--app-text-primary': '#f1f5f9',
                '--app-text-secondary': '#cbd5e1',
                '--app-border': '#475569',
                '--app-shadow': 'rgba(0, 0, 0, 0.3)'
            },
            'high-contrast': {
                '--app-background': '#ffffff',
                '--app-surface': '#ffffff',
                '--app-text-primary': '#000000',
                '--app-text-secondary': '#333333',
                '--app-border': '#000000',
                '--app-shadow': 'rgba(0, 0, 0, 0.5)'
            },
            dental: {
                '--app-primary': '#1976d2',
                '--app-secondary': '#00acc1',
                '--app-accent': '#43a047',
                '--app-warning': '#fb8c00'
            }
        };

        // تطبيق المتغيرات
        const variables = themeVariables[theme] || {};
        Object.entries(variables).forEach(([property, value]) => {
            root.style.setProperty(property, value);
        });
    }

    // ========================================
    // مراقب تفضيلات النظام - System Preference Watcher
    // ========================================

    setupSystemPreferenceWatcher() {
        // التحقق من دعم المتصفح
        if (window.matchMedia) {
            this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            // تحديد التفضيل الحالي
            this.systemPreference = this.mediaQuery.matches ? 'dark' : 'light';

            // مراقبة التغييرات
            this.mediaQuery.addEventListener('change', (e) => {
                this.systemPreference = e.matches ? 'dark' : 'light';
                
                // تطبيق التغيير إذا كان في الوضع التلقائي
                if (this.isAutoMode) {
                    this.applyTheme('auto');
                }
            });
        }
    }

    // ========================================
    // إعداد زر التبديل - Setup Theme Toggle
    // ========================================

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    // ========================================
    // تبديل الثيم - Toggle Theme
    // ========================================

    toggleTheme() {
        const themeOrder = ['light', 'dark', 'auto'];
        const currentIndex = themeOrder.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeOrder.length;
        const nextTheme = themeOrder[nextIndex];
        
        return this.applyTheme(nextTheme);
    }

    // ========================================
    // تحديث أيقونة التبديل - Update Toggle Icon
    // ========================================

    updateThemeToggleIcon(theme) {
        const themeToggle = document.getElementById('theme-toggle');
        if (!themeToggle) return;

        const icon = themeToggle.querySelector('i');
        if (!icon) return;

        // إزالة الفئات السابقة
        icon.classList.remove('fa-sun', 'fa-moon', 'fa-adjust', 'fa-eye', 'fa-tooth');

        // تحديد الأيقونة المناسبة
        const iconMap = {
            light: 'fa-sun',
            dark: 'fa-moon',
            auto: 'fa-adjust',
            'high-contrast': 'fa-eye',
            dental: 'fa-tooth'
        };

        icon.classList.add(iconMap[theme] || 'fa-sun');

        // تحديث التلميح
        const tooltipMap = {
            light: 'الوضع النهاري',
            dark: 'الوضع الليلي',
            auto: 'الوضع التلقائي',
            'high-contrast': 'التباين العالي',
            dental: 'ثيم معمل الأسنان'
        };

        themeToggle.title = tooltipMap[theme] || 'تبديل الثيم';
    }

    // ========================================
    // إدارة الثيم المحفوظ - Saved Theme Management
    // ========================================

    saveTheme(themeName) {
        localStorage.setItem('preferred_theme', themeName);
    }

    restoreSavedTheme() {
        const savedTheme = localStorage.getItem('preferred_theme');
        if (savedTheme && this.supportedThemes.includes(savedTheme)) {
            this.currentTheme = savedTheme;
        } else {
            // استخدام تفضيل النظام كافتراضي
            this.currentTheme = 'auto';
        }
    }

    // ========================================
    // إطلاق أحداث التغيير - Dispatch Change Events
    // ========================================

    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themechange', {
            detail: {
                theme: theme,
                isAutoMode: this.isAutoMode,
                systemPreference: this.systemPreference
            }
        });
        
        document.dispatchEvent(event);
    }

    // ========================================
    // دوال المساعدة - Helper Functions
    // ========================================

    getCurrentTheme() {
        return this.currentTheme;
    }

    getActualTheme() {
        if (this.isAutoMode) {
            return this.systemPreference;
        }
        return this.currentTheme;
    }

    getSupportedThemes() {
        return [...this.supportedThemes];
    }

    isDarkMode() {
        return this.getActualTheme() === 'dark';
    }

    isLightMode() {
        return this.getActualTheme() === 'light';
    }

    isAutoMode() {
        return this.isAutoMode;
    }

    getSystemPreference() {
        return this.systemPreference;
    }

    // ========================================
    // ثيمات مخصصة - Custom Themes
    // ========================================

    createCustomTheme(name, variables) {
        try {
            // إضافة الثيم للقائمة المدعومة
            if (!this.supportedThemes.includes(name)) {
                this.supportedThemes.push(name);
            }

            // إنشاء قواعد CSS للثيم المخصص
            const style = document.createElement('style');
            style.id = `custom-theme-${name}`;
            
            let css = `.theme-${name} {\n`;
            Object.entries(variables).forEach(([property, value]) => {
                css += `  ${property}: ${value};\n`;
            });
            css += '}';
            
            style.textContent = css;
            document.head.appendChild(style);

            console.log(`✅ تم إنشاء الثيم المخصص: ${name}`);
            return true;

        } catch (error) {
            console.error(`❌ خطأ في إنشاء الثيم المخصص ${name}:`, error);
            return false;
        }
    }

    removeCustomTheme(name) {
        try {
            // إزالة من القائمة المدعومة
            const index = this.supportedThemes.indexOf(name);
            if (index > -1) {
                this.supportedThemes.splice(index, 1);
            }

            // إزالة قواعد CSS
            const style = document.getElementById(`custom-theme-${name}`);
            if (style) {
                style.remove();
            }

            // التبديل لثيم آخر إذا كان هذا هو الثيم الحالي
            if (this.currentTheme === name) {
                this.applyTheme('light');
            }

            console.log(`✅ تم حذف الثيم المخصص: ${name}`);
            return true;

        } catch (error) {
            console.error(`❌ خطأ في حذف الثيم المخصص ${name}:`, error);
            return false;
        }
    }

    // ========================================
    // تصدير واستيراد الثيمات - Export/Import Themes
    // ========================================

    exportTheme(themeName) {
        try {
            const computedStyle = getComputedStyle(document.documentElement);
            const themeData = {
                name: themeName,
                version: '1.0',
                variables: {},
                created: new Date().toISOString()
            };

            // استخراج متغيرات CSS
            const cssVariables = Array.from(document.styleSheets)
                .flatMap(sheet => Array.from(sheet.cssRules))
                .filter(rule => rule.selectorText === `.theme-${themeName}`)
                .flatMap(rule => Array.from(rule.style))
                .filter(prop => prop.startsWith('--'));

            cssVariables.forEach(variable => {
                themeData.variables[variable] = computedStyle.getPropertyValue(variable).trim();
            });

            return JSON.stringify(themeData, null, 2);

        } catch (error) {
            console.error(`❌ خطأ في تصدير الثيم ${themeName}:`, error);
            return null;
        }
    }

    importTheme(themeData) {
        try {
            const theme = typeof themeData === 'string' ? JSON.parse(themeData) : themeData;
            
            if (!theme.name || !theme.variables) {
                throw new Error('بيانات الثيم غير صحيحة');
            }

            return this.createCustomTheme(theme.name, theme.variables);

        } catch (error) {
            console.error('❌ خطأ في استيراد الثيم:', error);
            return false;
        }
    }

    // ========================================
    // تحسينات الأداء - Performance Optimizations
    // ========================================

    preloadThemes() {
        // تحميل مسبق لموارد الثيمات
        this.supportedThemes.forEach(theme => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = `#theme-${theme}`;
            document.head.appendChild(link);
        });
    }

    optimizeTransitions() {
        // تحسين الانتقالات بين الثيمات
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    }
}

// ========================================
// إنشاء مثيل من مدير الثيمات
// ========================================

const themeManager = new ThemeManager();

// ========================================
// دوال المساعدة العامة - Global Helper Functions
// ========================================

// تطبيق ثيم
function setTheme(themeName) {
    return themeManager.applyTheme(themeName);
}

// تبديل الثيم
function toggleTheme() {
    return themeManager.toggleTheme();
}

// الحصول على الثيم الحالي
function getCurrentTheme() {
    return themeManager.getCurrentTheme();
}

// الحصول على الثيم الفعلي
function getActualTheme() {
    return themeManager.getActualTheme();
}

// التحقق من الوضع الليلي
function isDarkMode() {
    return themeManager.isDarkMode();
}

// التحقق من الوضع النهاري
function isLightMode() {
    return themeManager.isLightMode();
}

// إنشاء ثيم مخصص
function createCustomTheme(name, variables) {
    return themeManager.createCustomTheme(name, variables);
}

// تصدير ثيم
function exportTheme(themeName) {
    return themeManager.exportTheme(themeName);
}

// استيراد ثيم
function importTheme(themeData) {
    return themeManager.importTheme(themeData);
}

// تهيئة نظام الثيمات
async function initializeTheme() {
    return await themeManager.init();
}

// مراقبة تغييرات الثيم
document.addEventListener('themechange', (event) => {
    console.log('🎨 تم تغيير الثيم:', event.detail);
    
    // تحديث أي عناصر تحتاج لإعادة رسم
    const charts = document.querySelectorAll('canvas');
    charts.forEach(chart => {
        // إعادة رسم الرسوم البيانية مع الألوان الجديدة
        if (chart.chart && typeof chart.chart.update === 'function') {
            chart.chart.update();
        }
    });
});

console.log('✅ تم تحميل نظام الثيمات بنجاح');
