// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// الأدوات المساعدة - Utility Functions
// ========================================

console.log('🛠️ تحميل الأدوات المساعدة...');

// ========================================
// دوال التحقق من صحة البيانات - Validation Functions
// ========================================

// التحقق من البريد الإلكتروني
function isValidEmail(email) {
    return CONSTANTS.EMAIL_REGEX.test(email);
}

// التحقق من رقم الهاتف
function isValidPhone(phone) {
    return CONSTANTS.PHONE_REGEX.test(phone);
}

// التحقق من قوة كلمة المرور
function isValidPassword(password) {
    return CONSTANTS.PASSWORD_REGEX.test(password);
}

// التحقق من أن القيمة ليست فارغة
function isNotEmpty(value) {
    return value !== null && value !== undefined && value.toString().trim() !== '';
}

// التحقق من أن القيمة رقم
function isNumber(value) {
    return !isNaN(parseFloat(value)) && isFinite(value);
}

// التحقق من أن القيمة رقم صحيح
function isInteger(value) {
    return Number.isInteger(Number(value));
}

// التحقق من أن القيمة رقم موجب
function isPositiveNumber(value) {
    return isNumber(value) && parseFloat(value) > 0;
}

// التحقق من التاريخ
function isValidDate(date) {
    return date instanceof Date && !isNaN(date);
}

// ========================================
// دوال تنسيق البيانات - Data Formatting Functions
// ========================================

// تنسيق الأرقام
function formatNumber(number, decimals = 2) {
    if (!isNumber(number)) return '0';
    
    return new Intl.NumberFormat(getCurrentLanguage() === 'ar' ? 'ar-SA' : 'en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

// تنسيق العملة
function formatCurrency(amount, currency = CONSTANTS.CURRENCY) {
    if (!isNumber(amount)) return `0 ${currency}`;
    
    const formatted = formatNumber(amount, 2);
    return getCurrentLanguage() === 'ar' ? `${formatted} ${currency}` : `${currency} ${formatted}`;
}

// تنسيق النسبة المئوية
function formatPercentage(value, decimals = 1) {
    if (!isNumber(value)) return '0%';
    
    return `${formatNumber(value, decimals)}%`;
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تنسيق رقم الهاتف
function formatPhoneNumber(phone) {
    if (!phone) return '';
    
    // إزالة جميع الأحرف غير الرقمية
    const cleaned = phone.replace(/\D/g, '');
    
    // تنسيق الرقم السعودي
    if (cleaned.length === 10 && cleaned.startsWith('05')) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    }
    
    return phone;
}

// ========================================
// دوال التلاعب بالنصوص - String Manipulation Functions
// ========================================

// تحويل النص للأحرف الكبيرة الأولى
function capitalize(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// تحويل النص لعنوان
function toTitleCase(str) {
    if (!str) return '';
    return str.replace(/\w\S*/g, (txt) => 
        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
}

// إزالة المسافات الزائدة
function trimSpaces(str) {
    if (!str) return '';
    return str.replace(/\s+/g, ' ').trim();
}

// تحويل النص لـ slug
function slugify(str) {
    if (!str) return '';
    return str
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
}

// اقتطاع النص
function truncateText(str, length = 100, suffix = '...') {
    if (!str || str.length <= length) return str;
    return str.substring(0, length) + suffix;
}

// ========================================
// دوال التلاعب بالمصفوفات - Array Manipulation Functions
// ========================================

// إزالة العناصر المكررة
function uniqueArray(arr) {
    return [...new Set(arr)];
}

// ترتيب المصفوفة حسب خاصية
function sortByProperty(arr, property, ascending = true) {
    return arr.sort((a, b) => {
        const aVal = a[property];
        const bVal = b[property];
        
        if (aVal < bVal) return ascending ? -1 : 1;
        if (aVal > bVal) return ascending ? 1 : -1;
        return 0;
    });
}

// تجميع المصفوفة حسب خاصية
function groupBy(arr, property) {
    return arr.reduce((groups, item) => {
        const key = item[property];
        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(item);
        return groups;
    }, {});
}

// تصفية المصفوفة
function filterArray(arr, searchTerm, properties = []) {
    if (!searchTerm) return arr;
    
    const term = searchTerm.toLowerCase();
    
    return arr.filter(item => {
        if (properties.length === 0) {
            // البحث في جميع الخصائص
            return Object.values(item).some(value => 
                String(value).toLowerCase().includes(term)
            );
        } else {
            // البحث في خصائص محددة
            return properties.some(prop => 
                String(item[prop] || '').toLowerCase().includes(term)
            );
        }
    });
}

// ========================================
// دوال التلاعب بالكائنات - Object Manipulation Functions
// ========================================

// نسخ عميق للكائن
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    
    const cloned = {};
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}

// دمج الكائنات
function mergeObjects(...objects) {
    return Object.assign({}, ...objects);
}

// الحصول على قيمة متداخلة
function getNestedValue(obj, path, defaultValue = null) {
    const keys = path.split('.');
    let current = obj;
    
    for (let key of keys) {
        if (current === null || current === undefined || !(key in current)) {
            return defaultValue;
        }
        current = current[key];
    }
    
    return current;
}

// تعيين قيمة متداخلة
function setNestedValue(obj, path, value) {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in current) || typeof current[key] !== 'object') {
            current[key] = {};
        }
        current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
}

// ========================================
// دوال التلاعب بالتواريخ - Date Manipulation Functions
// ========================================

// إضافة أيام للتاريخ
function addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
}

// إضافة شهور للتاريخ
function addMonths(date, months) {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
}

// إضافة سنوات للتاريخ
function addYears(date, years) {
    const result = new Date(date);
    result.setFullYear(result.getFullYear() + years);
    return result;
}

// الحصول على بداية اليوم
function startOfDay(date) {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
}

// الحصول على نهاية اليوم
function endOfDay(date) {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
}

// الحصول على بداية الشهر
function startOfMonth(date) {
    const result = new Date(date);
    result.setDate(1);
    result.setHours(0, 0, 0, 0);
    return result;
}

// الحصول على نهاية الشهر
function endOfMonth(date) {
    const result = new Date(date);
    result.setMonth(result.getMonth() + 1, 0);
    result.setHours(23, 59, 59, 999);
    return result;
}

// حساب الفرق بين تاريخين بالأيام
function daysDifference(date1, date2) {
    const oneDay = 24 * 60 * 60 * 1000;
    return Math.round(Math.abs((date1 - date2) / oneDay));
}

// التحقق من أن التاريخ اليوم
function isToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}

// ========================================
// دوال التلاعب بـ DOM - DOM Manipulation Functions
// ========================================

// إنشاء عنصر HTML
function createElement(tag, attributes = {}, content = '') {
    const element = document.createElement(tag);
    
    Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'className') {
            element.className = value;
        } else if (key === 'innerHTML') {
            element.innerHTML = value;
        } else {
            element.setAttribute(key, value);
        }
    });
    
    if (content) {
        element.textContent = content;
    }
    
    return element;
}

// إضافة فئة CSS
function addClass(element, className) {
    if (element && className) {
        element.classList.add(className);
    }
}

// إزالة فئة CSS
function removeClass(element, className) {
    if (element && className) {
        element.classList.remove(className);
    }
}

// تبديل فئة CSS
function toggleClass(element, className) {
    if (element && className) {
        element.classList.toggle(className);
    }
}

// التحقق من وجود فئة CSS
function hasClass(element, className) {
    return element && className && element.classList.contains(className);
}

// ========================================
// دوال التخزين المحلي - Local Storage Functions
// ========================================

// حفظ البيانات
function saveToStorage(key, data, encrypt = false) {
    try {
        let value = JSON.stringify(data);
        
        if (encrypt && typeof CryptoJS !== 'undefined') {
            value = CryptoJS.AES.encrypt(value, 'dental-lab-key').toString();
        }
        
        localStorage.setItem(key, value);
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        return false;
    }
}

// استرجاع البيانات
function loadFromStorage(key, defaultValue = null, encrypted = false) {
    try {
        let value = localStorage.getItem(key);
        
        if (!value) return defaultValue;
        
        if (encrypted && typeof CryptoJS !== 'undefined') {
            const bytes = CryptoJS.AES.decrypt(value, 'dental-lab-key');
            value = bytes.toString(CryptoJS.enc.Utf8);
        }
        
        return JSON.parse(value);
    } catch (error) {
        console.error('خطأ في استرجاع البيانات:', error);
        return defaultValue;
    }
}

// حذف البيانات
function removeFromStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('خطأ في حذف البيانات:', error);
        return false;
    }
}

// ========================================
// دوال التصدير والاستيراد - Export/Import Functions
// ========================================

// تصدير البيانات إلى CSV
function exportToCSV(data, filename = 'data.csv') {
    if (!data || data.length === 0) {
        showError('لا توجد بيانات للتصدير');
        return;
    }
    
    try {
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');
        
        downloadFile(csvContent, filename, 'text/csv');
        showSuccess('تم تصدير البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في تصدير CSV:', error);
        showError('فشل في تصدير البيانات');
    }
}

// تصدير البيانات إلى JSON
function exportToJSON(data, filename = 'data.json') {
    try {
        const jsonContent = JSON.stringify(data, null, 2);
        downloadFile(jsonContent, filename, 'application/json');
        showSuccess('تم تصدير البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في تصدير JSON:', error);
        showError('فشل في تصدير البيانات');
    }
}

// تحميل ملف
function downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
}

// ========================================
// دوال متنوعة - Miscellaneous Functions
// ========================================

// إنشاء معرف فريد
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// تأخير التنفيذ
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// تنفيذ دالة مرة واحدة فقط
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تحديد معدل تنفيذ الدالة
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// نسخ النص للحافظة
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showSuccess('تم نسخ النص');
        return true;
    } catch (error) {
        console.error('خطأ في النسخ:', error);
        showError('فشل في نسخ النص');
        return false;
    }
}

// فتح رابط في نافذة جديدة
function openInNewTab(url) {
    window.open(url, '_blank', 'noopener,noreferrer');
}

// طباعة الصفحة
function printPage() {
    window.print();
}

// إعادة تحميل الصفحة
function reloadPage() {
    window.location.reload();
}

console.log('✅ تم تحميل الأدوات المساعدة بنجاح');
