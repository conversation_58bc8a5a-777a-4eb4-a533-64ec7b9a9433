// ========================================
// واجهة تسجيل الدخول العصرية - Modern Login Interface
// ========================================

console.log('🎨 تحميل واجهة تسجيل الدخول العصرية...');

// ========================================
// فئة إدارة واجهة الدخول العصرية
// ========================================

class ModernLoginInterface {
    constructor() {
        this.isInitialized = false;
        this.particles = [];
        this.animationFrame = null;
        
        this.init();
    }

    // ========================================
    // تهيئة الواجهة
    // ========================================

    init() {
        if (this.isInitialized) return;
        
        console.log('🚀 تهيئة واجهة الدخول العصرية...');
        
        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupInterface());
        } else {
            this.setupInterface();
        }
        
        this.isInitialized = true;
    }

    // ========================================
    // إعداد الواجهة
    // ========================================

    setupInterface() {
        this.setupInputAnimations();
        this.setupPasswordToggle();
        this.setupFormValidation();
        this.setupKeyboardShortcuts();
        this.createParticleEffect();
        
        console.log('✅ تم إعداد واجهة الدخول العصرية');
    }

    // ========================================
    // تحريك حقول الإدخال
    // ========================================

    setupInputAnimations() {
        const inputs = document.querySelectorAll('.modern-input');
        
        inputs.forEach(input => {
            // تأثير التركيز
            input.addEventListener('focus', (e) => {
                this.animateInputFocus(e.target, true);
            });
            
            // تأثير فقدان التركيز
            input.addEventListener('blur', (e) => {
                this.animateInputFocus(e.target, false);
            });
            
            // تحريك التسمية عند الكتابة
            input.addEventListener('input', (e) => {
                this.updateInputLabel(e.target);
            });
        });
    }

    animateInputFocus(input, isFocused) {
        const container = input.closest('.input-container');
        const icon = container.querySelector('.input-icon');
        const underline = container.querySelector('.input-underline');
        
        if (isFocused) {
            container.classList.add('focused');
            if (icon) icon.style.transform = 'translateY(-50%) scale(1.1)';
            if (underline) underline.style.transform = 'scaleX(1)';
        } else {
            container.classList.remove('focused');
            if (icon) icon.style.transform = 'translateY(-50%) scale(1)';
            if (underline && !input.value) underline.style.transform = 'scaleX(0)';
        }
    }

    updateInputLabel(input) {
        const label = input.nextElementSibling;
        if (label && label.classList.contains('modern-label')) {
            if (input.value) {
                label.classList.add('active');
            } else {
                label.classList.remove('active');
            }
        }
    }

    // ========================================
    // تبديل إظهار كلمة المرور
    // ========================================

    setupPasswordToggle() {
        const passwordToggle = document.getElementById('password-toggle');
        const passwordInput = document.getElementById('password');
        
        if (passwordToggle && passwordInput) {
            passwordToggle.addEventListener('click', () => {
                const isPassword = passwordInput.type === 'password';
                passwordInput.type = isPassword ? 'text' : 'password';
                
                const icon = passwordToggle.querySelector('i');
                if (icon) {
                    icon.className = isPassword ? 'fas fa-eye-slash' : 'fas fa-eye';
                }
                
                // تأثير بصري
                passwordToggle.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    passwordToggle.style.transform = 'scale(1)';
                }, 150);
            });
        }
    }

    // ========================================
    // التحقق من صحة النموذج
    // ========================================

    setupFormValidation() {
        const form = document.getElementById('login-form');
        const inputs = form.querySelectorAll('.modern-input');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateInput(input);
            });
            
            input.addEventListener('input', () => {
                this.clearInputError(input);
            });
        });
    }

    validateInput(input) {
        const container = input.closest('.input-container');
        let isValid = true;
        let errorMessage = '';
        
        // التحقق من الحقول المطلوبة
        if (input.required && !input.value.trim()) {
            isValid = false;
            errorMessage = 'هذا الحقل مطلوب';
        }
        
        // التحقق من اسم المستخدم
        if (input.id === 'username' && input.value.trim()) {
            if (input.value.length < 3) {
                isValid = false;
                errorMessage = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
            }
        }
        
        // التحقق من كلمة المرور
        if (input.id === 'password' && input.value) {
            if (input.value.length < 6) {
                isValid = false;
                errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            }
        }
        
        this.showInputValidation(container, isValid, errorMessage);
        return isValid;
    }

    showInputValidation(container, isValid, errorMessage) {
        // إزالة رسائل الخطأ السابقة
        const existingError = container.querySelector('.input-error');
        if (existingError) {
            existingError.remove();
        }
        
        if (!isValid && errorMessage) {
            const errorElement = document.createElement('div');
            errorElement.className = 'input-error';
            errorElement.textContent = errorMessage;
            container.appendChild(errorElement);
            
            container.classList.add('error');
        } else {
            container.classList.remove('error');
        }
    }

    clearInputError(input) {
        const container = input.closest('.input-container');
        const errorElement = container.querySelector('.input-error');
        
        if (errorElement) {
            errorElement.remove();
        }
        
        container.classList.remove('error');
    }

    // ========================================
    // اختصارات لوحة المفاتيح
    // ========================================

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Enter للتسجيل
            if (e.key === 'Enter' && document.querySelector('.modern-login-modal.show')) {
                const loginBtn = document.getElementById('login-btn');
                if (loginBtn && !loginBtn.disabled) {
                    e.preventDefault();
                    loginBtn.click();
                }
            }
            
            // Escape لإغلاق النافذة (إذا كان مسموحاً)
            if (e.key === 'Escape' && document.querySelector('.modern-login-modal.show')) {
                // يمكن إضافة منطق إغلاق النافذة هنا إذا لزم الأمر
            }
        });
    }

    // ========================================
    // تأثير الجسيمات
    // ========================================

    createParticleEffect() {
        const particlesContainer = document.querySelector('.login-particles');
        if (!particlesContainer) return;
        
        // إنشاء جسيمات متحركة
        for (let i = 0; i < 50; i++) {
            this.createParticle(particlesContainer);
        }
        
        // بدء الحركة
        this.animateParticles();
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: rgba(255, 255, 255, ${Math.random() * 0.5 + 0.1});
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            pointer-events: none;
        `;
        
        container.appendChild(particle);
        this.particles.push({
            element: particle,
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            life: Math.random() * 100
        });
    }

    animateParticles() {
        this.particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life += 1;
            
            // إعادة تدوير الجسيمات
            if (particle.life > 200) {
                particle.x = Math.random() * window.innerWidth;
                particle.y = Math.random() * window.innerHeight;
                particle.life = 0;
            }
            
            particle.element.style.transform = `translate(${particle.x}px, ${particle.y}px)`;
        });
        
        this.animationFrame = requestAnimationFrame(() => this.animateParticles());
    }

    // ========================================
    // تنظيف الموارد
    // ========================================

    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        
        this.particles.forEach(particle => {
            if (particle.element.parentNode) {
                particle.element.parentNode.removeChild(particle.element);
            }
        });
        
        this.particles = [];
        this.isInitialized = false;
    }
}

// ========================================
// إنشاء مثيل من واجهة الدخول العصرية
// ========================================

const modernLoginInterface = new ModernLoginInterface();

// تصدير للاستخدام العام
window.modernLoginInterface = modernLoginInterface;
