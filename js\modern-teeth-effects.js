// ========================================
// تأثيرات الأسنان العصرية - Modern Teeth Effects
// نظام إدارة معمل الأسنان المتقدم v2.0
// ========================================

console.log('🦷 تحميل تأثيرات الأسنان العصرية...');

// ========================================
// فئة تأثيرات الأسنان العصرية
// ========================================

class ModernTeethEffects {
    constructor() {
        this.isInitialized = false;
        this.soundEnabled = false;
        this.animationQueue = [];
        this.particleSystem = null;
        
        this.init();
    }

    // ========================================
    // تهيئة التأثيرات
    // ========================================

    init() {
        if (this.isInitialized) return;
        
        console.log('🚀 تهيئة تأثيرات الأسنان العصرية...');
        
        // انتظار تحميل DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEffects());
        } else {
            this.setupEffects();
        }
        
        this.isInitialized = true;
    }

    // ========================================
    // إعداد التأثيرات
    // ========================================

    setupEffects() {
        this.setupToothHoverEffects();
        this.setupToothClickEffects();
        this.setupToothSelectionEffects();
        this.setupKeyboardShortcuts();
        this.createParticleSystem();
        
        console.log('✅ تم إعداد تأثيرات الأسنان العصرية');
    }

    // ========================================
    // تأثيرات التمرير فوق الأسنان
    // ========================================

    setupToothHoverEffects() {
        // استخدام event delegation للأداء الأفضل
        document.addEventListener('mouseover', (e) => {
            const tooth = e.target.closest('.tooth');
            if (tooth) {
                this.onToothHover(tooth, true);
            }
        });

        document.addEventListener('mouseout', (e) => {
            const tooth = e.target.closest('.tooth');
            if (tooth) {
                this.onToothHover(tooth, false);
            }
        });
    }

    onToothHover(tooth, isHovering) {
        const toothVisual = tooth.querySelector('.tooth-visual');
        const toothNumber = tooth.querySelector('.tooth-number');
        const toothLabel = tooth.querySelector('.tooth-label');

        if (isHovering) {
            // تأثير التمرير
            this.createHoverRipple(toothVisual);
            this.showToothInfo(tooth);
            
            // تأثير الاهتزاز الخفيف
            tooth.style.animation = 'gentleVibration 0.3s ease-in-out';
            
            // تأثير الضوء
            this.createLightEffect(toothVisual);
            
        } else {
            // إزالة التأثيرات
            tooth.style.animation = '';
            this.hideToothInfo();
        }
    }

    // ========================================
    // تأثيرات النقر على الأسنان
    // ========================================

    setupToothClickEffects() {
        document.addEventListener('click', (e) => {
            const tooth = e.target.closest('.tooth');
            if (tooth) {
                this.onToothClick(tooth);
            }
        });
    }

    onToothClick(tooth) {
        const toothVisual = tooth.querySelector('.tooth-visual');
        const isSelected = tooth.classList.contains('selected');
        
        // تأثير النقر
        this.createClickRipple(toothVisual);
        
        // تأثير الاختيار/إلغاء الاختيار
        if (isSelected) {
            this.createDeselectionEffect(tooth);
        } else {
            this.createSelectionEffect(tooth);
        }
        
        // تأثير الاهتزاز
        tooth.style.animation = 'clickBounce 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
        
        // إنشاء جسيمات
        this.createParticles(toothVisual, isSelected ? 'deselect' : 'select');
    }

    // ========================================
    // تأثيرات الاختيار
    // ========================================

    setupToothSelectionEffects() {
        // مراقبة تغييرات الاختيار
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const tooth = mutation.target;
                    if (tooth.classList.contains('tooth')) {
                        this.onSelectionChange(tooth);
                    }
                }
            });
        });

        // بدء المراقبة
        document.querySelectorAll('.tooth').forEach(tooth => {
            observer.observe(tooth, { attributes: true });
        });
    }

    onSelectionChange(tooth) {
        const isSelected = tooth.classList.contains('selected');
        
        if (isSelected) {
            this.animateSelection(tooth);
        } else {
            this.animateDeselection(tooth);
        }
    }

    // ========================================
    // إنشاء تأثير الموجة
    // ========================================

    createHoverRipple(element) {
        const ripple = document.createElement('div');
        ripple.className = 'tooth-hover-ripple';
        ripple.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            z-index: 1;
        `;
        
        element.appendChild(ripple);
        
        // تحريك الموجة
        ripple.animate([
            { width: '0px', height: '0px', opacity: 0.8 },
            { width: '80px', height: '80px', opacity: 0 }
        ], {
            duration: 600,
            easing: 'ease-out'
        }).onfinish = () => {
            ripple.remove();
        };
    }

    createClickRipple(element) {
        const ripple = document.createElement('div');
        ripple.className = 'tooth-click-ripple';
        ripple.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.5) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            z-index: 2;
        `;
        
        element.appendChild(ripple);
        
        // تحريك الموجة
        ripple.animate([
            { width: '0px', height: '0px', opacity: 1 },
            { width: '100px', height: '100px', opacity: 0 }
        ], {
            duration: 500,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }).onfinish = () => {
            ripple.remove();
        };
    }

    // ========================================
    // تأثيرات الضوء
    // ========================================

    createLightEffect(element) {
        const light = document.createElement('div');
        light.className = 'tooth-light-effect';
        light.style.cssText = `
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            pointer-events: none;
            z-index: 1;
        `;
        
        element.appendChild(light);
        
        // تحريك الضوء
        light.animate([
            { left: '-100%' },
            { left: '100%' }
        ], {
            duration: 800,
            easing: 'ease-out'
        }).onfinish = () => {
            light.remove();
        };
    }

    // ========================================
    // نظام الجسيمات
    // ========================================

    createParticleSystem() {
        this.particleSystem = {
            particles: [],
            canvas: null,
            ctx: null
        };
    }

    createParticles(element, type) {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const colors = type === 'select' 
            ? ['#10b981', '#059669', '#047857'] 
            : ['#ef4444', '#dc2626', '#b91c1c'];
        
        for (let i = 0; i < 8; i++) {
            this.createParticle(centerX, centerY, colors[Math.floor(Math.random() * colors.length)]);
        }
    }

    createParticle(x, y, color) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: fixed;
            top: ${y}px;
            left: ${x}px;
            width: 4px;
            height: 4px;
            background: ${color};
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
        `;
        
        document.body.appendChild(particle);
        
        // تحريك الجسيم
        const angle = Math.random() * Math.PI * 2;
        const velocity = 50 + Math.random() * 50;
        const endX = x + Math.cos(angle) * velocity;
        const endY = y + Math.sin(angle) * velocity;
        
        particle.animate([
            { 
                transform: 'translate(0, 0) scale(1)',
                opacity: 1
            },
            { 
                transform: `translate(${endX - x}px, ${endY - y}px) scale(0)`,
                opacity: 0
            }
        ], {
            duration: 800,
            easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        }).onfinish = () => {
            particle.remove();
        };
    }

    // ========================================
    // عرض معلومات السن
    // ========================================

    showToothInfo(tooth) {
        // تم تعطيل tooltip لتجنب مشاكل العرض
        return;
    }

    hideToothInfo() {
        // تم تعطيل tooltip لتجنب مشاكل العرض
        return;
    }

    // ========================================
    // اختصارات لوحة المفاتيح
    // ========================================

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // التحقق من وجود نافذة التركيبات مفتوحة
            if (!document.querySelector('.modal:not(.hidden)')) return;
            
            switch(e.key) {
                case 'a':
                case 'A':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.selectAllTeethWithEffect();
                    }
                    break;
                case 'Escape':
                    this.clearAllTeethWithEffect();
                    break;
                case 'u':
                case 'U':
                    this.selectUpperJawWithEffect();
                    break;
                case 'l':
                case 'L':
                    this.selectLowerJawWithEffect();
                    break;
            }
        });
    }

    // ========================================
    // تأثيرات الاختيار المتقدمة
    // ========================================

    selectAllTeethWithEffect() {
        const teeth = document.querySelectorAll('.tooth');
        teeth.forEach((tooth, index) => {
            setTimeout(() => {
                tooth.click();
                this.createSelectionWave(tooth);
            }, index * 50);
        });
    }

    clearAllTeethWithEffect() {
        const selectedTeeth = document.querySelectorAll('.tooth.selected');
        selectedTeeth.forEach((tooth, index) => {
            setTimeout(() => {
                tooth.click();
                this.createDeselectionWave(tooth);
            }, index * 30);
        });
    }

    createSelectionWave(tooth) {
        const wave = document.createElement('div');
        wave.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border: 2px solid #10b981;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            z-index: 3;
        `;
        
        tooth.appendChild(wave);
        
        wave.animate([
            { width: '0px', height: '0px', opacity: 1 },
            { width: '120px', height: '120px', opacity: 0 }
        ], {
            duration: 1000,
            easing: 'ease-out'
        }).onfinish = () => {
            wave.remove();
        };
    }

    // ========================================
    // الحصول على معلومات السن
    // ========================================

    getToothInfo(toothNumber) {
        // تحديد الفك والجهة
        const jaw = toothNumber <= 16 ? 'العلوي' : 'السفلي';
        let side;

        if (toothNumber <= 16) {
            // الفك العلوي: 1-8 يمين، 9-16 يسار
            side = toothNumber <= 8 ? 'الأيمن' : 'الأيسر';
        } else {
            // الفك السفلي: 17-24 يمين، 25-32 يسار
            side = toothNumber <= 24 ? 'الأيمن' : 'الأيسر';
        }

        return {
            name: `السن رقم ${toothNumber} - ${side} ${jaw}`,
            type: 'سن'
        };
    }

    // ========================================
    // تنظيف الموارد
    // ========================================

    destroy() {
        // إزالة جميع التأثيرات والمستمعين
        document.querySelectorAll('.tooth-tooltip, .tooth-hover-ripple, .tooth-click-ripple').forEach(el => {
            el.remove();
        });
        
        this.isInitialized = false;
    }
}

// ========================================
// إنشاء مثيل من تأثيرات الأسنان العصرية
// ========================================

const modernTeethEffects = new ModernTeethEffects();

// تصدير للاستخدام العام
window.modernTeethEffects = modernTeethEffects;
