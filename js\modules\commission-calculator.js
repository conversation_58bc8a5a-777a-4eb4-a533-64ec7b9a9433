// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// حاسبة العمولات المتقدمة - Advanced Commission Calculator
// ========================================

console.log('💰 تحميل حاسبة العمولات المتقدمة...');

// ========================================
// فئة حاسبة العمولات المتقدمة - Advanced Commission Calculator
// ========================================

class CommissionCalculator {
    constructor() {
        this.commissionTypes = [];
        this.employeeCommissions = [];
        this.prosthetics = [];
        this.commissionRates = {
            // عمولات البورسلين
            'PORCELAIN': {
                'فيتا': 15,      // 15 ريال لكل تاج فيتا
                'جى سرام': 18,   // 18 ريال لكل تاج جى سرام
                'فيس': 25       // 25 ريال لكل قشرة فيس
            },
            // عمولات الزيركون
            'ZIRCONIA': {
                'Full Anatomy': 30,        // 30 ريال لكل تاج زيركون كامل
                'Copy + Porcelain': 35,    // 35 ريال لكل تاج زيركون مع بورسلين
                'Onlay': 20               // 20 ريال لكل حشوة زيركون خارجية
            },
            // عمولات التقويم
            'ORTHODONTICS': {
                'جهاز تقويم متحرك': 50,    // 50 ريال لكل جهاز تقويم
                'حافظ مكان': 30,          // 30 ريال لكل حافظ مكان
                'واقي أسنان': 25         // 25 ريال لكل واقي أسنان
            },
            // عمولات الأطقم المتحركة
            'DENTURES': {
                'طقم كامل': 80,          // 80 ريال لكل طقم كامل
                'طقم جزئي': 60,          // 60 ريال لكل طقم جزئي
                'برشل جزئي': 8          // 8 ريال لكل سن في البرشل الجزئي
            },
            // عمولات المعدن
            'METAL': {
                'معدن عادي': 10,         // 10 ريال لكل تاج معدني
                'Vitallium': 12          // 12 ريال لكل تاج فيتاليوم
            },
            // عمولات الأعمال الإضافية
            'ADDITIONAL': {
                'إصلاح كسر': 15,         // 15 ريال لكل إصلاح
                'تعديل وضبط': 8,        // 8 ريال لكل تعديل
                'إعادة تبطين': 25,      // 25 ريال لكل إعادة تبطين
                'تلميع وتنظيف': 5       // 5 ريال لكل تنظيف
            }
        };
    }

    // ========================================
    // تهيئة الحاسبة - Initialize Calculator
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة حاسبة العمولات...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            console.log('✅ تم تهيئة حاسبة العمولات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة حاسبة العمولات:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل أنواع العمولات
            this.commissionTypes = await db.findAll('commission_types');
            
            // تحميل عمولات الموظفين
            this.employeeCommissions = await db.findAll('employee_commissions');
            
            // تحميل التركيبات
            this.prosthetics = await db.findAll('prosthetics');
            
            console.log(`📊 تم تحميل ${this.commissionTypes.length} نوع عمولة`);
            console.log(`👥 تم تحميل ${this.employeeCommissions.length} عمولة موظف`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // حساب عمولة موظف لفترة محددة - Calculate Employee Commission for Period
    // ========================================

    async calculateEmployeeCommission(employeeId, startDate, endDate) {
        try {
            // البحث عن التركيبات المكتملة في الفترة المحددة
            const periodProsthetics = this.prosthetics.filter(prosthetic => {
                const prostheticDate = new Date(prosthetic.createdAt);
                const fromDate = new Date(startDate);
                const toDate = new Date(endDate);
                
                return prosthetic.technician_id === employeeId &&
                       prostheticDate >= fromDate &&
                       prostheticDate <= toDate &&
                       (prosthetic.status === 'completed' || prosthetic.status === 'delivered');
            });

            if (periodProsthetics.length === 0) {
                return {
                    totalCommission: 0,
                    commissionDetails: [],
                    summary: {
                        totalCases: 0,
                        porcelainCommission: 0,
                        zirconiaCommission: 0,
                        orthodonticsCommission: 0,
                        denturesCommission: 0,
                        metalCommission: 0,
                        additionalCommission: 0
                    }
                };
            }

            let totalCommission = 0;
            const commissionDetails = [];
            const summary = {
                totalCases: periodProsthetics.length,
                porcelainCommission: 0,
                zirconiaCommission: 0,
                orthodonticsCommission: 0,
                denturesCommission: 0,
                metalCommission: 0,
                additionalCommission: 0
            };

            // حساب العمولة لكل تركيبة
            for (const prosthetic of periodProsthetics) {
                const commissionData = this.calculateProstheticCommission(prosthetic);
                
                if (commissionData.commission > 0) {
                    totalCommission += commissionData.commission;
                    commissionDetails.push({
                        prosthetic_id: prosthetic.id,
                        patient_name: prosthetic.patient_name,
                        prosthetic_type: prosthetic.prosthetic_type,
                        material: prosthetic.material,
                        quantity: prosthetic.quantity || 1,
                        commission_type: commissionData.type,
                        unit_commission: commissionData.unitCommission,
                        total_commission: commissionData.commission,
                        calculation_date: prosthetic.createdAt.split('T')[0]
                    });

                    // إضافة للملخص حسب النوع
                    switch (commissionData.type) {
                        case 'PORCELAIN':
                            summary.porcelainCommission += commissionData.commission;
                            break;
                        case 'ZIRCONIA':
                            summary.zirconiaCommission += commissionData.commission;
                            break;
                        case 'ORTHODONTICS':
                            summary.orthodonticsCommission += commissionData.commission;
                            break;
                        case 'DENTURES':
                            summary.denturesCommission += commissionData.commission;
                            break;
                        case 'METAL':
                            summary.metalCommission += commissionData.commission;
                            break;
                        case 'ADDITIONAL':
                            summary.additionalCommission += commissionData.commission;
                            break;
                    }
                }
            }

            return {
                totalCommission: totalCommission,
                commissionDetails: commissionDetails,
                summary: summary
            };

        } catch (error) {
            console.error('خطأ في حساب عمولة الموظف:', error);
            throw error;
        }
    }

    // ========================================
    // حساب عمولة تركيبة واحدة - Calculate Single Prosthetic Commission
    // ========================================

    calculateProstheticCommission(prosthetic) {
        try {
            const category = prosthetic.category;
            const material = prosthetic.material;
            const quantity = prosthetic.quantity || 1;

            // تحديد نوع العمولة بناءً على الفئة
            let commissionType = '';
            switch (category) {
                case 'porcelain':
                    commissionType = 'PORCELAIN';
                    break;
                case 'zirconia':
                    commissionType = 'ZIRCONIA';
                    break;
                case 'orthodontics':
                    commissionType = 'ORTHODONTICS';
                    break;
                case 'dentures':
                    commissionType = 'DENTURES';
                    break;
                case 'metal':
                    commissionType = 'METAL';
                    break;
                case 'additional':
                    commissionType = 'ADDITIONAL';
                    break;
                default:
                    return { commission: 0, type: '', unitCommission: 0 };
            }

            // البحث عن معدل العمولة للمادة
            const commissionRates = this.commissionRates[commissionType];
            if (!commissionRates || !commissionRates[material]) {
                return { commission: 0, type: commissionType, unitCommission: 0 };
            }

            const unitCommission = commissionRates[material];
            let totalCommission = 0;

            // حساب العمولة حسب النوع
            if (commissionType === 'DENTURES' && material === 'برشل جزئي') {
                // للبرشل الجزئي: العمولة حسب عدد الأسنان
                const selectedTeeth = prosthetic.selected_teeth ? JSON.parse(prosthetic.selected_teeth) : [];
                totalCommission = selectedTeeth.length * unitCommission;
            } else {
                // للأنواع الأخرى: العمولة حسب الكمية
                totalCommission = quantity * unitCommission;
            }

            return {
                commission: totalCommission,
                type: commissionType,
                unitCommission: unitCommission
            };

        } catch (error) {
            console.error('خطأ في حساب عمولة التركيبة:', error);
            return { commission: 0, type: '', unitCommission: 0 };
        }
    }

    // ========================================
    // حساب خصم أيام الغياب - Calculate Absence Deduction
    // ========================================

    async calculateAbsenceDeduction(employeeId, startDate, endDate, dailySalary) {
        try {
            // البحث عن أيام الغياب في الفترة المحددة
            const absenceRecords = await db.findAll('attendance_records', {
                where: {
                    employee_id: employeeId,
                    attendance_date: { between: [startDate, endDate] },
                    status: 'absent'
                }
            });

            const absenceDays = absenceRecords.length;
            const totalDeduction = absenceDays * dailySalary;

            return {
                absenceDays: absenceDays,
                dailySalary: dailySalary,
                totalDeduction: totalDeduction,
                absenceRecords: absenceRecords
            };

        } catch (error) {
            console.error('خطأ في حساب خصم الغياب:', error);
            return {
                absenceDays: 0,
                dailySalary: dailySalary,
                totalDeduction: 0,
                absenceRecords: []
            };
        }
    }

    // ========================================
    // إنشاء كشف راتب شامل - Generate Comprehensive Payroll
    // ========================================

    async generatePayroll(employeeId, startDate, endDate) {
        try {
            // تحميل بيانات الموظف
            const employee = await db.findById('employees', employeeId);
            if (!employee) {
                throw new Error('لم يتم العثور على الموظف');
            }

            // حساب العمولات
            const commissionData = await this.calculateEmployeeCommission(employeeId, startDate, endDate);

            // حساب الراتب اليومي
            const dailySalary = employee.basic_salary / 30; // افتراض 30 يوم في الشهر

            // حساب خصم الغياب
            const absenceData = await this.calculateAbsenceDeduction(employeeId, startDate, endDate, dailySalary);

            // حساب الإجماليات
            const basicSalary = employee.basic_salary;
            const allowances = (employee.housing_allowance || 0) + 
                             (employee.transport_allowance || 0) + 
                             (employee.other_allowances || 0);
            const commissions = commissionData.totalCommission;
            const overtimePay = 0; // سيتم حسابه لاحقاً من سجلات الحضور
            const grossSalary = basicSalary + allowances + commissions + overtimePay;
            const deductions = 0; // خصومات أخرى (تأمينات، ضرائب، إلخ)
            const absenceDeduction = absenceData.totalDeduction;
            const netSalary = grossSalary - deductions - absenceDeduction;

            // إنشاء رقم كشف الراتب
            const payrollNumber = await this.generatePayrollNumber();

            const payrollData = {
                employee_id: employeeId,
                payroll_number: payrollNumber,
                pay_period_start: startDate,
                pay_period_end: endDate,
                basic_salary: basicSalary,
                allowances: allowances,
                commissions: commissions,
                overtime_pay: overtimePay,
                gross_salary: grossSalary,
                deductions: deductions,
                absence_deduction: absenceDeduction,
                net_salary: netSalary,
                status: 'draft',
                generated_by: getCurrentUser()?.name || 'النظام'
            };

            return {
                payrollData: payrollData,
                commissionDetails: commissionData.commissionDetails,
                commissionSummary: commissionData.summary,
                absenceData: absenceData,
                employee: employee
            };

        } catch (error) {
            console.error('خطأ في إنشاء كشف الراتب:', error);
            throw error;
        }
    }

    // ========================================
    // إنشاء رقم كشف راتب - Generate Payroll Number
    // ========================================

    async generatePayrollNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        
        // البحث عن آخر رقم في الشهر الحالي
        const existingPayrolls = await db.findAll('payroll_records', {
            where: {
                payroll_number: { like: `PAY-${year}${month}%` }
            }
        });
        
        const nextNumber = existingPayrolls.length + 1;
        return `PAY-${year}${month}-${String(nextNumber).padStart(4, '0')}`;
    }

    // ========================================
    // حفظ كشف الراتب - Save Payroll Record
    // ========================================

    async savePayrollRecord(payrollData, commissionDetails) {
        try {
            // حفظ كشف الراتب الأساسي
            const payroll = await db.insert('payroll_records', payrollData);
            
            if (payroll && commissionDetails.length > 0) {
                // حفظ تفاصيل العمولات
                for (const detail of commissionDetails) {
                    await db.insert('commission_details', {
                        ...detail,
                        payroll_id: payroll.id
                    });
                }
            }

            return payroll;

        } catch (error) {
            console.error('خطأ في حفظ كشف الراتب:', error);
            throw error;
        }
    }

    // ========================================
    // تحديث معدلات العمولة - Update Commission Rates
    // ========================================

    updateCommissionRates(newRates) {
        this.commissionRates = { ...this.commissionRates, ...newRates };
    }

    // ========================================
    // الحصول على معدلات العمولة الحالية - Get Current Commission Rates
    // ========================================

    getCommissionRates() {
        return this.commissionRates;
    }

    // ========================================
    // حساب إجمالي العمولات لجميع الموظفين - Calculate Total Commissions for All Employees
    // ========================================

    async calculateTotalCommissions(startDate, endDate) {
        try {
            const employees = await db.findAll('employees', { where: { isActive: true } });
            const totalCommissions = [];

            for (const employee of employees) {
                const commissionData = await this.calculateEmployeeCommission(employee.id, startDate, endDate);
                
                if (commissionData.totalCommission > 0) {
                    totalCommissions.push({
                        employee: employee,
                        ...commissionData
                    });
                }
            }

            return totalCommissions;

        } catch (error) {
            console.error('خطأ في حساب إجمالي العمولات:', error);
            throw error;
        }
    }
}

// ========================================
// تهيئة حاسبة العمولات - Initialize Commission Calculator
// ========================================

// إنشاء مثيل من حاسبة العمولات
const commissionCalculator = new CommissionCalculator();

// دالة تهيئة الحاسبة
async function initializeCommissionCalculator() {
    try {
        const success = await commissionCalculator.init();
        if (success) {
            console.log('✅ تم تهيئة حاسبة العمولات بنجاح');
        } else {
            console.error('❌ فشل في تهيئة حاسبة العمولات');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة حاسبة العمولات:', error);
        return false;
    }
}

// تصدير الحاسبة للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { commissionCalculator, CommissionCalculator };
}

console.log('✅ تم تحميل حاسبة العمولات المتقدمة بنجاح');
