// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// وحدة لوحة التحكم - Dashboard Module
// ========================================

console.log('📊 تحميل وحدة لوحة التحكم...');

// ========================================
// فئة إدارة لوحة التحكم - Dashboard Manager
// ========================================

class DashboardManager {
    constructor() {
        this.statistics = {
            newProsthetics: 0,
            pendingProsthetics: 0,
            totalDoctors: 0,
            totalEmployees: 0,
            nextCaseNumber: 1001
        };
        this.recentActivities = [];
        this.charts = {};
        this.refreshInterval = null;
        this.autoRefresh = true;
        this.refreshRate = 30000; // 30 ثانية
        this.isAutoRefreshEnabled = true;
    }

    // ========================================
    // تهيئة الوحدة - Initialize Module
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة وحدة لوحة التحكم...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            // إعداد التحديث التلقائي
            this.setupAutoRefresh();
            
            console.log('✅ تم تهيئة وحدة لوحة التحكم بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة وحدة لوحة التحكم:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل الإحصائيات
            await this.loadStatistics();

            // تحميل النشاطات الأخيرة
            await this.loadRecentActivities();

            // تحميل البيانات للرسوم البيانية
            await this.loadChartsData();

        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // تحميل الإحصائيات - Load Statistics
    // ========================================

    async loadStatistics() {
        try {
            // التركيبات الجديدة (في الانتظار)
            const newProsthetics = await db.findAll('prosthetics', {
                where: { status: 'pending' }
            });
            this.statistics.newProsthetics = newProsthetics.length;

            // التركيبات المعلقة (قيد التنفيذ)
            const pendingProsthetics = await db.findAll('prosthetics', {
                where: { status: 'in_progress' }
            });
            this.statistics.pendingProsthetics = pendingProsthetics.length;

            // إجمالي الأطباء
            const doctors = await db.findAll('doctors');
            this.statistics.totalDoctors = doctors.length;

            // إجمالي الموظفين
            const employees = await db.findAll('employees');
            this.statistics.totalEmployees = employees.length;

            // رقم الحالة التالي
            const nextCaseSetting = await db.findAll('system_settings', {
                where: { setting_key: 'next_case_number' }
            });

            if (nextCaseSetting.length > 0) {
                this.statistics.nextCaseNumber = parseInt(nextCaseSetting[0].setting_value);
            }

            // إحصائيات إضافية للرسوم البيانية
            const allProsthetics = await db.findAll('prosthetics');
            const completedProsthetics = allProsthetics.filter(p => p.status === 'completed');
            const activeDoctors = doctors.filter(d => d.isActive);
            const activeEmployees = employees.filter(e => e.isActive);

            // إحصائيات مالية
            const totalRevenue = allProsthetics.reduce((sum, p) => sum + (p.price || 0), 0);
            const monthlyRevenue = this.calculateMonthlyRevenue(allProsthetics);

            // حفظ الإحصائيات الإضافية للرسوم البيانية
            this.statistics.prosthetics = {
                total: allProsthetics.length,
                completed: completedProsthetics.length,
                pending: pendingProsthetics.length,
                completionRate: allProsthetics.length > 0 ? (completedProsthetics.length / allProsthetics.length * 100) : 0
            };

            this.statistics.doctors = {
                total: doctors.length,
                active: activeDoctors.length
            };

            this.statistics.employees = {
                total: employees.length,
                active: activeEmployees.length
            };

            this.statistics.financial = {
                totalRevenue,
                monthlyRevenue,
                averageOrderValue: allProsthetics.length > 0 ? totalRevenue / allProsthetics.length : 0
            };

            console.log('📈 تم تحميل الإحصائيات:', this.statistics);

        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
            throw error;
        }
    }

    // ========================================
    // تحميل النشاطات الأخيرة - Load Recent Activities
    // ========================================

    async loadRecentActivities() {
        try {
            // تحميل آخر 10 نشاطات
            const activities = await db.findAll('activity_logs', {
                orderBy: 'createdAt DESC',
                limit: 10
            });

            this.recentActivities = activities.map(activity => ({
                id: activity.id,
                type: activity.activity_type,
                description: activity.description,
                user: activity.user_name,
                role: activity.user_role,
                time: activity.createdAt,
                status: activity.status,
                priority: activity.priority,
                details: activity.details ? JSON.parse(activity.details) : null
            }));

            console.log(`📋 تم تحميل ${this.recentActivities.length} نشاط حديث`);

        } catch (error) {
            console.error('خطأ في تحميل النشاطات الأخيرة:', error);
            this.recentActivities = [];
        }
    }

    // ========================================
    // حساب الإيرادات الشهرية - Calculate Monthly Revenue
    // ========================================

    calculateMonthlyRevenue(prosthetics) {
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        return prosthetics
            .filter(p => {
                const date = new Date(p.createdAt);
                return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
            })
            .reduce((sum, p) => sum + (p.price || 0), 0);
    }

    // ========================================
    // تحميل بيانات الرسوم البيانية - Load Charts Data
    // ========================================

    async loadChartsData() {
        try {
            // بيانات الرسم البياني للتركيبات حسب النوع
            const prosthetics = await db.findAll('prosthetics');
            this.charts.prostheticsByType = this.groupProstheticsByType(prosthetics);
            
            // بيانات الرسم البياني للإيرادات الشهرية
            this.charts.monthlyRevenue = this.calculateMonthlyRevenueChart(prosthetics);
            
            // بيانات الرسم البياني لحالات التركيبات
            this.charts.prostheticsByStatus = this.groupProstheticsByStatus(prosthetics);
            
        } catch (error) {
            console.error('خطأ في تحميل بيانات الرسوم البيانية:', error);
            throw error;
        }
    }

    // ========================================
    // تجميع التركيبات حسب النوع - Group Prosthetics by Type
    // ========================================

    groupProstheticsByType(prosthetics) {
        const grouped = groupBy(prosthetics, 'type');
        
        return {
            labels: Object.keys(grouped),
            data: Object.values(grouped).map(group => group.length),
            colors: ['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0', '#00BCD4']
        };
    }

    // ========================================
    // تجميع التركيبات حسب الحالة - Group Prosthetics by Status
    // ========================================

    groupProstheticsByStatus(prosthetics) {
        const grouped = groupBy(prosthetics, 'status');
        
        return {
            labels: Object.keys(grouped).map(status => {
                const statusObj = PROSTHETIC_STATUSES.find(s => s.id === status);
                return statusObj ? statusObj.name : status;
            }),
            data: Object.values(grouped).map(group => group.length),
            colors: Object.keys(grouped).map(status => {
                const statusObj = PROSTHETIC_STATUSES.find(s => s.id === status);
                return statusObj ? statusObj.color : '#666666';
            })
        };
    }

    // ========================================
    // حساب الإيرادات الشهرية للرسم البياني - Calculate Monthly Revenue Chart
    // ========================================

    calculateMonthlyRevenueChart(prosthetics) {
        const months = [];
        const revenues = [];
        
        // آخر 6 شهور
        for (let i = 5; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            
            const monthName = date.toLocaleDateString(getCurrentLanguage() === 'ar' ? 'ar-SA' : 'en-US', { month: 'short' });
            months.push(monthName);
            
            const monthRevenue = prosthetics
                .filter(p => {
                    const pDate = new Date(p.createdAt);
                    return pDate.getMonth() === date.getMonth() && pDate.getFullYear() === date.getFullYear();
                })
                .reduce((sum, p) => sum + (p.price || 0), 0);
                
            revenues.push(monthRevenue);
        }
        
        return {
            labels: months,
            data: revenues
        };
    }

    // ========================================
    // عرض لوحة التحكم - Render Dashboard
    // ========================================

    async render() {
        try {
            const contentArea = document.getElementById('content-area');
            if (!contentArea) return;

            // إنشاء HTML للوحة التحكم
            contentArea.innerHTML = this.getDashboardHTML();
            
            // عرض الإحصائيات الجديدة
            this.renderNewStatistics();

            // عرض النشاطات الأخيرة
            this.renderRecentActivities();

            // عرض الرسوم البيانية
            await this.renderCharts();

            // إعداد الأحداث
            this.setupEventListeners();

            // بدء عرض الوقت الحالي
            this.startTimeDisplay();
            
        } catch (error) {
            console.error('خطأ في عرض لوحة التحكم:', error);
            showError('فشل في تحميل لوحة التحكم');
        }
    }

    // ========================================
    // إنشاء HTML للوحة التحكم - Get Dashboard HTML
    // ========================================

    getDashboardHTML() {
        return `
            <div class="dashboard-container">
                <!-- ترحيب وعنوان -->
                <div class="dashboard-header">
                    <div class="welcome-section">
                        <h1 class="dashboard-title">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم الرئيسية
                        </h1>
                        <p class="dashboard-subtitle">
                            مرحباً بك في نظام إدارة معمل الأسنان المتقدم
                            <span class="current-time" id="current-time"></span>
                        </p>
                    </div>
                    <div class="dashboard-controls">
                        <button class="btn btn-primary" onclick="dentalLabApp.navigateToModule('prosthetics')">
                            <i class="fas fa-plus"></i>
                            تسجيل تركيبة جديدة
                        </button>
                        <button class="btn btn-outline" onclick="dashboardManager.refreshDashboard()" title="تحديث البيانات">
                            <i class="fas fa-sync"></i>
                            تحديث
                        </button>
                        <button class="btn btn-outline" onclick="dashboardManager.toggleAutoRefresh()" id="auto-refresh-btn" title="تبديل التحديث التلقائي">
                            <i class="fas fa-play"></i>
                            تحديث تلقائي
                        </button>
                        <button class="btn btn-outline" onclick="dashboardManager.exportReport()" title="تصدير تقرير">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="dashboard-stats">
                    <div class="stats-grid">

                        <!-- التركيبات الجديدة -->
                        <div class="stat-card stat-new-prosthetics" onclick="dashboardManager.navigateToSection('prosthetics', 'pending')">
                            <div class="stat-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="new-prosthetics-count">0</h3>
                                <p class="stat-title">التركيبات الجديدة</p>
                                <small class="stat-subtitle">في الانتظار</small>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up trend-up"></i>
                            </div>
                        </div>

                        <!-- التركيبات المعلقة -->
                        <div class="stat-card stat-pending-prosthetics" onclick="dashboardManager.navigateToSection('prosthetics', 'in_progress')">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="pending-prosthetics-count">0</h3>
                                <p class="stat-title">التركيبات المعلقة</p>
                                <small class="stat-subtitle">قيد التنفيذ</small>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-right trend-stable"></i>
                            </div>
                        </div>

                        <!-- إجمالي الأطباء -->
                        <div class="stat-card stat-doctors" onclick="dashboardManager.navigateToSection('doctors')">
                            <div class="stat-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="total-doctors-count">0</h3>
                                <p class="stat-title">إجمالي الأطباء</p>
                                <small class="stat-subtitle">مسجلين</small>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up trend-up"></i>
                            </div>
                        </div>

                        <!-- إجمالي الموظفين -->
                        <div class="stat-card stat-employees" onclick="dashboardManager.navigateToSection('employees')">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="total-employees-count">0</h3>
                                <p class="stat-title">إجمالي الموظفين</p>
                                <small class="stat-subtitle">نشطين</small>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up trend-up"></i>
                            </div>
                        </div>

                        <!-- رقم الحالة التالي -->
                        <div class="stat-card stat-next-case">
                            <div class="stat-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-value" id="next-case-number">1001</h3>
                                <p class="stat-title">رقم الحالة التالي</p>
                                <small class="stat-subtitle">تلقائي</small>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up trend-up"></i>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="dashboard-content">

                    <!-- النشاطات الأخيرة -->
                    <div class="dashboard-section recent-activities-section">
                        <div class="section-header">
                            <h2 class="section-title">
                                <i class="fas fa-history"></i>
                                النشاطات الأخيرة
                            </h2>
                            <div class="section-controls">
                                <button class="btn btn-sm btn-outline" onclick="dashboardManager.refreshActivities()">
                                    <i class="fas fa-sync"></i>
                                    تحديث
                                </button>
                                <button class="btn btn-sm btn-outline" onclick="dashboardManager.viewAllActivities()">
                                    <i class="fas fa-list"></i>
                                    عرض الكل
                                </button>
                            </div>
                        </div>

                        <div class="activities-container">
                            <div class="activities-list" id="recent-activities-list">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>
                    </div>

                <!-- الرسوم البيانية -->
                <div class="charts-section">
                    <h2 class="section-title">التحليلات والرسوم البيانية</h2>
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-chart-pie"></i>
                                    التركيبات حسب النوع
                                </h3>
                            </div>
                            <div class="card-content">
                                <canvas id="prosthetics-type-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-chart-line"></i>
                                    الإيرادات الشهرية
                                </h3>
                            </div>
                            <div class="card-content">
                                <canvas id="monthly-revenue-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-chart-bar"></i>
                                    حالات التركيبات
                                </h3>
                            </div>
                            <div class="card-content">
                                <canvas id="prosthetics-status-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- إحصائيات سريعة إضافية -->
                    <div class="dashboard-section quick-stats-section">
                        <div class="section-header">
                            <h2 class="section-title">
                                <i class="fas fa-chart-pie"></i>
                                إحصائيات سريعة
                            </h2>
                        </div>

                        <div class="quick-stats-grid">
                            <div class="quick-stat-item">
                                <div class="quick-stat-icon">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="quick-stat-content">
                                    <h4 id="today-cases">0</h4>
                                    <p>حالات اليوم</p>
                                </div>
                            </div>

                            <div class="quick-stat-item">
                                <div class="quick-stat-icon">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="quick-stat-content">
                                    <h4 id="week-cases">0</h4>
                                    <p>حالات الأسبوع</p>
                                </div>
                            </div>

                            <div class="quick-stat-item">
                                <div class="quick-stat-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="quick-stat-content">
                                    <h4 id="month-revenue">0</h4>
                                    <p>إيرادات الشهر</p>
                                </div>
                            </div>

                            <div class="quick-stat-item">
                                <div class="quick-stat-icon">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="quick-stat-content">
                                    <h4 id="completion-rate">0%</h4>
                                    <p>معدل الإنجاز</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- مؤشر التحديث التلقائي -->
                <div class="auto-refresh-indicator" id="auto-refresh-indicator">
                    <i class="fas fa-sync fa-spin"></i>
                    <span>تحديث تلقائي نشط</span>
                </div>

            </div>
        `;
    }

    // ========================================
    // عرض الإحصائيات الجديدة - Render New Statistics
    // ========================================

    renderNewStatistics() {
        // تحديث التركيبات الجديدة
        const newProstheticsElement = document.getElementById('new-prosthetics-count');
        if (newProstheticsElement) {
            newProstheticsElement.textContent = this.statistics.newProsthetics;
            newProstheticsElement.className = `stat-value ${this.getStatColor(this.statistics.newProsthetics, 'new')}`;
        }

        // تحديث التركيبات المعلقة
        const pendingProstheticsElement = document.getElementById('pending-prosthetics-count');
        if (pendingProstheticsElement) {
            pendingProstheticsElement.textContent = this.statistics.pendingProsthetics;
            pendingProstheticsElement.className = `stat-value ${this.getStatColor(this.statistics.pendingProsthetics, 'pending')}`;
        }

        // تحديث إجمالي الأطباء
        const totalDoctorsElement = document.getElementById('total-doctors-count');
        if (totalDoctorsElement) {
            totalDoctorsElement.textContent = this.statistics.totalDoctors;
            totalDoctorsElement.className = `stat-value ${this.getStatColor(this.statistics.totalDoctors, 'doctors')}`;
        }

        // تحديث إجمالي الموظفين
        const totalEmployeesElement = document.getElementById('total-employees-count');
        if (totalEmployeesElement) {
            totalEmployeesElement.textContent = this.statistics.totalEmployees;
            totalEmployeesElement.className = `stat-value ${this.getStatColor(this.statistics.totalEmployees, 'employees')}`;
        }

        // تحديث رقم الحالة التالي
        const nextCaseElement = document.getElementById('next-case-number');
        if (nextCaseElement) {
            nextCaseElement.textContent = this.statistics.nextCaseNumber;
        }

        // تحديث الإحصائيات السريعة الإضافية
        this.updateQuickStats();
    }

    // ========================================
    // تحديد لون الإحصائية - Get Stat Color
    // ========================================

    getStatColor(value, type) {
        switch (type) {
            case 'new':
                return value > 5 ? 'stat-red' : value > 2 ? 'stat-yellow' : 'stat-green';
            case 'pending':
                return value > 10 ? 'stat-red' : value > 5 ? 'stat-yellow' : 'stat-green';
            case 'doctors':
            case 'employees':
                return value > 0 ? 'stat-green' : 'stat-red';
            default:
                return 'stat-green';
        }
    }

    // ========================================
    // تحديث الإحصائيات السريعة - Update Quick Stats
    // ========================================

    updateQuickStats() {
        // حالات اليوم
        const todayCases = this.calculateTodayCases();
        const todayElement = document.getElementById('today-cases');
        if (todayElement) todayElement.textContent = todayCases;

        // حالات الأسبوع
        const weekCases = this.calculateWeekCases();
        const weekElement = document.getElementById('week-cases');
        if (weekElement) weekElement.textContent = weekCases;

        // إيرادات الشهر
        const monthRevenue = this.statistics.financial ? this.statistics.financial.monthlyRevenue : 0;
        const revenueElement = document.getElementById('month-revenue');
        if (revenueElement) revenueElement.textContent = formatCurrency(monthRevenue);

        // معدل الإنجاز
        const completionRate = this.statistics.prosthetics ? this.statistics.prosthetics.completionRate.toFixed(1) : 0;
        const completionElement = document.getElementById('completion-rate');
        if (completionElement) completionElement.textContent = `${completionRate}%`;
    }

    // ========================================
    // حساب حالات اليوم - Calculate Today Cases
    // ========================================

    calculateTodayCases() {
        // سيتم تنفيذها لاحقاً مع البيانات الفعلية
        return Math.floor(Math.random() * 10) + 1;
    }

    // ========================================
    // حساب حالات الأسبوع - Calculate Week Cases
    // ========================================

    calculateWeekCases() {
        // سيتم تنفيذها لاحقاً مع البيانات الفعلية
        return Math.floor(Math.random() * 50) + 10;
    }

    // ========================================
    // عرض النشاطات الأخيرة - Render Recent Activities
    // ========================================

    renderRecentActivities() {
        const activitiesList = document.getElementById('recent-activities-list');
        if (!activitiesList) return;

        if (this.recentActivities.length === 0) {
            activitiesList.innerHTML = `
                <div class="empty-activities">
                    <i class="fas fa-history"></i>
                    <h3>لا توجد نشاطات حديثة</h3>
                    <p>ستظهر النشاطات هنا عند حدوثها</p>
                </div>
            `;
            return;
        }

        activitiesList.innerHTML = this.recentActivities.map(activity => {
            const timeAgo = this.getTimeAgo(activity.time);
            const statusClass = this.getActivityStatusClass(activity.status);
            const priorityClass = this.getActivityPriorityClass(activity.priority);

            return `
                <div class="activity-item ${statusClass} ${priorityClass}">
                    <div class="activity-icon">
                        <i class="${this.getActivityIcon(activity.type)}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-description">${activity.description}</div>
                        <div class="activity-meta">
                            <span class="activity-user">
                                <i class="fas fa-user"></i>
                                ${activity.user}
                            </span>
                            <span class="activity-time">
                                <i class="fas fa-clock"></i>
                                ${timeAgo}
                            </span>
                        </div>
                    </div>
                    <div class="activity-status">
                        <span class="status-badge status-${activity.status}">${this.getStatusText(activity.status)}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    // ========================================
    // عرض الرسوم البيانية - Render Charts
    // ========================================

    async renderCharts() {
        try {
            // رسم بياني للتركيبات حسب النوع
            await this.renderProstheticsTypeChart();
            
            // رسم بياني للإيرادات الشهرية
            await this.renderMonthlyRevenueChart();
            
            // رسم بياني لحالات التركيبات
            await this.renderProstheticsStatusChart();
            
        } catch (error) {
            console.error('خطأ في عرض الرسوم البيانية:', error);
        }
    }

    // ========================================
    // رسم بياني للتركيبات حسب النوع - Render Prosthetics Type Chart
    // ========================================

    async renderProstheticsTypeChart() {
        const canvas = document.getElementById('prosthetics-type-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.charts.prostheticsByType;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: data.colors,
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    // ========================================
    // رسم بياني للإيرادات الشهرية - Render Monthly Revenue Chart
    // ========================================

    async renderMonthlyRevenueChart() {
        const canvas = document.getElementById('monthly-revenue-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.charts.monthlyRevenue;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'الإيرادات',
                    data: data.data,
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }

    // ========================================
    // رسم بياني لحالات التركيبات - Render Prosthetics Status Chart
    // ========================================

    async renderProstheticsStatusChart() {
        const canvas = document.getElementById('prosthetics-status-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.charts.prostheticsByStatus;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: data.colors,
                    borderWidth: 0,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // ========================================
    // الدوال المساعدة للنشاطات - Activity Helper Functions
    // ========================================

    getTimeAgo(timeString) {
        const now = new Date();
        const time = new Date(timeString);
        const diffInSeconds = Math.floor((now - time) / 1000);

        if (diffInSeconds < 60) {
            return 'منذ لحظات';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `منذ ${minutes} دقيقة`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `منذ ${hours} ساعة`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `منذ ${days} يوم`;
        }
    }

    getActivityIcon(type) {
        const icons = {
            'system_initialized': 'fas fa-cogs',
            'default_data_inserted': 'fas fa-database',
            'doctor_added': 'fas fa-user-md',
            'employee_added': 'fas fa-user-plus',
            'prosthetic_created': 'fas fa-tooth',
            'prosthetic_status_updated': 'fas fa-edit',
            'payment_received': 'fas fa-money-bill-wave',
            'backup_created': 'fas fa-save',
            'user_login': 'fas fa-sign-in-alt',
            'dashboard_accessed': 'fas fa-tachometer-alt'
        };
        return icons[type] || 'fas fa-info-circle';
    }

    getActivityStatusClass(status) {
        const classes = {
            'success': 'activity-success',
            'info': 'activity-info',
            'warning': 'activity-warning',
            'error': 'activity-error'
        };
        return classes[status] || 'activity-info';
    }

    getActivityPriorityClass(priority) {
        const classes = {
            'low': 'priority-low',
            'normal': 'priority-normal',
            'high': 'priority-high',
            'critical': 'priority-critical'
        };
        return classes[priority] || 'priority-normal';
    }

    getStatusText(status) {
        const texts = {
            'success': 'نجح',
            'info': 'معلومات',
            'warning': 'تحذير',
            'error': 'خطأ'
        };
        return texts[status] || 'معلومات';
    }

    // ========================================
    // بدء عرض الوقت الحالي - Start Time Display
    // ========================================

    startTimeDisplay() {
        const updateTime = () => {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = ` - ${timeString}`;
            }
        };

        updateTime();
        setInterval(updateTime, 60000); // تحديث كل دقيقة
    }

    // ========================================
    // إعداد الأحداث - Setup Event Listeners
    // ========================================

    setupEventListeners() {
        // تحديث حالة زر التحديث التلقائي
        this.updateAutoRefreshButton();
    }

    // ========================================
    // تحديث زر التحديث التلقائي - Update Auto Refresh Button
    // ========================================

    updateAutoRefreshButton() {
        const btn = document.getElementById('auto-refresh-btn');
        const indicator = document.getElementById('auto-refresh-indicator');

        if (btn) {
            if (this.isAutoRefreshEnabled) {
                btn.innerHTML = '<i class="fas fa-pause"></i> إيقاف التحديث';
                btn.classList.add('btn-warning');
                btn.classList.remove('btn-outline');
            } else {
                btn.innerHTML = '<i class="fas fa-play"></i> تحديث تلقائي';
                btn.classList.remove('btn-warning');
                btn.classList.add('btn-outline');
            }
        }

        if (indicator) {
            indicator.style.display = this.isAutoRefreshEnabled ? 'flex' : 'none';
        }
    }

    // ========================================
    // الوظائف الجديدة - New Functions
    // ========================================

    toggleAutoRefresh() {
        this.isAutoRefreshEnabled = !this.isAutoRefreshEnabled;

        if (this.isAutoRefreshEnabled) {
            this.startAutoRefresh();
            showSuccess('تم تفعيل التحديث التلقائي');
        } else {
            this.stopAutoRefresh();
            showInfo('تم إيقاف التحديث التلقائي');
        }

        this.updateAutoRefreshButton();
    }

    startAutoRefresh() {
        this.stopAutoRefresh(); // إيقاف أي تحديث سابق

        if (this.isAutoRefreshEnabled) {
            this.refreshInterval = setInterval(() => {
                this.refreshDashboard();
            }, this.refreshRate);
        }
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    async refreshActivities() {
        try {
            await this.loadRecentActivities();
            this.renderRecentActivities();
            showSuccess('تم تحديث النشاطات');
        } catch (error) {
            console.error('خطأ في تحديث النشاطات:', error);
            showError('فشل في تحديث النشاطات');
        }
    }

    viewAllActivities() {
        // TODO: تنفيذ عرض جميع النشاطات
        showInfo('عرض جميع النشاطات - قيد التطوير');
    }

    exportReport() {
        // TODO: تنفيذ تصدير التقرير
        showInfo('تصدير التقرير - قيد التطوير');
    }

    navigateToSection(module, filter = null) {
        if (typeof dentalLabApp !== 'undefined') {
            dentalLabApp.navigateToModule(module);

            // إضافة تصفية إذا كانت متوفرة
            if (filter && module === 'prosthetics') {
                setTimeout(() => {
                    // تطبيق التصفية حسب الحالة
                    const statusFilter = document.getElementById('status-filter');
                    if (statusFilter) {
                        statusFilter.value = filter;
                        statusFilter.dispatchEvent(new Event('change'));
                    }
                }, 500);
            }
        }
    }

    // ========================================
    // تحديث لوحة التحكم - Refresh Dashboard
    // ========================================

    async refreshDashboard() {
        try {
            // إعادة تحميل البيانات
            await this.loadInitialData();

            // إعادة عرض الإحصائيات الجديدة
            this.renderNewStatistics();

            // إعادة عرض النشاطات الأخيرة
            this.renderRecentActivities();

            // إعادة عرض الرسوم البيانية
            await this.renderCharts();

            // تسجيل نشاط التحديث
            await this.logActivity('dashboard_refreshed', 'تم تحديث لوحة التحكم', 'dashboard');

        } catch (error) {
            console.error('خطأ في تحديث لوحة التحكم:', error);
            showError('فشل في تحديث البيانات');
        }
    }

    // ========================================
    // تسجيل نشاط جديد - Log New Activity
    // ========================================

    async logActivity(type, description, entityType = null, entityId = null, priority = 'normal', status = 'info') {
        try {
            const currentUser = getCurrentUser();
            const activityData = {
                activity_type: type,
                description: description,
                entity_type: entityType,
                entity_id: entityId,
                user_name: currentUser ? currentUser.name : 'مستخدم غير معروف',
                user_role: currentUser ? currentUser.role : 'unknown',
                details: JSON.stringify({
                    timestamp: new Date().toISOString(),
                    user_agent: navigator.userAgent,
                    page: 'dashboard'
                }),
                priority: priority,
                status: status
            };

            await db.insert('activity_logs', activityData);

            // تحديث النشاطات الأخيرة
            await this.loadRecentActivities();
            this.renderRecentActivities();

        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    // ========================================
    // إعداد التحديث التلقائي - Setup Auto Refresh
    // ========================================

    setupAutoRefresh() {
        if (this.autoRefresh) {
            this.refreshInterval = setInterval(() => {
                this.refreshDashboard();
            }, this.refreshRate);
        }
    }

    // ========================================
    // تنظيف الموارد - Cleanup
    // ========================================

    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
        
        // تنظيف الرسوم البيانية
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        
        this.charts = {};
    }
}

// ========================================
// تهيئة مدير لوحة التحكم - Initialize Dashboard Manager
// ========================================

// إنشاء مثيل من مدير لوحة التحكم
const dashboardManager = new DashboardManager();

// دالة تهيئة لوحة التحكم
async function initializeDashboard() {
    try {
        const success = await dashboardManager.init();
        if (success) {
            console.log('✅ تم تهيئة لوحة التحكم بنجاح');
        } else {
            console.error('❌ فشل في تهيئة لوحة التحكم');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة لوحة التحكم:', error);
        return false;
    }
}

// تصدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { dashboardManager, DashboardManager };
}

// ========================================
// تصدير الفئة للاستخدام العام
// ========================================

window.DashboardManager = DashboardManager;
window.dashboardManager = dashboardManager;

console.log('✅ تم تحميل وحدة لوحة التحكم بنجاح');
