// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// مدير قوائم أسعار الأطباء - Doctor Price Manager
// ========================================

console.log('💰 تحميل مدير قوائم أسعار الأطباء...');

// ========================================
// فئة مدير قوائم أسعار الأطباء - Doctor Price Manager
// ========================================

class DoctorPriceManager {
    constructor() {
        this.doctorPriceLists = [];
        this.prostheticTypes = [];
        this.doctors = [];
        this.currentDoctorId = null;
        this.currentPriceList = [];
    }

    // ========================================
    // تهيئة المدير - Initialize Manager
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة مدير قوائم الأسعار...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            console.log('✅ تم تهيئة مدير قوائم الأسعار بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير قوائم الأسعار:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل قوائم الأسعار
            this.doctorPriceLists = await db.findAll('doctor_price_lists');
            
            // تحميل أنواع التركيبات
            this.prostheticTypes = await db.findAll('prosthetic_types');
            
            // تحميل الأطباء
            this.doctors = await db.findAll('doctors');
            
            console.log(`📊 تم تحميل ${this.doctorPriceLists.length} قائمة أسعار`);
            console.log(`🦷 تم تحميل ${this.prostheticTypes.length} نوع تركيبة`);
            console.log(`👨‍⚕️ تم تحميل ${this.doctors.length} طبيب`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // عرض نافذة إدارة الأسعار - Show Price Management Modal
    // ========================================

    async showPriceManagementModal(doctorId) {
        this.currentDoctorId = doctorId;
        const doctor = this.doctors.find(d => d.id === doctorId);
        
        if (!doctor) {
            showError('لم يتم العثور على الطبيب');
            return;
        }

        // تحميل قائمة أسعار الطبيب
        await this.loadDoctorPriceList(doctorId);

        // إنشاء النافذة المنبثقة
        this.createPriceManagementModal(doctor);
    }

    // ========================================
    // إنشاء نافذة إدارة الأسعار - Create Price Management Modal
    // ========================================

    createPriceManagementModal(doctor) {
        const modalHTML = `
            <div id="price-management-modal" class="modal">
                <div class="modal-overlay"></div>
                <div class="modal-content extra-large-modal">
                    <div class="modal-header">
                        <h2>إدارة أسعار الطبيب: ${doctor.name}</h2>
                        <button class="modal-close" onclick="doctorPriceManager.closePriceManagementModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <!-- معلومات الطبيب -->
                        <div class="doctor-info-section">
                            <div class="doctor-card">
                                <div class="doctor-details">
                                    <h3>${doctor.name}</h3>
                                    <p><strong>التخصص:</strong> ${doctor.specialty}</p>
                                    <p><strong>العيادة:</strong> ${doctor.clinic_name || '-'}</p>
                                    <p><strong>نسبة الخصم العامة:</strong> ${doctor.discount_percentage || 0}%</p>
                                </div>
                                <div class="doctor-stats">
                                    <div class="stat-item">
                                        <span class="stat-value">${this.currentPriceList.length}</span>
                                        <span class="stat-label">أسعار مخصصة</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">${doctor.total_cases || 0}</span>
                                        <span class="stat-label">إجمالي الحالات</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أدوات الإدارة -->
                        <div class="price-management-tools">
                            <div class="tools-left">
                                <button class="btn btn-primary" onclick="doctorPriceManager.showAddPriceModal()">
                                    <i class="fas fa-plus"></i>
                                    إضافة سعر مخصص
                                </button>
                                <button class="btn btn-outline" onclick="doctorPriceManager.copyFromAnotherDoctor()">
                                    <i class="fas fa-copy"></i>
                                    نسخ من طبيب آخر
                                </button>
                                <button class="btn btn-outline" onclick="doctorPriceManager.applyBulkDiscount()">
                                    <i class="fas fa-percentage"></i>
                                    تطبيق خصم شامل
                                </button>
                            </div>
                            <div class="tools-right">
                                <button class="btn btn-outline" onclick="doctorPriceManager.exportPriceList()">
                                    <i class="fas fa-download"></i>
                                    تصدير القائمة
                                </button>
                                <button class="btn btn-outline" onclick="doctorPriceManager.printPriceList()">
                                    <i class="fas fa-print"></i>
                                    طباعة
                                </button>
                            </div>
                        </div>

                        <!-- قائمة الأسعار -->
                        <div class="price-list-container">
                            <div class="price-list-header">
                                <h4>قائمة الأسعار المخصصة</h4>
                                <div class="price-filters">
                                    <select id="category-filter" class="form-control" onchange="doctorPriceManager.filterPriceList()">
                                        <option value="">جميع الفئات</option>
                                        <option value="porcelain">بورسلين</option>
                                        <option value="zirconia">زيركون</option>
                                        <option value="metal">معدن</option>
                                        <option value="dentures">أطقم</option>
                                        <option value="orthodontics">تقويم</option>
                                        <option value="additional">إضافية</option>
                                    </select>
                                    <input type="search" id="price-search" class="form-control" placeholder="البحث..." onkeyup="doctorPriceManager.filterPriceList()">
                                </div>
                            </div>
                            
                            <div class="price-table-container">
                                <table class="table price-table">
                                    <thead>
                                        <tr>
                                            <th>نوع التركيبة</th>
                                            <th>الفئة</th>
                                            <th>السعر الأساسي</th>
                                            <th>السعر المخصص</th>
                                            <th>نسبة الخصم</th>
                                            <th>التوفير</th>
                                            <th>تاريخ السريان</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="price-table-body">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- إحصائيات الأسعار -->
                        <div class="price-statistics">
                            <h4>إحصائيات الأسعار</h4>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-tags"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-value" id="total-custom-prices">0</span>
                                        <span class="stat-label">أسعار مخصصة</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-percentage"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-value" id="average-discount">0%</span>
                                        <span class="stat-label">متوسط الخصم</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-value" id="total-savings">0</span>
                                        <span class="stat-label">إجمالي التوفير</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-content">
                                        <span class="stat-value" id="active-prices">0</span>
                                        <span class="stat-label">أسعار نشطة</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="doctorPriceManager.closePriceManagementModal()">
                            إغلاق
                        </button>
                        <button type="button" class="btn btn-success" onclick="doctorPriceManager.saveAllChanges()">
                            <i class="fas fa-save"></i>
                            حفظ جميع التغييرات
                        </button>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // عرض النافذة
        const modal = document.getElementById('price-management-modal');
        modal.classList.remove('hidden');
        
        // عرض قائمة الأسعار
        this.renderPriceList();
        this.updatePriceStatistics();
    }

    // ========================================
    // تحميل قائمة أسعار الطبيب - Load Doctor Price List
    // ========================================

    async loadDoctorPriceList(doctorId) {
        try {
            // تحميل الأسعار المخصصة للطبيب
            this.currentPriceList = this.doctorPriceLists.filter(price => 
                price.doctor_id === doctorId && price.isActive
            );

            // إضافة معلومات نوع التركيبة لكل سعر
            this.currentPriceList = this.currentPriceList.map(price => {
                const prostheticType = this.prostheticTypes.find(type => 
                    type.id === price.prosthetic_type_id
                );
                return {
                    ...price,
                    prosthetic_type: prostheticType
                };
            });

            console.log(`📋 تم تحميل ${this.currentPriceList.length} سعر مخصص للطبيب`);
            
        } catch (error) {
            console.error('خطأ في تحميل قائمة أسعار الطبيب:', error);
            throw error;
        }
    }

    // ========================================
    // عرض قائمة الأسعار - Render Price List
    // ========================================

    renderPriceList() {
        const tableBody = document.getElementById('price-table-body');
        if (!tableBody) return;

        if (this.currentPriceList.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-tags"></i>
                            <h3>لا توجد أسعار مخصصة</h3>
                            <p>ابدأ بإضافة أول سعر مخصص</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.currentPriceList.map(price => {
            const prostheticType = price.prosthetic_type;
            if (!prostheticType) return '';

            const basePrice = prostheticType.unit_price;
            const customPrice = price.custom_price;
            const savings = basePrice - customPrice;
            const discountPercentage = ((savings / basePrice) * 100).toFixed(1);
            
            const isActive = new Date(price.effective_date) <= new Date() && 
                           (!price.expiry_date || new Date(price.expiry_date) >= new Date());
            
            return `
                <tr data-price-id="${price.id}">
                    <td>
                        <div class="prosthetic-info">
                            <strong>${prostheticType.type_name}</strong>
                            <br><small>${prostheticType.material}</small>
                        </div>
                    </td>
                    <td>
                        <span class="category-badge category-${prostheticType.category}">
                            ${this.getCategoryName(prostheticType.category)}
                        </span>
                    </td>
                    <td>
                        <span class="base-price">${formatCurrency(basePrice)}</span>
                    </td>
                    <td>
                        <span class="custom-price">${formatCurrency(customPrice)}</span>
                    </td>
                    <td>
                        <span class="discount-percentage">${discountPercentage}%</span>
                    </td>
                    <td>
                        <span class="savings ${savings > 0 ? 'positive' : 'negative'}">
                            ${formatCurrency(savings)}
                        </span>
                    </td>
                    <td>
                        <span class="effective-date">${formatDate(price.effective_date)}</span>
                        ${price.expiry_date ? `<br><small>حتى ${formatDate(price.expiry_date)}</small>` : ''}
                    </td>
                    <td>
                        <span class="status-badge status-${isActive ? 'active' : 'inactive'}">
                            ${isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline" onclick="doctorPriceManager.editPrice(${price.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="doctorPriceManager.duplicatePrice(${price.id})" title="نسخ">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="doctorPriceManager.deletePrice(${price.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    // ========================================
    // تحديث إحصائيات الأسعار - Update Price Statistics
    // ========================================

    updatePriceStatistics() {
        const totalCustomPrices = this.currentPriceList.length;
        
        const activePrices = this.currentPriceList.filter(price => {
            const isActive = new Date(price.effective_date) <= new Date() && 
                           (!price.expiry_date || new Date(price.expiry_date) >= new Date());
            return isActive;
        }).length;

        let totalSavings = 0;
        let totalDiscountPercentage = 0;

        this.currentPriceList.forEach(price => {
            const prostheticType = price.prosthetic_type;
            if (prostheticType) {
                const savings = prostheticType.unit_price - price.custom_price;
                totalSavings += savings;
                totalDiscountPercentage += ((savings / prostheticType.unit_price) * 100);
            }
        });

        const averageDiscount = totalCustomPrices > 0 ? 
            (totalDiscountPercentage / totalCustomPrices).toFixed(1) : 0;

        // تحديث العناصر
        const elements = {
            'total-custom-prices': totalCustomPrices,
            'average-discount': `${averageDiscount}%`,
            'total-savings': formatCurrency(totalSavings),
            'active-prices': activePrices
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
    }

    // ========================================
    // دوال مساعدة - Helper Functions
    // ========================================

    getCategoryName(category) {
        const categoryNames = {
            'porcelain': 'بورسلين',
            'zirconia': 'زيركون',
            'metal': 'معدن',
            'dentures': 'أطقم',
            'orthodontics': 'تقويم',
            'additional': 'إضافية'
        };
        return categoryNames[category] || category;
    }

    // ========================================
    // إغلاق النافذة - Close Modal
    // ========================================

    closePriceManagementModal() {
        const modal = document.getElementById('price-management-modal');
        if (modal) {
            modal.remove();
        }
        this.currentDoctorId = null;
        this.currentPriceList = [];
    }

    // ========================================
    // وظائف مؤقتة للتطوير المستقبلي - Placeholder Functions
    // ========================================

    showAddPriceModal() {
        console.log('إضافة سعر مخصص - قيد التطوير');
        showInfo('هذه الميزة قيد التطوير');
    }

    copyFromAnotherDoctor() {
        console.log('نسخ من طبيب آخر - قيد التطوير');
        showInfo('هذه الميزة قيد التطوير');
    }

    applyBulkDiscount() {
        console.log('تطبيق خصم شامل - قيد التطوير');
        showInfo('هذه الميزة قيد التطوير');
    }

    exportPriceList() {
        console.log('تصدير القائمة - قيد التطوير');
        showInfo('هذه الميزة قيد التطوير');
    }

    printPriceList() {
        console.log('طباعة القائمة - قيد التطوير');
        showInfo('هذه الميزة قيد التطوير');
    }

    filterPriceList() {
        console.log('تصفية القائمة - قيد التطوير');
    }

    editPrice(id) {
        console.log('تعديل السعر:', id);
        showInfo('هذه الميزة قيد التطوير');
    }

    duplicatePrice(id) {
        console.log('نسخ السعر:', id);
        showInfo('هذه الميزة قيد التطوير');
    }

    deletePrice(id) {
        console.log('حذف السعر:', id);
        showInfo('هذه الميزة قيد التطوير');
    }

    saveAllChanges() {
        console.log('حفظ جميع التغييرات - قيد التطوير');
        showSuccess('تم حفظ التغييرات بنجاح');
    }
}

// ========================================
// تهيئة مدير قوائم الأسعار - Initialize Price Manager
// ========================================

// إنشاء مثيل من مدير قوائم الأسعار
const doctorPriceManager = new DoctorPriceManager();

// دالة تهيئة المدير
async function initializeDoctorPriceManager() {
    try {
        const success = await doctorPriceManager.init();
        if (success) {
            console.log('✅ تم تهيئة مدير قوائم الأسعار بنجاح');
        } else {
            console.error('❌ فشل في تهيئة مدير قوائم الأسعار');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة مدير قوائم الأسعار:', error);
        return false;
    }
}

// تصدير المدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { doctorPriceManager, DoctorPriceManager };
}

console.log('✅ تم تحميل مدير قوائم أسعار الأطباء بنجاح');
