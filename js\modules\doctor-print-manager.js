// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// مدير طباعة كشوف الحساب - Doctor Print Manager
// ========================================

console.log('🖨️ تحميل مدير طباعة كشوف الحساب...');

// ========================================
// فئة مدير طباعة كشوف الحساب - Doctor Print Manager
// ========================================

class DoctorPrintManager {
    constructor() {
        this.printStyles = this.getPrintStyles();
        this.companyInfo = this.getCompanyInfo();
    }

    // ========================================
    // معلومات الشركة - Company Information
    // ========================================

    getCompanyInfo() {
        return {
            name: 'معمل الأسنان المتقدم',
            nameEn: 'Advanced Dental Lab',
            address: 'الرياض - المملكة العربية السعودية',
            phone: '+966 11 123 4567',
            email: '<EMAIL>',
            website: 'www.advanceddentallab.com',
            taxNumber: '*********',
            crNumber: 'CR-2024-001',
            logo: 'assets/images/logo.png' // مسار الشعار
        };
    }

    // ========================================
    // أنماط الطباعة - Print Styles
    // ========================================

    getPrintStyles() {
        return `
            <style>
                @media print {
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }
                    
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        font-size: 12px;
                        line-height: 1.4;
                        color: #333;
                        direction: rtl;
                        text-align: right;
                    }
                    
                    .print-container {
                        width: 100%;
                        max-width: 210mm;
                        margin: 0 auto;
                        padding: 15mm;
                        background: white;
                    }
                    
                    .statement-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        margin-bottom: 20px;
                        padding-bottom: 15px;
                        border-bottom: 2px solid #333;
                    }
                    
                    .company-info {
                        flex: 1;
                    }
                    
                    .company-name {
                        font-size: 18px;
                        font-weight: bold;
                        color: #2c5aa0;
                        margin-bottom: 5px;
                    }
                    
                    .company-name-en {
                        font-size: 14px;
                        color: #666;
                        margin-bottom: 10px;
                    }
                    
                    .company-details {
                        font-size: 10px;
                        color: #666;
                        line-height: 1.6;
                    }
                    
                    .statement-title {
                        text-align: center;
                        flex: 1;
                    }
                    
                    .statement-title h1 {
                        font-size: 20px;
                        font-weight: bold;
                        color: #2c5aa0;
                        margin-bottom: 5px;
                    }
                    
                    .statement-number {
                        font-size: 12px;
                        color: #666;
                    }
                    
                    .logo-section {
                        flex: 1;
                        text-align: left;
                    }
                    
                    .logo {
                        max-width: 80px;
                        max-height: 80px;
                    }
                    
                    .statement-info {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                        margin-bottom: 20px;
                        padding: 15px;
                        background: #f8f9fa;
                        border-radius: 5px;
                    }
                    
                    .info-section h3 {
                        font-size: 14px;
                        font-weight: bold;
                        color: #2c5aa0;
                        margin-bottom: 10px;
                        border-bottom: 1px solid #ddd;
                        padding-bottom: 5px;
                    }
                    
                    .info-item {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 5px;
                        font-size: 11px;
                    }
                    
                    .info-label {
                        font-weight: bold;
                        color: #555;
                    }
                    
                    .info-value {
                        color: #333;
                    }
                    
                    .statement-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                        font-size: 10px;
                    }
                    
                    .statement-table th {
                        background: #2c5aa0;
                        color: white;
                        padding: 8px 5px;
                        text-align: center;
                        font-weight: bold;
                        border: 1px solid #ddd;
                    }
                    
                    .statement-table td {
                        padding: 6px 5px;
                        border: 1px solid #ddd;
                        text-align: center;
                    }
                    
                    .statement-table tbody tr:nth-child(even) {
                        background: #f8f9fa;
                    }
                    
                    .statement-table tbody tr:hover {
                        background: #e3f2fd;
                    }
                    
                    .text-right {
                        text-align: right !important;
                    }
                    
                    .text-center {
                        text-align: center !important;
                    }
                    
                    .font-bold {
                        font-weight: bold;
                    }
                    
                    .statement-summary {
                        margin-top: 20px;
                        padding: 15px;
                        background: #f8f9fa;
                        border-radius: 5px;
                    }
                    
                    .summary-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    
                    .summary-item {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 8px;
                        padding: 5px 0;
                    }
                    
                    .summary-item.total {
                        border-top: 2px solid #2c5aa0;
                        padding-top: 10px;
                        margin-top: 10px;
                        font-weight: bold;
                        font-size: 12px;
                    }
                    
                    .statement-footer {
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 1px solid #ddd;
                        font-size: 10px;
                        color: #666;
                    }
                    
                    .footer-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr 1fr;
                        gap: 20px;
                        text-align: center;
                    }
                    
                    .signature-section {
                        padding: 20px 0;
                    }
                    
                    .signature-line {
                        border-top: 1px solid #333;
                        margin-top: 40px;
                        padding-top: 5px;
                        text-align: center;
                    }
                    
                    .page-break {
                        page-break-before: always;
                    }
                    
                    @page {
                        size: A4;
                        margin: 15mm;
                    }
                }
            </style>
        `;
    }

    // ========================================
    // طباعة كشف حساب - Print Statement
    // ========================================

    async printStatement(statementId) {
        try {
            // تحميل بيانات كشف الحساب
            const statement = await this.getStatementData(statementId);
            if (!statement) {
                throw new Error('لم يتم العثور على كشف الحساب');
            }

            // إنشاء HTML للطباعة
            const printHTML = this.generateStatementHTML(statement);
            
            // فتح نافذة الطباعة
            this.openPrintWindow(printHTML);
            
        } catch (error) {
            console.error('خطأ في طباعة كشف الحساب:', error);
            showError('فشل في طباعة كشف الحساب: ' + error.message);
        }
    }

    // ========================================
    // تحميل بيانات كشف الحساب - Get Statement Data
    // ========================================

    async getStatementData(statementId) {
        try {
            // تحميل كشف الحساب
            const statement = await db.findById('doctor_statements', statementId);
            if (!statement) return null;

            // تحميل بيانات الطبيب
            const doctor = await db.findById('doctors', statement.doctor_id);
            
            // تحميل تفاصيل كشف الحساب
            const items = await db.findAll('doctor_statement_items', {
                where: { statement_id: statementId }
            });

            return {
                ...statement,
                doctor: doctor,
                items: items || []
            };
            
        } catch (error) {
            console.error('خطأ في تحميل بيانات كشف الحساب:', error);
            return null;
        }
    }

    // ========================================
    // إنشاء HTML لكشف الحساب - Generate Statement HTML
    // ========================================

    generateStatementHTML(statement) {
        const doctor = statement.doctor;
        const items = statement.items;
        
        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>كشف حساب - ${statement.statement_number}</title>
                ${this.printStyles}
            </head>
            <body>
                <div class="print-container">
                    
                    <!-- رأس كشف الحساب -->
                    <div class="statement-header">
                        <div class="company-info">
                            <div class="company-name">${this.companyInfo.name}</div>
                            <div class="company-name-en">${this.companyInfo.nameEn}</div>
                            <div class="company-details">
                                <div>العنوان: ${this.companyInfo.address}</div>
                                <div>الهاتف: ${this.companyInfo.phone}</div>
                                <div>البريد الإلكتروني: ${this.companyInfo.email}</div>
                                <div>الرقم الضريبي: ${this.companyInfo.taxNumber}</div>
                            </div>
                        </div>
                        
                        <div class="statement-title">
                            <h1>كشف حساب</h1>
                            <div class="statement-number">رقم الكشف: ${statement.statement_number}</div>
                        </div>
                        
                        <div class="logo-section">
                            <!-- يمكن إضافة الشعار هنا -->
                            <div style="width: 80px; height: 80px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #999;">
                                شعار المعمل
                            </div>
                        </div>
                    </div>

                    <!-- معلومات كشف الحساب -->
                    <div class="statement-info">
                        <div class="info-section">
                            <h3>معلومات الطبيب</h3>
                            <div class="info-item">
                                <span class="info-label">اسم الطبيب:</span>
                                <span class="info-value">${doctor.name}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">التخصص:</span>
                                <span class="info-value">${doctor.specialty || '-'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">العيادة:</span>
                                <span class="info-value">${doctor.clinic_name || '-'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">الهاتف:</span>
                                <span class="info-value">${doctor.phone || '-'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">رقم الترخيص:</span>
                                <span class="info-value">${doctor.license_number || '-'}</span>
                            </div>
                        </div>
                        
                        <div class="info-section">
                            <h3>معلومات الكشف</h3>
                            <div class="info-item">
                                <span class="info-label">تاريخ الكشف:</span>
                                <span class="info-value">${this.formatDate(statement.statement_date)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">فترة الكشف:</span>
                                <span class="info-value">من ${this.formatDate(statement.period_from)} إلى ${this.formatDate(statement.period_to)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">تاريخ الاستحقاق:</span>
                                <span class="info-value">${this.formatDate(statement.due_date)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">حالة الكشف:</span>
                                <span class="info-value">${this.getStatusText(statement.status)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">أُنشئ بواسطة:</span>
                                <span class="info-value">${statement.generated_by || 'النظام'}</span>
                            </div>
                        </div>
                    </div>

                    <!-- جدول تفاصيل كشف الحساب -->
                    <table class="statement-table">
                        <thead>
                            <tr>
                                <th style="width: 8%">#</th>
                                <th style="width: 12%">التاريخ</th>
                                <th style="width: 20%">اسم المريض</th>
                                <th style="width: 20%">نوع التركيبة</th>
                                <th style="width: 8%">الكمية</th>
                                <th style="width: 12%">سعر الوحدة</th>
                                <th style="width: 12%">المجموع</th>
                                <th style="width: 8%">الخصم %</th>
                                <th style="width: 12%">صافي المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${items.map((item, index) => `
                                <tr>
                                    <td class="text-center">${index + 1}</td>
                                    <td class="text-center">${this.formatDate(item.item_date)}</td>
                                    <td class="text-right">${item.patient_name}</td>
                                    <td class="text-right">${item.prosthetic_type}</td>
                                    <td class="text-center">${item.quantity}</td>
                                    <td class="text-center">${this.formatCurrency(item.unit_price)}</td>
                                    <td class="text-center">${this.formatCurrency(item.total_price)}</td>
                                    <td class="text-center">${item.discount_percentage || 0}%</td>
                                    <td class="text-center font-bold">${this.formatCurrency(item.net_amount)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <!-- ملخص كشف الحساب -->
                    <div class="statement-summary">
                        <div class="summary-grid">
                            <div class="summary-section">
                                <div class="summary-item">
                                    <span>عدد الحالات:</span>
                                    <span class="font-bold">${statement.total_cases}</span>
                                </div>
                                <div class="summary-item">
                                    <span>إجمالي المبلغ:</span>
                                    <span>${this.formatCurrency(statement.total_amount)}</span>
                                </div>
                                <div class="summary-item">
                                    <span>إجمالي الخصم:</span>
                                    <span>${this.formatCurrency(statement.total_discount)}</span>
                                </div>
                                <div class="summary-item total">
                                    <span>صافي المبلغ:</span>
                                    <span>${this.formatCurrency(statement.net_amount)}</span>
                                </div>
                            </div>
                            
                            <div class="summary-section">
                                <div class="summary-item">
                                    <span>الرصيد السابق:</span>
                                    <span>${this.formatCurrency(statement.previous_balance)}</span>
                                </div>
                                <div class="summary-item">
                                    <span>المدفوعات المستلمة:</span>
                                    <span>${this.formatCurrency(statement.payments_received)}</span>
                                </div>
                                <div class="summary-item total">
                                    <span>الرصيد الحالي:</span>
                                    <span class="font-bold">${this.formatCurrency(statement.current_balance)}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تذييل كشف الحساب -->
                    <div class="statement-footer">
                        <div class="footer-grid">
                            <div class="signature-section">
                                <div>إعداد:</div>
                                <div class="signature-line">${statement.generated_by || 'النظام'}</div>
                            </div>
                            <div class="signature-section">
                                <div>مراجعة:</div>
                                <div class="signature-line">المدير المالي</div>
                            </div>
                            <div class="signature-section">
                                <div>اعتماد:</div>
                                <div class="signature-line">مدير المعمل</div>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 20px; font-size: 9px; color: #999;">
                            تم إنشاء هذا الكشف تلقائياً بواسطة نظام إدارة معمل الأسنان المتقدم
                            <br>
                            تاريخ الطباعة: ${this.formatDateTime(new Date())}
                        </div>
                    </div>

                </div>
            </body>
            </html>
        `;
    }

    // ========================================
    // فتح نافذة الطباعة - Open Print Window
    // ========================================

    openPrintWindow(htmlContent) {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (!printWindow) {
            showError('فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
            return;
        }

        printWindow.document.write(htmlContent);
        printWindow.document.close();
        
        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        };
    }

    // ========================================
    // دوال مساعدة - Helper Functions
    // ========================================

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    formatDateTime(date) {
        return date.toLocaleString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatCurrency(amount) {
        if (amount === null || amount === undefined) return '0.00 ريال';
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 2
        }).format(amount);
    }

    getStatusText(status) {
        const statusTexts = {
            'draft': 'مسودة',
            'sent': 'مُرسل',
            'paid': 'مدفوع',
            'overdue': 'متأخر'
        };
        return statusTexts[status] || status;
    }

    // ========================================
    // طباعة قائمة أسعار الطبيب - Print Doctor Price List
    // ========================================

    async printDoctorPriceList(doctorId) {
        try {
            // TODO: تنفيذ طباعة قائمة أسعار الطبيب
            console.log('طباعة قائمة أسعار الطبيب:', doctorId);
            showInfo('طباعة قائمة الأسعار - قيد التطوير');
        } catch (error) {
            console.error('خطأ في طباعة قائمة الأسعار:', error);
            showError('فشل في طباعة قائمة الأسعار');
        }
    }

    // ========================================
    // طباعة تقرير الطبيب - Print Doctor Report
    // ========================================

    async printDoctorReport(doctorId, reportType) {
        try {
            // TODO: تنفيذ طباعة تقرير الطبيب
            console.log('طباعة تقرير الطبيب:', doctorId, reportType);
            showInfo('طباعة التقرير - قيد التطوير');
        } catch (error) {
            console.error('خطأ في طباعة التقرير:', error);
            showError('فشل في طباعة التقرير');
        }
    }
}

// ========================================
// تهيئة مدير الطباعة - Initialize Print Manager
// ========================================

// إنشاء مثيل من مدير الطباعة
const doctorPrintManager = new DoctorPrintManager();

// تصدير المدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { doctorPrintManager, DoctorPrintManager };
}

console.log('✅ تم تحميل مدير طباعة كشوف الحساب بنجاح');
