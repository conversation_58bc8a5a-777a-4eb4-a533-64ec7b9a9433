// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// مدير تقارير الأطباء - Doctor Reports Manager
// ========================================

console.log('📊 تحميل مدير تقارير الأطباء...');

// ========================================
// فئة مدير تقارير الأطباء - Doctor Reports Manager
// ========================================

class DoctorReportsManager {
    constructor() {
        this.doctors = [];
        this.prosthetics = [];
        this.statements = [];
        this.payments = [];
        this.reportData = {};
        this.currentReportType = null;
    }

    // ========================================
    // تهيئة المدير - Initialize Manager
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة مدير التقارير...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            console.log('✅ تم تهيئة مدير التقارير بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير التقارير:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل الأطباء
            this.doctors = await db.findAll('doctors');
            
            // تحميل التركيبات
            this.prosthetics = await db.findAll('prosthetics');
            
            // تحميل كشوف الحساب
            this.statements = await db.findAll('doctor_statements');
            
            // تحميل المدفوعات
            this.payments = await db.findAll('doctor_payments');
            
            console.log(`📊 تم تحميل بيانات ${this.doctors.length} طبيب للتقارير`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // عرض تقارير الأطباء - Render Doctor Reports
    // ========================================

    renderDoctorReports() {
        const content = document.getElementById('reports-content');
        if (!content) return;

        content.innerHTML = `
            <div class="reports-dashboard">
                <!-- أنواع التقارير -->
                <div class="report-types">
                    <h4>أنواع التقارير المتاحة</h4>
                    <div class="report-cards">
                        <div class="report-card" onclick="doctorReportsManager.generateReport('doctor_summary')">
                            <div class="report-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="report-info">
                                <h5>ملخص الأطباء</h5>
                                <p>تقرير شامل لجميع الأطباء وأنشطتهم</p>
                            </div>
                        </div>
                        
                        <div class="report-card" onclick="doctorReportsManager.generateReport('doctor_performance')">
                            <div class="report-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="report-info">
                                <h5>أداء الأطباء</h5>
                                <p>تحليل أداء كل طبيب وإحصائياته</p>
                            </div>
                        </div>
                        
                        <div class="report-card" onclick="doctorReportsManager.generateReport('financial_summary')">
                            <div class="report-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="report-info">
                                <h5>التقرير المالي</h5>
                                <p>الإيرادات والمدفوعات والأرصدة</p>
                            </div>
                        </div>
                        
                        <div class="report-card" onclick="doctorReportsManager.generateReport('monthly_analysis')">
                            <div class="report-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="report-info">
                                <h5>التحليل الشهري</h5>
                                <p>تحليل النشاط الشهري للأطباء</p>
                            </div>
                        </div>
                        
                        <div class="report-card" onclick="doctorReportsManager.generateReport('specialty_analysis')">
                            <div class="report-icon">
                                <i class="fas fa-stethoscope"></i>
                            </div>
                            <div class="report-info">
                                <h5>تحليل التخصصات</h5>
                                <p>إحصائيات حسب تخصص الأطباء</p>
                            </div>
                        </div>
                        
                        <div class="report-card" onclick="doctorReportsManager.generateReport('custom_report')">
                            <div class="report-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="report-info">
                                <h5>تقرير مخصص</h5>
                                <p>إنشاء تقرير حسب معايير محددة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- منطقة عرض التقرير -->
                <div class="report-display" id="report-display">
                    <div class="report-placeholder">
                        <i class="fas fa-chart-bar"></i>
                        <h3>اختر نوع التقرير</h3>
                        <p>انقر على أحد أنواع التقارير أعلاه لعرضه</p>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إنشاء التقرير - Generate Report
    // ========================================

    async generateReport(reportType) {
        this.currentReportType = reportType;
        
        try {
            let reportHTML = '';
            
            switch (reportType) {
                case 'doctor_summary':
                    reportHTML = this.generateDoctorSummaryReport();
                    break;
                case 'doctor_performance':
                    reportHTML = this.generateDoctorPerformanceReport();
                    break;
                case 'financial_summary':
                    reportHTML = this.generateFinancialSummaryReport();
                    break;
                case 'monthly_analysis':
                    reportHTML = this.generateMonthlyAnalysisReport();
                    break;
                case 'specialty_analysis':
                    reportHTML = this.generateSpecialtyAnalysisReport();
                    break;
                case 'custom_report':
                    this.showCustomReportModal();
                    return;
                default:
                    reportHTML = '<p>نوع التقرير غير مدعوم</p>';
            }
            
            this.displayReport(reportHTML, reportType);
            
        } catch (error) {
            console.error('خطأ في إنشاء التقرير:', error);
            showError('فشل في إنشاء التقرير');
        }
    }

    // ========================================
    // تقرير ملخص الأطباء - Doctor Summary Report
    // ========================================

    generateDoctorSummaryReport() {
        const doctorsWithStats = this.doctors.map(doctor => {
            const doctorProsthetics = this.prosthetics.filter(p => p.doctor_id === doctor.id);
            const totalRevenue = doctorProsthetics.reduce((sum, p) => sum + (p.final_price || 0), 0);
            const completedCases = doctorProsthetics.filter(p => p.status === 'completed' || p.status === 'delivered').length;
            
            return {
                ...doctor,
                totalCases: doctorProsthetics.length,
                completedCases: completedCases,
                totalRevenue: totalRevenue,
                averageCase: doctorProsthetics.length > 0 ? totalRevenue / doctorProsthetics.length : 0
            };
        });

        return `
            <div class="report-header">
                <h3>تقرير ملخص الأطباء</h3>
                <div class="report-actions">
                    <button class="btn btn-outline" onclick="doctorReportsManager.exportReport()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="btn btn-outline" onclick="doctorReportsManager.printReport()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
            
            <div class="report-content">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-value">${this.doctors.length}</span>
                        <span class="stat-label">إجمالي الأطباء</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${this.doctors.filter(d => d.isActive).length}</span>
                        <span class="stat-label">أطباء نشطون</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${this.prosthetics.length}</span>
                        <span class="stat-label">إجمالي الحالات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${formatCurrency(doctorsWithStats.reduce((sum, d) => sum + d.totalRevenue, 0))}</span>
                        <span class="stat-label">إجمالي الإيرادات</span>
                    </div>
                </div>
                
                <div class="doctors-table-container">
                    <table class="table report-table">
                        <thead>
                            <tr>
                                <th>الطبيب</th>
                                <th>التخصص</th>
                                <th>العيادة</th>
                                <th>إجمالي الحالات</th>
                                <th>الحالات المكتملة</th>
                                <th>إجمالي الإيرادات</th>
                                <th>متوسط الحالة</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${doctorsWithStats.map(doctor => `
                                <tr>
                                    <td><strong>${doctor.name}</strong></td>
                                    <td>${doctor.specialty || '-'}</td>
                                    <td>${doctor.clinic_name || '-'}</td>
                                    <td>${doctor.totalCases}</td>
                                    <td>${doctor.completedCases}</td>
                                    <td>${formatCurrency(doctor.totalRevenue)}</td>
                                    <td>${formatCurrency(doctor.averageCase)}</td>
                                    <td class="${doctor.current_balance >= 0 ? 'positive' : 'negative'}">
                                        ${formatCurrency(doctor.current_balance || 0)}
                                    </td>
                                    <td>
                                        <span class="status-badge status-${doctor.isActive ? 'active' : 'inactive'}">
                                            ${doctor.isActive ? 'نشط' : 'غير نشط'}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // ========================================
    // تقرير أداء الأطباء - Doctor Performance Report
    // ========================================

    generateDoctorPerformanceReport() {
        const performanceData = this.doctors.map(doctor => {
            const doctorProsthetics = this.prosthetics.filter(p => p.doctor_id === doctor.id);
            const last30Days = doctorProsthetics.filter(p => {
                const prostheticDate = new Date(p.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return prostheticDate >= thirtyDaysAgo;
            });
            
            const completionRate = doctorProsthetics.length > 0 ? 
                (doctorProsthetics.filter(p => p.status === 'completed' || p.status === 'delivered').length / doctorProsthetics.length * 100) : 0;
            
            return {
                ...doctor,
                totalCases: doctorProsthetics.length,
                recentCases: last30Days.length,
                completionRate: completionRate,
                avgDeliveryTime: this.calculateAverageDeliveryTime(doctorProsthetics)
            };
        });

        return `
            <div class="report-header">
                <h3>تقرير أداء الأطباء</h3>
                <div class="report-actions">
                    <button class="btn btn-outline" onclick="doctorReportsManager.exportReport()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="btn btn-outline" onclick="doctorReportsManager.printReport()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
            
            <div class="report-content">
                <div class="performance-table-container">
                    <table class="table report-table">
                        <thead>
                            <tr>
                                <th>الطبيب</th>
                                <th>إجمالي الحالات</th>
                                <th>الحالات الأخيرة (30 يوم)</th>
                                <th>معدل الإنجاز</th>
                                <th>متوسط وقت التسليم</th>
                                <th>التقييم</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${performanceData.map(doctor => `
                                <tr>
                                    <td><strong>${doctor.name}</strong></td>
                                    <td>${doctor.totalCases}</td>
                                    <td>${doctor.recentCases}</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: ${doctor.completionRate}%"></div>
                                            <span class="progress-text">${doctor.completionRate.toFixed(1)}%</span>
                                        </div>
                                    </td>
                                    <td>${doctor.avgDeliveryTime} يوم</td>
                                    <td>
                                        <span class="performance-rating rating-${this.getPerformanceRating(doctor)}">
                                            ${this.getPerformanceRatingText(doctor)}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // ========================================
    // التقرير المالي - Financial Summary Report
    // ========================================

    generateFinancialSummaryReport() {
        const financialData = this.doctors.map(doctor => {
            const doctorStatements = this.statements.filter(s => s.doctor_id === doctor.id);
            const doctorPayments = this.payments.filter(p => p.doctor_id === doctor.id);
            
            const totalBilled = doctorStatements.reduce((sum, s) => sum + (s.net_amount || 0), 0);
            const totalPaid = doctorPayments.reduce((sum, p) => sum + (p.amount || 0), 0);
            const outstanding = totalBilled - totalPaid;
            
            return {
                ...doctor,
                totalBilled: totalBilled,
                totalPaid: totalPaid,
                outstanding: outstanding,
                paymentRate: totalBilled > 0 ? (totalPaid / totalBilled * 100) : 0
            };
        });

        const totals = financialData.reduce((acc, doctor) => ({
            totalBilled: acc.totalBilled + doctor.totalBilled,
            totalPaid: acc.totalPaid + doctor.totalPaid,
            outstanding: acc.outstanding + doctor.outstanding
        }), { totalBilled: 0, totalPaid: 0, outstanding: 0 });

        return `
            <div class="report-header">
                <h3>التقرير المالي للأطباء</h3>
                <div class="report-actions">
                    <button class="btn btn-outline" onclick="doctorReportsManager.exportReport()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="btn btn-outline" onclick="doctorReportsManager.printReport()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
            
            <div class="report-content">
                <div class="financial-summary">
                    <div class="summary-card">
                        <h4>إجمالي المفوتر</h4>
                        <span class="amount">${formatCurrency(totals.totalBilled)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي المحصل</h4>
                        <span class="amount positive">${formatCurrency(totals.totalPaid)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>الرصيد المستحق</h4>
                        <span class="amount ${totals.outstanding > 0 ? 'negative' : 'positive'}">${formatCurrency(totals.outstanding)}</span>
                    </div>
                </div>
                
                <div class="financial-table-container">
                    <table class="table report-table">
                        <thead>
                            <tr>
                                <th>الطبيب</th>
                                <th>إجمالي المفوتر</th>
                                <th>إجمالي المحصل</th>
                                <th>الرصيد المستحق</th>
                                <th>معدل التحصيل</th>
                                <th>الحالة المالية</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${financialData.map(doctor => `
                                <tr>
                                    <td><strong>${doctor.name}</strong></td>
                                    <td>${formatCurrency(doctor.totalBilled)}</td>
                                    <td class="positive">${formatCurrency(doctor.totalPaid)}</td>
                                    <td class="${doctor.outstanding > 0 ? 'negative' : 'positive'}">
                                        ${formatCurrency(doctor.outstanding)}
                                    </td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: ${doctor.paymentRate}%"></div>
                                            <span class="progress-text">${doctor.paymentRate.toFixed(1)}%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="financial-status status-${this.getFinancialStatus(doctor.outstanding)}">
                                            ${this.getFinancialStatusText(doctor.outstanding)}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // ========================================
    // دوال مساعدة - Helper Functions
    // ========================================

    calculateAverageDeliveryTime(prosthetics) {
        const completedProsthetics = prosthetics.filter(p => 
            p.status === 'completed' || p.status === 'delivered'
        );
        
        if (completedProsthetics.length === 0) return 0;
        
        const totalDays = completedProsthetics.reduce((sum, p) => {
            const startDate = new Date(p.createdAt);
            const endDate = new Date(p.delivery_date || p.updatedAt);
            const diffTime = Math.abs(endDate - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return sum + diffDays;
        }, 0);
        
        return Math.round(totalDays / completedProsthetics.length);
    }

    getPerformanceRating(doctor) {
        if (doctor.completionRate >= 90 && doctor.recentCases >= 10) return 'excellent';
        if (doctor.completionRate >= 75 && doctor.recentCases >= 5) return 'good';
        if (doctor.completionRate >= 50) return 'average';
        return 'poor';
    }

    getPerformanceRatingText(doctor) {
        const rating = this.getPerformanceRating(doctor);
        const ratings = {
            'excellent': 'ممتاز',
            'good': 'جيد',
            'average': 'متوسط',
            'poor': 'ضعيف'
        };
        return ratings[rating];
    }

    getFinancialStatus(outstanding) {
        if (outstanding <= 0) return 'good';
        if (outstanding <= 5000) return 'warning';
        return 'danger';
    }

    getFinancialStatusText(outstanding) {
        if (outstanding <= 0) return 'مسدد';
        if (outstanding <= 5000) return 'متابعة';
        return 'متأخر';
    }

    // ========================================
    // عرض التقرير - Display Report
    // ========================================

    displayReport(reportHTML, reportType) {
        const reportDisplay = document.getElementById('report-display');
        if (reportDisplay) {
            reportDisplay.innerHTML = reportHTML;
        }
    }

    // ========================================
    // وظائف مؤقتة - Placeholder Functions
    // ========================================

    generateMonthlyAnalysisReport() {
        return '<div class="report-placeholder"><h3>التحليل الشهري</h3><p>قيد التطوير</p></div>';
    }

    generateSpecialtyAnalysisReport() {
        return '<div class="report-placeholder"><h3>تحليل التخصصات</h3><p>قيد التطوير</p></div>';
    }

    showCustomReportModal() {
        showInfo('التقرير المخصص - قيد التطوير');
    }

    exportReport() {
        showInfo('تصدير التقرير - قيد التطوير');
    }

    printReport() {
        showInfo('طباعة التقرير - قيد التطوير');
    }
}

// ========================================
// تهيئة مدير التقارير - Initialize Reports Manager
// ========================================

// إنشاء مثيل من مدير التقارير
const doctorReportsManager = new DoctorReportsManager();

// دالة تهيئة المدير
async function initializeDoctorReportsManager() {
    try {
        const success = await doctorReportsManager.init();
        if (success) {
            console.log('✅ تم تهيئة مدير التقارير بنجاح');
        } else {
            console.error('❌ فشل في تهيئة مدير التقارير');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة مدير التقارير:', error);
        return false;
    }
}

// تصدير المدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { doctorReportsManager, DoctorReportsManager };
}

console.log('✅ تم تحميل مدير تقارير الأطباء بنجاح');
