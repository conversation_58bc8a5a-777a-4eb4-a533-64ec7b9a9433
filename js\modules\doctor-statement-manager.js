// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// مدير كشوف حساب الأطباء - Doctor Statement Manager
// ========================================

console.log('📋 تحميل مدير كشوف حساب الأطباء...');

// ========================================
// فئة مدير كشوف حساب الأطباء - Doctor Statement Manager
// ========================================

class DoctorStatementManager {
    constructor() {
        this.statements = [];
        this.statementItems = [];
        this.doctors = [];
        this.prosthetics = [];
        this.currentStatement = null;
        this.currentDoctorId = null;
    }

    // ========================================
    // تهيئة المدير - Initialize Manager
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة مدير كشوف الحساب...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            console.log('✅ تم تهيئة مدير كشوف الحساب بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير كشوف الحساب:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل كشوف الحساب
            this.statements = await db.findAll('doctor_statements');
            
            // تحميل تفاصيل كشوف الحساب
            this.statementItems = await db.findAll('doctor_statement_items');
            
            // تحميل الأطباء
            this.doctors = await db.findAll('doctors');
            
            // تحميل التركيبات
            this.prosthetics = await db.findAll('prosthetics');
            
            console.log(`📊 تم تحميل ${this.statements.length} كشف حساب`);
            console.log(`📋 تم تحميل ${this.statementItems.length} عنصر كشف حساب`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // إنشاء كشف حساب جديد - Generate New Statement
    // ========================================

    async generateNewStatement(doctorId, periodFrom, periodTo) {
        try {
            const doctor = this.doctors.find(d => d.id === doctorId);
            if (!doctor) {
                throw new Error('لم يتم العثور على الطبيب');
            }

            // البحث عن التركيبات في الفترة المحددة
            const periodProsthetics = this.prosthetics.filter(prosthetic => {
                const prostheticDate = new Date(prosthetic.createdAt);
                const fromDate = new Date(periodFrom);
                const toDate = new Date(periodTo);
                
                return prosthetic.doctor_id === doctorId &&
                       prostheticDate >= fromDate &&
                       prostheticDate <= toDate &&
                       (prosthetic.status === 'completed' || prosthetic.status === 'delivered');
            });

            if (periodProsthetics.length === 0) {
                throw new Error('لا توجد تركيبات مكتملة في الفترة المحددة');
            }

            // حساب الإجماليات
            const totalAmount = periodProsthetics.reduce((sum, p) => sum + (p.total_price || 0), 0);
            const totalDiscount = periodProsthetics.reduce((sum, p) => sum + (p.discount_amount || 0), 0);
            const netAmount = periodProsthetics.reduce((sum, p) => sum + (p.final_price || 0), 0);

            // إنشاء رقم كشف الحساب
            const statementNumber = await this.generateStatementNumber();

            // إنشاء كشف الحساب
            const statementData = {
                doctor_id: doctorId,
                statement_number: statementNumber,
                statement_date: new Date().toISOString().split('T')[0],
                period_from: periodFrom,
                period_to: periodTo,
                total_cases: periodProsthetics.length,
                total_amount: totalAmount,
                total_discount: totalDiscount,
                net_amount: netAmount,
                previous_balance: doctor.current_balance || 0,
                payments_received: 0,
                current_balance: (doctor.current_balance || 0) + netAmount,
                status: 'draft',
                due_date: this.calculateDueDate(doctor.payment_terms),
                generated_by: getCurrentUser()?.name || 'النظام'
            };

            // حفظ كشف الحساب
            const statement = await db.insert('doctor_statements', statementData);
            
            if (statement) {
                // إضافة تفاصيل كشف الحساب
                await this.addStatementItems(statement.id, periodProsthetics);
                
                // تحديث رصيد الطبيب
                await this.updateDoctorBalance(doctorId, netAmount);
                
                this.currentStatement = statement;
                return statement;
            }

            throw new Error('فشل في إنشاء كشف الحساب');

        } catch (error) {
            console.error('خطأ في إنشاء كشف الحساب:', error);
            throw error;
        }
    }

    // ========================================
    // إضافة تفاصيل كشف الحساب - Add Statement Items
    // ========================================

    async addStatementItems(statementId, prosthetics) {
        try {
            for (const prosthetic of prosthetics) {
                const itemData = {
                    statement_id: statementId,
                    prosthetic_id: prosthetic.id,
                    item_date: prosthetic.createdAt.split('T')[0],
                    patient_name: prosthetic.patient_name,
                    prosthetic_type: prosthetic.prosthetic_type,
                    quantity: prosthetic.quantity || 1,
                    unit_price: prosthetic.unit_price || 0,
                    total_price: prosthetic.total_price || 0,
                    discount_percentage: prosthetic.discount_percentage || 0,
                    discount_amount: prosthetic.discount_amount || 0,
                    net_amount: prosthetic.final_price || 0,
                    notes: prosthetic.notes || ''
                };

                await db.insert('doctor_statement_items', itemData);
            }
        } catch (error) {
            console.error('خطأ في إضافة تفاصيل كشف الحساب:', error);
            throw error;
        }
    }

    // ========================================
    // تحديث رصيد الطبيب - Update Doctor Balance
    // ========================================

    async updateDoctorBalance(doctorId, amount) {
        try {
            const doctor = this.doctors.find(d => d.id === doctorId);
            if (doctor) {
                const newBalance = (doctor.current_balance || 0) + amount;
                await db.update('doctors', doctorId, {
                    current_balance: newBalance,
                    last_statement_date: new Date().toISOString().split('T')[0]
                });
                
                // تحديث البيانات المحلية
                doctor.current_balance = newBalance;
                doctor.last_statement_date = new Date().toISOString().split('T')[0];
            }
        } catch (error) {
            console.error('خطأ في تحديث رصيد الطبيب:', error);
            throw error;
        }
    }

    // ========================================
    // إنشاء رقم كشف الحساب - Generate Statement Number
    // ========================================

    async generateStatementNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        
        // البحث عن آخر رقم في الشهر الحالي
        const existingStatements = this.statements.filter(s => 
            s.statement_number.startsWith(`ST-${year}${month}`)
        );
        
        const nextNumber = existingStatements.length + 1;
        return `ST-${year}${month}-${String(nextNumber).padStart(4, '0')}`;
    }

    // ========================================
    // حساب تاريخ الاستحقاق - Calculate Due Date
    // ========================================

    calculateDueDate(paymentTerms) {
        const today = new Date();
        let dueDate = new Date(today);
        
        switch (paymentTerms) {
            case 'weekly':
                dueDate.setDate(today.getDate() + 7);
                break;
            case 'monthly':
                dueDate.setMonth(today.getMonth() + 1);
                break;
            case 'per_case':
                dueDate.setDate(today.getDate() + 3);
                break;
            default:
                dueDate.setMonth(today.getMonth() + 1);
        }
        
        return dueDate.toISOString().split('T')[0];
    }

    // ========================================
    // عرض نافذة إنشاء كشف حساب - Show Generate Statement Modal
    // ========================================

    showGenerateStatementModal(doctorId = null) {
        const modalHTML = `
            <div id="generate-statement-modal" class="modal">
                <div class="modal-overlay"></div>
                <div class="modal-content large-modal">
                    <div class="modal-header">
                        <h2>إنشاء كشف حساب جديد</h2>
                        <button class="modal-close" onclick="doctorStatementManager.closeGenerateStatementModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="generate-statement-form" class="modal-body">
                        <div class="form-section">
                            <h3 class="section-title">معلومات كشف الحساب</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="statement-doctor" class="form-label">الطبيب *</label>
                                    <select id="statement-doctor" class="form-control" required>
                                        <option value="">اختر الطبيب</option>
                                        ${this.doctors.map(doctor => 
                                            `<option value="${doctor.id}" ${doctorId === doctor.id ? 'selected' : ''}>
                                                ${doctor.name} - ${doctor.specialty}
                                            </option>`
                                        ).join('')}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="period-from" class="form-label">من تاريخ *</label>
                                    <input type="date" id="period-from" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="period-to" class="form-label">إلى تاريخ *</label>
                                    <input type="date" id="period-to" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">معاينة البيانات</h3>
                            <div id="statement-preview" class="statement-preview">
                                <p class="text-center text-secondary">اختر الطبيب والفترة لمعاينة البيانات</p>
                            </div>
                        </div>
                    </form>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="doctorStatementManager.closeGenerateStatementModal()">
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-info" onclick="doctorStatementManager.previewStatement()">
                            <i class="fas fa-eye"></i>
                            معاينة
                        </button>
                        <button type="submit" form="generate-statement-form" class="btn btn-primary">
                            <i class="fas fa-file-invoice"></i>
                            إنشاء كشف الحساب
                        </button>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // عرض النافذة
        const modal = document.getElementById('generate-statement-modal');
        modal.classList.remove('hidden');

        // إعداد الأحداث
        this.setupGenerateStatementEvents();

        // تعيين التواريخ الافتراضية
        this.setDefaultDates();
    }

    // ========================================
    // إعداد أحداث نافذة إنشاء كشف الحساب - Setup Generate Statement Events
    // ========================================

    setupGenerateStatementEvents() {
        const form = document.getElementById('generate-statement-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleGenerateStatement();
            });
        }

        // أحداث تغيير البيانات للمعاينة
        const doctorSelect = document.getElementById('statement-doctor');
        const periodFrom = document.getElementById('period-from');
        const periodTo = document.getElementById('period-to');

        [doctorSelect, periodFrom, periodTo].forEach(element => {
            if (element) {
                element.addEventListener('change', () => {
                    this.updateStatementPreview();
                });
            }
        });
    }

    // ========================================
    // تعيين التواريخ الافتراضية - Set Default Dates
    // ========================================

    setDefaultDates() {
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        const periodFromInput = document.getElementById('period-from');
        const periodToInput = document.getElementById('period-to');

        if (periodFromInput) {
            periodFromInput.value = firstDayOfMonth.toISOString().split('T')[0];
        }

        if (periodToInput) {
            periodToInput.value = lastDayOfMonth.toISOString().split('T')[0];
        }
    }

    // ========================================
    // تحديث معاينة كشف الحساب - Update Statement Preview
    // ========================================

    updateStatementPreview() {
        const doctorId = parseInt(document.getElementById('statement-doctor').value);
        const periodFrom = document.getElementById('period-from').value;
        const periodTo = document.getElementById('period-to').value;
        const previewDiv = document.getElementById('statement-preview');

        if (!doctorId || !periodFrom || !periodTo) {
            previewDiv.innerHTML = '<p class="text-center text-secondary">اختر الطبيب والفترة لمعاينة البيانات</p>';
            return;
        }

        // البحث عن التركيبات في الفترة
        const periodProsthetics = this.prosthetics.filter(prosthetic => {
            const prostheticDate = new Date(prosthetic.createdAt);
            const fromDate = new Date(periodFrom);
            const toDate = new Date(periodTo);
            
            return prosthetic.doctor_id === doctorId &&
                   prostheticDate >= fromDate &&
                   prostheticDate <= toDate &&
                   (prosthetic.status === 'completed' || prosthetic.status === 'delivered');
        });

        if (periodProsthetics.length === 0) {
            previewDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    لا توجد تركيبات مكتملة في الفترة المحددة
                </div>
            `;
            return;
        }

        // حساب الإجماليات
        const totalAmount = periodProsthetics.reduce((sum, p) => sum + (p.total_price || 0), 0);
        const totalDiscount = periodProsthetics.reduce((sum, p) => sum + (p.discount_amount || 0), 0);
        const netAmount = periodProsthetics.reduce((sum, p) => sum + (p.final_price || 0), 0);

        previewDiv.innerHTML = `
            <div class="preview-summary">
                <div class="summary-item">
                    <span class="summary-label">عدد الحالات:</span>
                    <span class="summary-value">${periodProsthetics.length}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي المبلغ:</span>
                    <span class="summary-value">${formatCurrency(totalAmount)}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">إجمالي الخصم:</span>
                    <span class="summary-value">${formatCurrency(totalDiscount)}</span>
                </div>
                <div class="summary-item total">
                    <span class="summary-label">صافي المبلغ:</span>
                    <span class="summary-value">${formatCurrency(netAmount)}</span>
                </div>
            </div>
        `;
    }

    // ========================================
    // معالجة إنشاء كشف الحساب - Handle Generate Statement
    // ========================================

    async handleGenerateStatement() {
        try {
            const doctorId = parseInt(document.getElementById('statement-doctor').value);
            const periodFrom = document.getElementById('period-from').value;
            const periodTo = document.getElementById('period-to').value;

            if (!doctorId || !periodFrom || !periodTo) {
                showError('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إنشاء كشف الحساب
            const statement = await this.generateNewStatement(doctorId, periodFrom, periodTo);
            
            if (statement) {
                showSuccess('تم إنشاء كشف الحساب بنجاح');
                this.closeGenerateStatementModal();
                
                // تحديث البيانات
                await this.loadInitialData();
                
                // عرض كشف الحساب الجديد
                this.viewStatement(statement.id);
            }

        } catch (error) {
            console.error('خطأ في إنشاء كشف الحساب:', error);
            showError(error.message || 'فشل في إنشاء كشف الحساب');
        }
    }

    // ========================================
    // معاينة كشف الحساب - Preview Statement
    // ========================================

    previewStatement() {
        this.updateStatementPreview();
        showInfo('تم تحديث المعاينة');
    }

    // ========================================
    // إغلاق نافذة إنشاء كشف الحساب - Close Generate Statement Modal
    // ========================================

    closeGenerateStatementModal() {
        const modal = document.getElementById('generate-statement-modal');
        if (modal) {
            modal.remove();
        }
    }

    // ========================================
    // عرض كشف الحساب - View Statement
    // ========================================

    viewStatement(statementId) {
        // TODO: تنفيذ عرض كشف الحساب
        console.log('عرض كشف الحساب:', statementId);
        showInfo('عرض كشف الحساب - قيد التطوير');
    }

    // ========================================
    // طباعة كشف الحساب - Print Statement
    // ========================================

    async printStatement(statementId) {
        try {
            if (typeof doctorPrintManager !== 'undefined') {
                await doctorPrintManager.printStatement(statementId);
            } else {
                showError('مدير الطباعة غير متاح');
            }
        } catch (error) {
            console.error('خطأ في طباعة كشف الحساب:', error);
            showError('فشل في طباعة كشف الحساب');
        }
    }
}

// ========================================
// تهيئة مدير كشوف الحساب - Initialize Statement Manager
// ========================================

// إنشاء مثيل من مدير كشوف الحساب
const doctorStatementManager = new DoctorStatementManager();

// دالة تهيئة المدير
async function initializeDoctorStatementManager() {
    try {
        const success = await doctorStatementManager.init();
        if (success) {
            console.log('✅ تم تهيئة مدير كشوف الحساب بنجاح');
        } else {
            console.error('❌ فشل في تهيئة مدير كشوف الحساب');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة مدير كشوف الحساب:', error);
        return false;
    }
}

// تصدير المدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { doctorStatementManager, DoctorStatementManager };
}

console.log('✅ تم تحميل مدير كشوف حساب الأطباء بنجاح');
