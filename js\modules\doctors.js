// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// وحدة إدارة الأطباء - Doctors Management Module
// ========================================

console.log('👨‍⚕️ تحميل وحدة إدارة الأطباء...');

// ========================================
// فئة إدارة الأطباء - Doctors Manager
// ========================================

class DoctorsManager {
    constructor() {
        this.doctors = [];
        this.doctorPriceLists = [];
        this.doctorStatements = [];
        this.doctorPayments = [];
        this.prostheticTypes = [];
        this.currentDoctor = null;
        this.currentView = 'list'; // list, profile, prices, statements, reports
    }

    // ========================================
    // تهيئة الوحدة - Initialize Module
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة وحدة الأطباء...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            console.log('✅ تم تهيئة وحدة الأطباء بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة وحدة الأطباء:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل الأطباء
            this.doctors = await db.findAll('doctors');
            
            // تحميل قوائم الأسعار
            this.doctorPriceLists = await db.findAll('doctor_price_lists');
            
            // تحميل كشوف الحساب
            this.doctorStatements = await db.findAll('doctor_statements');
            
            // تحميل المدفوعات
            this.doctorPayments = await db.findAll('doctor_payments');
            
            // تحميل أنواع التركيبات
            this.prostheticTypes = await db.findAll('prosthetic_types');
            
            console.log(`📊 تم تحميل ${this.doctors.length} طبيب`);
            console.log(`💰 تم تحميل ${this.doctorPriceLists.length} قائمة أسعار`);
            console.log(`📋 تم تحميل ${this.doctorStatements.length} كشف حساب`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // عرض وحدة الأطباء - Render Doctors Module
    // ========================================

    async render() {
        try {
            const contentArea = document.getElementById('content-area');
            if (!contentArea) return;

            // إنشاء HTML للوحدة
            contentArea.innerHTML = this.getDoctorsHTML();
            
            // تحميل البيانات وعرضها
            await this.loadInitialData();
            
            // عرض قائمة الأطباء
            this.renderDoctorsList();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
        } catch (error) {
            console.error('خطأ في عرض وحدة الأطباء:', error);
            showError('فشل في تحميل وحدة الأطباء');
        }
    }

    // ========================================
    // إنشاء HTML للوحدة - Get Doctors HTML
    // ========================================

    getDoctorsHTML() {
        return `
            <div class="doctors-container">
                <!-- شريط الأدوات العلوي -->
                <div class="doctors-toolbar">
                    <div class="toolbar-left">
                        <h1 class="page-title">
                            <i class="fas fa-user-md"></i>
                            إدارة الأطباء
                        </h1>
                        <p class="page-subtitle">إدارة شاملة لبيانات الأطباء وقوائم الأسعار وكشوف الحساب</p>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="doctorsManager.showNewDoctorModal()">
                            <i class="fas fa-plus"></i>
                            طبيب جديد
                        </button>
                        <button class="btn btn-outline" onclick="doctorsManager.refreshData()">
                            <i class="fas fa-sync"></i>
                            تحديث
                        </button>
                        <button class="btn btn-outline" onclick="doctorsManager.exportDoctors()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="doctors-stats">
                    <div class="stat-card stat-card-primary">
                        <div class="stat-icon">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-doctors" class="stat-value">0</h3>
                            <p class="stat-title">إجمالي الأطباء</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-success">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-revenue" class="stat-value">0</h3>
                            <p class="stat-title">إجمالي الإيرادات</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-warning">
                        <div class="stat-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pending-statements" class="stat-value">0</h3>
                            <p class="stat-title">كشوف معلقة</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-info">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="outstanding-balance" class="stat-value">0</h3>
                            <p class="stat-title">الرصيد المستحق</p>
                        </div>
                    </div>
                </div>

                <!-- التنقل بين الأقسام -->
                <div class="doctors-navigation">
                    <div class="nav-tabs">
                        <button class="nav-tab active" data-view="list" onclick="doctorsManager.switchView('list')">
                            <i class="fas fa-list"></i>
                            قائمة الأطباء
                        </button>
                        <button class="nav-tab" data-view="statements" onclick="doctorsManager.switchView('statements')">
                            <i class="fas fa-file-invoice"></i>
                            كشوف الحساب
                        </button>
                        <button class="nav-tab" data-view="payments" onclick="doctorsManager.switchView('payments')">
                            <i class="fas fa-money-bill"></i>
                            المدفوعات
                        </button>
                        <button class="nav-tab" data-view="reports" onclick="doctorsManager.switchView('reports')">
                            <i class="fas fa-chart-bar"></i>
                            التقارير
                        </button>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="doctors-content">
                    
                    <!-- قسم قائمة الأطباء -->
                    <div id="doctors-list-section" class="content-section active">
                        
                        <!-- شريط البحث والتصفية -->
                        <div class="doctors-filters">
                            <div class="filter-group">
                                <input type="search" id="doctors-search" class="form-control" placeholder="البحث في الأطباء...">
                            </div>
                            <div class="filter-group">
                                <select id="specialty-filter" class="form-control">
                                    <option value="">جميع التخصصات</option>
                                    <option value="طب الأسنان العام">طب الأسنان العام</option>
                                    <option value="تقويم الأسنان">تقويم الأسنان</option>
                                    <option value="جراحة الفم والأسنان">جراحة الفم والأسنان</option>
                                    <option value="طب أسنان الأطفال">طب أسنان الأطفال</option>
                                    <option value="تركيبات الأسنان">تركيبات الأسنان</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <select id="status-filter" class="form-control">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>

                        <!-- قائمة الأطباء -->
                        <div class="doctors-list-container">
                            <div class="table-container">
                                <table class="table doctors-table">
                                    <thead>
                                        <tr>
                                            <th>الطبيب</th>
                                            <th>التخصص</th>
                                            <th>العيادة</th>
                                            <th>الهاتف</th>
                                            <th>نسبة الخصم</th>
                                            <th>الرصيد الحالي</th>
                                            <th>إجمالي الحالات</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="doctors-table-body">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>

                    <!-- قسم كشوف الحساب -->
                    <div id="statements-section" class="content-section">
                        <div class="statements-header">
                            <h3>كشوف حساب الأطباء</h3>
                            <button class="btn btn-primary" onclick="doctorsManager.generateNewStatement()">
                                <i class="fas fa-plus"></i>
                                إنشاء كشف حساب جديد
                            </button>
                        </div>
                        <div id="statements-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>

                    <!-- قسم المدفوعات -->
                    <div id="payments-section" class="content-section">
                        <div class="payments-header">
                            <h3>مدفوعات الأطباء</h3>
                            <button class="btn btn-primary" onclick="doctorsManager.recordNewPayment()">
                                <i class="fas fa-plus"></i>
                                تسجيل دفعة جديدة
                            </button>
                        </div>
                        <div id="payments-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>

                    <!-- قسم التقارير -->
                    <div id="reports-section" class="content-section">
                        <div class="reports-header">
                            <h3>تقارير الأطباء</h3>
                        </div>
                        <div id="reports-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>

                </div>
            </div>

            <!-- نافذة طبيب جديد -->
            <div id="new-doctor-modal" class="modal hidden">
                <div class="modal-overlay"></div>
                <div class="modal-content large-modal">
                    <div class="modal-header">
                        <h2>إضافة طبيب جديد</h2>
                        <button class="modal-close" onclick="doctorsManager.closeNewDoctorModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="new-doctor-form" class="modal-body">
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h3 class="section-title">المعلومات الأساسية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="doctor-name" class="form-label">اسم الطبيب *</label>
                                    <input type="text" id="doctor-name" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="doctor-specialty" class="form-label">التخصص *</label>
                                    <select id="doctor-specialty" class="form-control" required>
                                        <option value="">اختر التخصص</option>
                                        <option value="طب الأسنان العام">طب الأسنان العام</option>
                                        <option value="تقويم الأسنان">تقويم الأسنان</option>
                                        <option value="جراحة الفم والأسنان">جراحة الفم والأسنان</option>
                                        <option value="طب أسنان الأطفال">طب أسنان الأطفال</option>
                                        <option value="تركيبات الأسنان">تركيبات الأسنان</option>
                                        <option value="علاج الجذور">علاج الجذور</option>
                                        <option value="أمراض اللثة">أمراض اللثة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="doctor-phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" id="doctor-phone" class="form-control" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="doctor-email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" id="doctor-email" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="clinic-name" class="form-label">اسم العيادة</label>
                                    <input type="text" id="clinic-name" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="license-number" class="form-label">رقم الترخيص</label>
                                    <input type="text" id="license-number" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- معلومات مالية -->
                        <div class="form-section">
                            <h3 class="section-title">المعلومات المالية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="discount-percentage" class="form-label">نسبة الخصم (%)</label>
                                    <input type="number" id="discount-percentage" class="form-control" min="0" max="100" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="commission-percentage" class="form-label">نسبة العمولة (%)</label>
                                    <input type="number" id="commission-percentage" class="form-control" min="0" max="100" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="credit-limit" class="form-label">الحد الائتماني</label>
                                    <input type="number" id="credit-limit" class="form-control" min="0" step="0.01">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="payment-terms" class="form-label">شروط الدفع</label>
                                    <select id="payment-terms" class="form-control">
                                        <option value="monthly">شهرياً</option>
                                        <option value="weekly">أسبوعياً</option>
                                        <option value="per_case">لكل حالة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="payment-method" class="form-label">طريقة الدفع المفضلة</label>
                                    <select id="payment-method" class="form-control">
                                        <option value="bank_transfer">تحويل بنكي</option>
                                        <option value="cash">نقداً</option>
                                        <option value="check">شيك</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="bank-account" class="form-label">رقم الحساب البنكي</label>
                                    <input type="text" id="bank-account" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h3 class="section-title">معلومات إضافية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="doctor-address" class="form-label">العنوان</label>
                                    <textarea id="doctor-address" class="form-control" rows="2"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="tax-number" class="form-label">الرقم الضريبي</label>
                                    <input type="text" id="tax-number" class="form-control">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="doctor-notes" class="form-label">ملاحظات</label>
                                    <textarea id="doctor-notes" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="doctorsManager.closeNewDoctorModal()">
                            إلغاء
                        </button>
                        <button type="submit" form="new-doctor-form" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الطبيب
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إعداد الأحداث - Setup Event Listeners
    // ========================================

    setupEventListeners() {
        // نموذج طبيب جديد
        const newDoctorForm = document.getElementById('new-doctor-form');
        if (newDoctorForm) {
            newDoctorForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveDoctor();
            });
        }

        // البحث والتصفية
        const searchInput = document.getElementById('doctors-search');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(() => {
                this.filterDoctors();
            }, 300));
        }

        const specialtyFilter = document.getElementById('specialty-filter');
        if (specialtyFilter) {
            specialtyFilter.addEventListener('change', () => {
                this.filterDoctors();
            });
        }

        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filterDoctors();
            });
        }
    }

    // ========================================
    // عرض قائمة الأطباء - Render Doctors List
    // ========================================

    renderDoctorsList() {
        const tableBody = document.getElementById('doctors-table-body');
        if (!tableBody) return;

        if (this.doctors.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-user-md"></i>
                            <h3>لا يوجد أطباء مسجلين</h3>
                            <p>ابدأ بإضافة أول طبيب</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.doctors.map(doctor => {
            const statusClass = doctor.isActive ? 'success' : 'error';
            const statusText = doctor.isActive ? 'نشط' : 'غير نشط';

            return `
                <tr data-doctor-id="${doctor.id}">
                    <td>
                        <div class="doctor-info">
                            <strong>${doctor.name}</strong>
                            ${doctor.email ? `<br><small>${doctor.email}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="specialty">${doctor.specialty || '-'}</span>
                    </td>
                    <td>
                        <span class="clinic-name">${doctor.clinic_name || '-'}</span>
                    </td>
                    <td>
                        <span class="phone">${doctor.phone || '-'}</span>
                    </td>
                    <td>
                        <span class="discount">${doctor.discount_percentage || 0}%</span>
                    </td>
                    <td>
                        <span class="balance ${doctor.current_balance >= 0 ? 'positive' : 'negative'}">
                            ${formatCurrency(doctor.current_balance || 0)}
                        </span>
                    </td>
                    <td>
                        <span class="cases-count">${doctor.total_cases || 0}</span>
                    </td>
                    <td>
                        <span class="status-badge status-${statusClass}">${statusText}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline" onclick="doctorsManager.viewDoctorProfile(${doctor.id})" title="عرض الملف">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="doctorsManager.editDoctor(${doctor.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="doctorsManager.managePrices(${doctor.id})" title="إدارة الأسعار">
                                <i class="fas fa-tags"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="doctorsManager.generateStatement(${doctor.id})" title="كشف حساب">
                                <i class="fas fa-file-invoice"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="doctorsManager.deleteDoctor(${doctor.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // تحديث الإحصائيات
        this.updateStatistics();
    }

    // ========================================
    // تحديث الإحصائيات - Update Statistics
    // ========================================

    updateStatistics() {
        const totalElement = document.getElementById('total-doctors');
        const revenueElement = document.getElementById('total-revenue');
        const pendingElement = document.getElementById('pending-statements');
        const balanceElement = document.getElementById('outstanding-balance');

        if (totalElement) totalElement.textContent = this.doctors.length;

        const totalRevenue = this.doctors.reduce((sum, doctor) => sum + (doctor.total_revenue || 0), 0);
        if (revenueElement) revenueElement.textContent = formatCurrency(totalRevenue);

        const pendingStatements = this.doctorStatements.filter(s => s.status === 'draft' || s.status === 'sent').length;
        if (pendingElement) pendingElement.textContent = pendingStatements;

        const outstandingBalance = this.doctors.reduce((sum, doctor) => sum + (doctor.current_balance || 0), 0);
        if (balanceElement) balanceElement.textContent = formatCurrency(outstandingBalance);
    }

    // ========================================
    // تبديل العرض - Switch View
    // ========================================

    switchView(view) {
        this.currentView = view;

        // تحديث التبويبات
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // تحديث المحتوى
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        switch (view) {
            case 'list':
                document.getElementById('doctors-list-section').classList.add('active');
                this.renderDoctorsList();
                break;
            case 'statements':
                document.getElementById('statements-section').classList.add('active');
                this.renderStatements();
                break;
            case 'payments':
                document.getElementById('payments-section').classList.add('active');
                this.renderPayments();
                break;
            case 'reports':
                document.getElementById('reports-section').classList.add('active');
                this.renderReports();
                break;
        }
    }

    // ========================================
    // حفظ طبيب جديد - Save New Doctor
    // ========================================

    async saveDoctor() {
        try {
            // التحقق من صحة البيانات
            const validationResult = this.validateDoctorData();
            if (!validationResult.isValid) {
                showError(validationResult.message);
                return;
            }

            // جمع البيانات
            const doctorData = this.collectDoctorData();

            // حفظ في قاعدة البيانات
            const result = await db.insert('doctors', doctorData);

            if (result) {
                showSuccess('تم حفظ الطبيب بنجاح');
                this.closeNewDoctorModal();
                await this.refreshData();
            } else {
                showError('فشل في حفظ الطبيب');
            }

        } catch (error) {
            console.error('خطأ في حفظ الطبيب:', error);
            showError('حدث خطأ أثناء حفظ الطبيب');
        }
    }

    validateDoctorData() {
        const name = document.getElementById('doctor-name').value.trim();
        const specialty = document.getElementById('doctor-specialty').value;
        const phone = document.getElementById('doctor-phone').value.trim();

        if (!name) {
            return { isValid: false, message: 'يرجى إدخال اسم الطبيب' };
        }

        if (!specialty) {
            return { isValid: false, message: 'يرجى اختيار التخصص' };
        }

        if (!phone) {
            return { isValid: false, message: 'يرجى إدخال رقم الهاتف' };
        }

        return { isValid: true };
    }

    collectDoctorData() {
        return {
            name: document.getElementById('doctor-name').value.trim(),
            specialty: document.getElementById('doctor-specialty').value,
            phone: document.getElementById('doctor-phone').value.trim(),
            email: document.getElementById('doctor-email').value.trim(),
            address: document.getElementById('doctor-address').value.trim(),
            clinic_name: document.getElementById('clinic-name').value.trim(),
            license_number: document.getElementById('license-number').value.trim(),
            tax_number: document.getElementById('tax-number').value.trim(),
            discount_percentage: parseFloat(document.getElementById('discount-percentage').value) || 0,
            commission_percentage: parseFloat(document.getElementById('commission-percentage').value) || 0,
            payment_terms: document.getElementById('payment-terms').value,
            credit_limit: parseFloat(document.getElementById('credit-limit').value) || 0,
            preferred_payment_method: document.getElementById('payment-method').value,
            bank_account: document.getElementById('bank-account').value.trim(),
            notes: document.getElementById('doctor-notes').value.trim(),
            isActive: true
        };
    }

    // ========================================
    // إدارة النوافذ المنبثقة - Modal Management
    // ========================================

    showNewDoctorModal() {
        const modal = document.getElementById('new-doctor-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.resetNewDoctorForm();
        }
    }

    closeNewDoctorModal() {
        const modal = document.getElementById('new-doctor-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetNewDoctorForm();
        }
    }

    resetNewDoctorForm() {
        const form = document.getElementById('new-doctor-form');
        if (form) {
            form.reset();
        }
    }

    // ========================================
    // تصفية الأطباء - Filter Doctors
    // ========================================

    filterDoctors() {
        const searchTerm = document.getElementById('doctors-search')?.value.toLowerCase() || '';
        const specialtyFilter = document.getElementById('specialty-filter')?.value || '';
        const statusFilter = document.getElementById('status-filter')?.value || '';

        let filteredDoctors = [...this.doctors];

        // تطبيق البحث النصي
        if (searchTerm) {
            filteredDoctors = filteredDoctors.filter(doctor =>
                doctor.name.toLowerCase().includes(searchTerm) ||
                (doctor.clinic_name && doctor.clinic_name.toLowerCase().includes(searchTerm)) ||
                (doctor.phone && doctor.phone.includes(searchTerm))
            );
        }

        // تطبيق تصفية التخصص
        if (specialtyFilter) {
            filteredDoctors = filteredDoctors.filter(doctor =>
                doctor.specialty === specialtyFilter
            );
        }

        // تطبيق تصفية الحالة
        if (statusFilter) {
            const isActive = statusFilter === 'active';
            filteredDoctors = filteredDoctors.filter(doctor =>
                doctor.isActive === isActive
            );
        }

        // تحديث العرض
        const originalDoctors = this.doctors;
        this.doctors = filteredDoctors;
        this.renderDoctorsList();
        this.doctors = originalDoctors;
    }

    // ========================================
    // إجراءات الأطباء - Doctor Actions
    // ========================================

    async refreshData() {
        await this.loadInitialData();
        this.renderDoctorsList();
    }

    viewDoctorProfile(id) {
        // TODO: تنفيذ عرض ملف الطبيب
        console.log('عرض ملف الطبيب:', id);
    }

    editDoctor(id) {
        // TODO: تنفيذ تعديل الطبيب
        console.log('تعديل الطبيب:', id);
    }

    async managePrices(id) {
        try {
            // التأكد من تهيئة مدير قوائم الأسعار
            if (typeof doctorPriceManager !== 'undefined') {
                await doctorPriceManager.showPriceManagementModal(id);
            } else {
                showError('مدير قوائم الأسعار غير متاح');
            }
        } catch (error) {
            console.error('خطأ في إدارة أسعار الطبيب:', error);
            showError('فشل في فتح إدارة الأسعار');
        }
    }

    async generateStatement(id) {
        try {
            // التأكد من تهيئة مدير كشوف الحساب
            if (typeof doctorStatementManager !== 'undefined') {
                doctorStatementManager.showGenerateStatementModal(id);
            } else {
                showError('مدير كشوف الحساب غير متاح');
            }
        } catch (error) {
            console.error('خطأ في إنشاء كشف حساب:', error);
            showError('فشل في فتح إنشاء كشف الحساب');
        }
    }

    generateNewStatement() {
        if (typeof doctorStatementManager !== 'undefined') {
            doctorStatementManager.showGenerateStatementModal();
        } else {
            showError('مدير كشوف الحساب غير متاح');
        }
    }

    async deleteDoctor(id) {
        if (confirm('هل أنت متأكد من حذف هذا الطبيب؟')) {
            try {
                await db.delete('doctors', id);
                showSuccess('تم حذف الطبيب بنجاح');
                await this.refreshData();
            } catch (error) {
                console.error('خطأ في حذف الطبيب:', error);
                showError('فشل في حذف الطبيب');
            }
        }
    }

    exportDoctors() {
        // TODO: تنفيذ تصدير الأطباء
        console.log('تصدير الأطباء');
    }

    // ========================================
    // عرض كشوف الحساب - Render Statements
    // ========================================

    renderStatements() {
        const content = document.getElementById('statements-content');
        if (!content) return;

        content.innerHTML = `
            <div class="statements-placeholder">
                <i class="fas fa-file-invoice"></i>
                <h3>كشوف الحساب</h3>
                <p>سيتم تطوير هذا القسم قريباً</p>
            </div>
        `;
    }

    // ========================================
    // عرض المدفوعات - Render Payments
    // ========================================

    renderPayments() {
        const content = document.getElementById('payments-content');
        if (!content) return;

        content.innerHTML = `
            <div class="payments-placeholder">
                <i class="fas fa-money-bill"></i>
                <h3>المدفوعات</h3>
                <p>سيتم تطوير هذا القسم قريباً</p>
            </div>
        `;
    }

    // ========================================
    // عرض التقارير - Render Reports
    // ========================================

    renderReports() {
        if (typeof doctorReportsManager !== 'undefined') {
            doctorReportsManager.renderDoctorReports();
        } else {
            const content = document.getElementById('reports-content');
            if (content) {
                content.innerHTML = `
                    <div class="reports-placeholder">
                        <i class="fas fa-chart-bar"></i>
                        <h3>التقارير</h3>
                        <p>مدير التقارير غير متاح</p>
                    </div>
                `;
            }
        }
    }
}

// ========================================
// تهيئة وحدة الأطباء - Initialize Doctors Module
// ========================================

// إنشاء مثيل من مدير الأطباء
const doctorsManager = new DoctorsManager();

// دالة تهيئة الوحدة
async function initializeDoctorsModule() {
    try {
        const success = await doctorsManager.init();
        if (success) {
            console.log('✅ تم تهيئة وحدة الأطباء بنجاح');
        } else {
            console.error('❌ فشل في تهيئة وحدة الأطباء');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة وحدة الأطباء:', error);
        return false;
    }
}

// تصدير الوحدة للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { doctorsManager, DoctorsManager };
}

console.log('✅ تم تحميل وحدة إدارة الأطباء بنجاح');
