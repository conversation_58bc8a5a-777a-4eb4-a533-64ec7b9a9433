// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// وحدة إدارة الموظفين - Employees Management Module
// ========================================

console.log('👥 تحميل وحدة إدارة الموظفين...');

// ========================================
// فئة إدارة الموظفين - Employees Manager
// ========================================

class EmployeesManager {
    constructor() {
        this.employees = [];
        this.commissionTypes = [];
        this.employeeCommissions = [];
        this.payrollRecords = [];
        this.attendanceRecords = [];
        this.currentEmployee = null;
        this.currentView = 'list'; // list, profile, payroll, attendance, commissions
    }

    // ========================================
    // تهيئة الوحدة - Initialize Module
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة وحدة الموظفين...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            console.log('✅ تم تهيئة وحدة الموظفين بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة وحدة الموظفين:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل الموظفين
            this.employees = await db.findAll('employees');
            
            // تحميل أنواع العمولات
            this.commissionTypes = await db.findAll('commission_types');
            
            // تحميل عمولات الموظفين
            this.employeeCommissions = await db.findAll('employee_commissions');
            
            // تحميل كشوف المرتبات
            this.payrollRecords = await db.findAll('payroll_records');
            
            // تحميل سجلات الحضور
            this.attendanceRecords = await db.findAll('attendance_records');
            
            console.log(`📊 تم تحميل ${this.employees.length} موظف`);
            console.log(`💰 تم تحميل ${this.commissionTypes.length} نوع عمولة`);
            console.log(`📋 تم تحميل ${this.payrollRecords.length} كشف راتب`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // عرض وحدة الموظفين - Render Employees Module
    // ========================================

    async render() {
        try {
            const contentArea = document.getElementById('content-area');
            if (!contentArea) return;

            // إنشاء HTML للوحدة
            contentArea.innerHTML = this.getEmployeesHTML();
            
            // تحميل البيانات وعرضها
            await this.loadInitialData();
            
            // عرض قائمة الموظفين
            this.renderEmployeesList();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
        } catch (error) {
            console.error('خطأ في عرض وحدة الموظفين:', error);
            showError('فشل في تحميل وحدة الموظفين');
        }
    }

    // ========================================
    // إنشاء HTML للوحدة - Get Employees HTML
    // ========================================

    getEmployeesHTML() {
        return `
            <div class="employees-container">
                <!-- شريط الأدوات العلوي -->
                <div class="employees-toolbar">
                    <div class="toolbar-left">
                        <h1 class="page-title">
                            <i class="fas fa-users"></i>
                            إدارة الموظفين
                        </h1>
                        <p class="page-subtitle">إدارة شاملة للموظفين والمرتبات ونظام العمولات المتقدم</p>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="employeesManager.showNewEmployeeModal()">
                            <i class="fas fa-plus"></i>
                            موظف جديد
                        </button>
                        <button class="btn btn-outline" onclick="employeesManager.generatePayroll()">
                            <i class="fas fa-calculator"></i>
                            حساب المرتبات
                        </button>
                        <button class="btn btn-outline" onclick="employeesManager.refreshData()">
                            <i class="fas fa-sync"></i>
                            تحديث
                        </button>
                        <button class="btn btn-outline" onclick="employeesManager.exportEmployees()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="employees-stats">
                    <div class="stat-card stat-card-primary">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-employees" class="stat-value">0</h3>
                            <p class="stat-title">إجمالي الموظفين</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-success">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-salaries" class="stat-value">0</h3>
                            <p class="stat-title">إجمالي المرتبات</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-warning">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-commissions" class="stat-value">0</h3>
                            <p class="stat-title">إجمالي العمولات</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-info">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="attendance-rate" class="stat-value">0%</h3>
                            <p class="stat-title">معدل الحضور</p>
                        </div>
                    </div>
                </div>

                <!-- التنقل بين الأقسام -->
                <div class="employees-navigation">
                    <div class="nav-tabs">
                        <button class="nav-tab active" data-view="list" onclick="employeesManager.switchView('list')">
                            <i class="fas fa-list"></i>
                            قائمة الموظفين
                        </button>
                        <button class="nav-tab" data-view="payroll" onclick="employeesManager.switchView('payroll')">
                            <i class="fas fa-calculator"></i>
                            كشوف المرتبات
                        </button>
                        <button class="nav-tab" data-view="commissions" onclick="employeesManager.switchView('commissions')">
                            <i class="fas fa-percentage"></i>
                            نظام العمولات
                        </button>
                        <button class="nav-tab" data-view="attendance" onclick="employeesManager.switchView('attendance')">
                            <i class="fas fa-clock"></i>
                            الحضور والغياب
                        </button>
                        <button class="nav-tab" data-view="reports" onclick="employeesManager.switchView('reports')">
                            <i class="fas fa-chart-bar"></i>
                            التقارير
                        </button>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="employees-content">
                    
                    <!-- قسم قائمة الموظفين -->
                    <div id="employees-list-section" class="content-section active">
                        
                        <!-- شريط البحث والتصفية -->
                        <div class="employees-filters">
                            <div class="filter-group">
                                <input type="search" id="employees-search" class="form-control" placeholder="البحث في الموظفين...">
                            </div>
                            <div class="filter-group">
                                <select id="department-filter" class="form-control">
                                    <option value="">جميع الأقسام</option>
                                    <option value="الإنتاج">الإنتاج</option>
                                    <option value="الإدارة">الإدارة</option>
                                    <option value="المبيعات">المبيعات</option>
                                    <option value="الجودة">الجودة</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <select id="position-filter" class="form-control">
                                    <option value="">جميع المناصب</option>
                                    <option value="فني أسنان أول">فني أسنان أول</option>
                                    <option value="فنية أسنان">فنية أسنان</option>
                                    <option value="مساعد فني">مساعد فني</option>
                                    <option value="موظفة استقبال">موظفة استقبال</option>
                                    <option value="مدير الإنتاج">مدير الإنتاج</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <select id="status-filter" class="form-control">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>

                        <!-- قائمة الموظفين -->
                        <div class="employees-list-container">
                            <div class="table-container">
                                <table class="table employees-table">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>المنصب</th>
                                            <th>القسم</th>
                                            <th>الهاتف</th>
                                            <th>الراتب الأساسي</th>
                                            <th>معدل العمولة</th>
                                            <th>تاريخ التوظيف</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="employees-table-body">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>

                    <!-- قسم كشوف المرتبات -->
                    <div id="payroll-section" class="content-section">
                        <div class="payroll-header">
                            <h3>كشوف المرتبات</h3>
                            <button class="btn btn-primary" onclick="employeesManager.generateNewPayroll()">
                                <i class="fas fa-plus"></i>
                                إنشاء كشف راتب جديد
                            </button>
                        </div>
                        <div id="payroll-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>

                    <!-- قسم نظام العمولات -->
                    <div id="commissions-section" class="content-section">
                        <div class="commissions-header">
                            <h3>نظام العمولات المتقدم</h3>
                            <button class="btn btn-primary" onclick="employeesManager.manageCommissions()">
                                <i class="fas fa-cogs"></i>
                                إدارة العمولات
                            </button>
                        </div>
                        <div id="commissions-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>

                    <!-- قسم الحضور والغياب -->
                    <div id="attendance-section" class="content-section">
                        <div class="attendance-header">
                            <h3>الحضور والغياب</h3>
                            <button class="btn btn-primary" onclick="employeesManager.recordAttendance()">
                                <i class="fas fa-plus"></i>
                                تسجيل حضور
                            </button>
                        </div>
                        <div id="attendance-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>

                    <!-- قسم التقارير -->
                    <div id="reports-section" class="content-section">
                        <div class="reports-header">
                            <h3>تقارير الموظفين</h3>
                        </div>
                        <div id="reports-content">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>

                </div>
            </div>

            <!-- نافذة موظف جديد -->
            <div id="new-employee-modal" class="modal hidden">
                <div class="modal-overlay"></div>
                <div class="modal-content large-modal">
                    <div class="modal-header">
                        <h2>إضافة موظف جديد</h2>
                        <button class="modal-close" onclick="employeesManager.closeNewEmployeeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="new-employee-form" class="modal-body">
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h3 class="section-title">المعلومات الأساسية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="employee-number" class="form-label">رقم الموظف *</label>
                                    <input type="text" id="employee-number" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="employee-name" class="form-label">اسم الموظف *</label>
                                    <input type="text" id="employee-name" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="employee-position" class="form-label">المنصب *</label>
                                    <select id="employee-position" class="form-control" required>
                                        <option value="">اختر المنصب</option>
                                        <option value="فني أسنان أول">فني أسنان أول</option>
                                        <option value="فنية أسنان">فنية أسنان</option>
                                        <option value="مساعد فني">مساعد فني</option>
                                        <option value="موظفة استقبال">موظفة استقبال</option>
                                        <option value="مدير الإنتاج">مدير الإنتاج</option>
                                        <option value="مدير عام">مدير عام</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="employee-department" class="form-label">القسم</label>
                                    <select id="employee-department" class="form-control">
                                        <option value="">اختر القسم</option>
                                        <option value="الإنتاج">الإنتاج</option>
                                        <option value="الإدارة">الإدارة</option>
                                        <option value="المبيعات">المبيعات</option>
                                        <option value="الجودة">الجودة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="employee-phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" id="employee-phone" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="employee-email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" id="employee-email" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- معلومات شخصية -->
                        <div class="form-section">
                            <h3 class="section-title">المعلومات الشخصية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="national-id" class="form-label">رقم الهوية</label>
                                    <input type="text" id="national-id" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="birth-date" class="form-label">تاريخ الميلاد</label>
                                    <input type="date" id="birth-date" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="hire-date" class="form-label">تاريخ التوظيف *</label>
                                    <input type="date" id="hire-date" class="form-control" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="employee-address" class="form-label">العنوان</label>
                                    <textarea id="employee-address" class="form-control" rows="2"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="emergency-contact" class="form-label">جهة الاتصال للطوارئ</label>
                                    <input type="text" id="emergency-contact" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="emergency-phone" class="form-label">هاتف الطوارئ</label>
                                    <input type="tel" id="emergency-phone" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- معلومات مالية -->
                        <div class="form-section">
                            <h3 class="section-title">المعلومات المالية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="basic-salary" class="form-label">الراتب الأساسي *</label>
                                    <input type="number" id="basic-salary" class="form-control" min="0" step="0.01" required>
                                </div>
                                <div class="form-group">
                                    <label for="housing-allowance" class="form-label">بدل السكن</label>
                                    <input type="number" id="housing-allowance" class="form-control" min="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="transport-allowance" class="form-label">بدل النقل</label>
                                    <input type="number" id="transport-allowance" class="form-control" min="0" step="0.01">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="commission-rate" class="form-label">معدل العمولة (%)</label>
                                    <input type="number" id="commission-rate" class="form-control" min="0" max="100" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="overtime-rate" class="form-label">معدل الساعات الإضافية</label>
                                    <input type="number" id="overtime-rate" class="form-control" min="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="bank-account" class="form-label">رقم الحساب البنكي</label>
                                    <input type="text" id="bank-account" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="form-section">
                            <h3 class="section-title">ملاحظات إضافية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="employee-notes" class="form-label">ملاحظات</label>
                                    <textarea id="employee-notes" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="employeesManager.closeNewEmployeeModal()">
                            إلغاء
                        </button>
                        <button type="submit" form="new-employee-form" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الموظف
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إعداد الأحداث - Setup Event Listeners
    // ========================================

    setupEventListeners() {
        // نموذج موظف جديد
        const newEmployeeForm = document.getElementById('new-employee-form');
        if (newEmployeeForm) {
            newEmployeeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveEmployee();
            });
        }

        // البحث والتصفية
        const searchInput = document.getElementById('employees-search');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(() => {
                this.filterEmployees();
            }, 300));
        }

        const departmentFilter = document.getElementById('department-filter');
        if (departmentFilter) {
            departmentFilter.addEventListener('change', () => {
                this.filterEmployees();
            });
        }

        const positionFilter = document.getElementById('position-filter');
        if (positionFilter) {
            positionFilter.addEventListener('change', () => {
                this.filterEmployees();
            });
        }

        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filterEmployees();
            });
        }

        // تعيين تاريخ التوظيف الافتراضي
        const hireDateInput = document.getElementById('hire-date');
        if (hireDateInput) {
            hireDateInput.value = new Date().toISOString().split('T')[0];
        }

        // توليد رقم موظف تلقائي
        this.generateEmployeeNumber();
    }

    // ========================================
    // توليد رقم موظف تلقائي - Generate Employee Number
    // ========================================

    async generateEmployeeNumber() {
        try {
            const lastEmployee = this.employees
                .filter(emp => emp.employee_number && emp.employee_number.startsWith('EMP-'))
                .sort((a, b) => b.employee_number.localeCompare(a.employee_number))[0];

            let nextNumber = 1;
            if (lastEmployee) {
                const lastNumber = parseInt(lastEmployee.employee_number.split('-')[1]);
                nextNumber = lastNumber + 1;
            }

            const employeeNumberInput = document.getElementById('employee-number');
            if (employeeNumberInput) {
                employeeNumberInput.value = `EMP-${String(nextNumber).padStart(3, '0')}`;
            }
        } catch (error) {
            console.error('خطأ في توليد رقم الموظف:', error);
        }
    }

    // ========================================
    // عرض قائمة الموظفين - Render Employees List
    // ========================================

    renderEmployeesList() {
        const tableBody = document.getElementById('employees-table-body');
        if (!tableBody) return;

        if (this.employees.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <h3>لا يوجد موظفين مسجلين</h3>
                            <p>ابدأ بإضافة أول موظف</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.employees.map(employee => {
            const statusClass = employee.isActive ? 'success' : 'error';
            const statusText = employee.isActive ? 'نشط' : 'غير نشط';

            return `
                <tr data-employee-id="${employee.id}">
                    <td>
                        <div class="employee-info">
                            <strong>${employee.name}</strong>
                            <br><small>${employee.employee_number}</small>
                            ${employee.email ? `<br><small>${employee.email}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="position">${employee.position || '-'}</span>
                    </td>
                    <td>
                        <span class="department">${employee.department || '-'}</span>
                    </td>
                    <td>
                        <span class="phone">${employee.phone || '-'}</span>
                    </td>
                    <td>
                        <span class="salary">${formatCurrency(employee.basic_salary || 0)}</span>
                    </td>
                    <td>
                        <span class="commission-rate">${employee.commission_rate || 0}%</span>
                    </td>
                    <td>
                        <span class="hire-date">${formatDate(employee.hire_date)}</span>
                    </td>
                    <td>
                        <span class="status-badge status-${statusClass}">${statusText}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline" onclick="employeesManager.viewEmployeeProfile(${employee.id})" title="عرض الملف">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="employeesManager.editEmployee(${employee.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="employeesManager.manageEmployeeCommissions(${employee.id})" title="إدارة العمولات">
                                <i class="fas fa-percentage"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="employeesManager.generateEmployeePayroll(${employee.id})" title="كشف راتب">
                                <i class="fas fa-calculator"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="employeesManager.deleteEmployee(${employee.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // تحديث الإحصائيات
        this.updateStatistics();
    }

    // ========================================
    // تحديث الإحصائيات - Update Statistics
    // ========================================

    updateStatistics() {
        const totalElement = document.getElementById('total-employees');
        const salariesElement = document.getElementById('total-salaries');
        const commissionsElement = document.getElementById('total-commissions');
        const attendanceElement = document.getElementById('attendance-rate');

        if (totalElement) totalElement.textContent = this.employees.length;

        const totalSalaries = this.employees.reduce((sum, emp) => sum + (emp.basic_salary || 0), 0);
        if (salariesElement) salariesElement.textContent = formatCurrency(totalSalaries);

        const totalCommissions = this.payrollRecords.reduce((sum, record) => sum + (record.commissions || 0), 0);
        if (commissionsElement) commissionsElement.textContent = formatCurrency(totalCommissions);

        // حساب معدل الحضور (مؤقت)
        const attendanceRate = 95; // سيتم حسابه لاحقاً من بيانات الحضور الفعلية
        if (attendanceElement) attendanceElement.textContent = `${attendanceRate}%`;
    }

    // ========================================
    // تبديل العرض - Switch View
    // ========================================

    switchView(view) {
        this.currentView = view;

        // تحديث التبويبات
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // تحديث المحتوى
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        switch (view) {
            case 'list':
                document.getElementById('employees-list-section').classList.add('active');
                this.renderEmployeesList();
                break;
            case 'payroll':
                document.getElementById('payroll-section').classList.add('active');
                this.renderPayroll();
                break;
            case 'commissions':
                document.getElementById('commissions-section').classList.add('active');
                this.renderCommissions();
                break;
            case 'attendance':
                document.getElementById('attendance-section').classList.add('active');
                this.renderAttendance();
                break;
            case 'reports':
                document.getElementById('reports-section').classList.add('active');
                this.renderReports();
                break;
        }
    }

    // ========================================
    // حفظ موظف جديد - Save New Employee
    // ========================================

    async saveEmployee() {
        try {
            // التحقق من صحة البيانات
            const validationResult = this.validateEmployeeData();
            if (!validationResult.isValid) {
                showError(validationResult.message);
                return;
            }

            // جمع البيانات
            const employeeData = this.collectEmployeeData();

            // حفظ في قاعدة البيانات
            const result = await db.insert('employees', employeeData);

            if (result) {
                showSuccess('تم حفظ الموظف بنجاح');
                this.closeNewEmployeeModal();
                await this.refreshData();
            } else {
                showError('فشل في حفظ الموظف');
            }

        } catch (error) {
            console.error('خطأ في حفظ الموظف:', error);
            showError('حدث خطأ أثناء حفظ الموظف');
        }
    }

    validateEmployeeData() {
        const employeeNumber = document.getElementById('employee-number').value.trim();
        const name = document.getElementById('employee-name').value.trim();
        const position = document.getElementById('employee-position').value;
        const phone = document.getElementById('employee-phone').value.trim();
        const hireDate = document.getElementById('hire-date').value;
        const basicSalary = document.getElementById('basic-salary').value;

        if (!employeeNumber) {
            return { isValid: false, message: 'يرجى إدخال رقم الموظف' };
        }

        if (!name) {
            return { isValid: false, message: 'يرجى إدخال اسم الموظف' };
        }

        if (!position) {
            return { isValid: false, message: 'يرجى اختيار المنصب' };
        }

        if (!phone) {
            return { isValid: false, message: 'يرجى إدخال رقم الهاتف' };
        }

        if (!hireDate) {
            return { isValid: false, message: 'يرجى إدخال تاريخ التوظيف' };
        }

        if (!basicSalary || parseFloat(basicSalary) <= 0) {
            return { isValid: false, message: 'يرجى إدخال راتب أساسي صحيح' };
        }

        return { isValid: true };
    }

    collectEmployeeData() {
        return {
            employee_number: document.getElementById('employee-number').value.trim(),
            name: document.getElementById('employee-name').value.trim(),
            position: document.getElementById('employee-position').value,
            department: document.getElementById('employee-department').value,
            phone: document.getElementById('employee-phone').value.trim(),
            email: document.getElementById('employee-email').value.trim(),
            address: document.getElementById('employee-address').value.trim(),
            national_id: document.getElementById('national-id').value.trim(),
            birth_date: document.getElementById('birth-date').value,
            hire_date: document.getElementById('hire-date').value,
            contract_type: 'permanent',
            basic_salary: parseFloat(document.getElementById('basic-salary').value) || 0,
            housing_allowance: parseFloat(document.getElementById('housing-allowance').value) || 0,
            transport_allowance: parseFloat(document.getElementById('transport-allowance').value) || 0,
            other_allowances: 0,
            commission_rate: parseFloat(document.getElementById('commission-rate').value) || 0,
            overtime_rate: parseFloat(document.getElementById('overtime-rate').value) || 0,
            bank_account: document.getElementById('bank-account').value.trim(),
            emergency_contact: document.getElementById('emergency-contact').value.trim(),
            emergency_phone: document.getElementById('emergency-phone').value.trim(),
            notes: document.getElementById('employee-notes').value.trim(),
            isActive: true
        };
    }

    // ========================================
    // إدارة النوافذ المنبثقة - Modal Management
    // ========================================

    showNewEmployeeModal() {
        const modal = document.getElementById('new-employee-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.resetNewEmployeeForm();
            this.generateEmployeeNumber();
        }
    }

    closeNewEmployeeModal() {
        const modal = document.getElementById('new-employee-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetNewEmployeeForm();
        }
    }

    resetNewEmployeeForm() {
        const form = document.getElementById('new-employee-form');
        if (form) {
            form.reset();
        }
    }

    // ========================================
    // تصفية الموظفين - Filter Employees
    // ========================================

    filterEmployees() {
        const searchTerm = document.getElementById('employees-search')?.value.toLowerCase() || '';
        const departmentFilter = document.getElementById('department-filter')?.value || '';
        const positionFilter = document.getElementById('position-filter')?.value || '';
        const statusFilter = document.getElementById('status-filter')?.value || '';

        let filteredEmployees = [...this.employees];

        // تطبيق البحث النصي
        if (searchTerm) {
            filteredEmployees = filteredEmployees.filter(employee =>
                employee.name.toLowerCase().includes(searchTerm) ||
                (employee.employee_number && employee.employee_number.toLowerCase().includes(searchTerm)) ||
                (employee.phone && employee.phone.includes(searchTerm))
            );
        }

        // تطبيق تصفية القسم
        if (departmentFilter) {
            filteredEmployees = filteredEmployees.filter(employee =>
                employee.department === departmentFilter
            );
        }

        // تطبيق تصفية المنصب
        if (positionFilter) {
            filteredEmployees = filteredEmployees.filter(employee =>
                employee.position === positionFilter
            );
        }

        // تطبيق تصفية الحالة
        if (statusFilter) {
            const isActive = statusFilter === 'active';
            filteredEmployees = filteredEmployees.filter(employee =>
                employee.isActive === isActive
            );
        }

        // تحديث العرض
        const originalEmployees = this.employees;
        this.employees = filteredEmployees;
        this.renderEmployeesList();
        this.employees = originalEmployees;
    }

    // ========================================
    // إجراءات الموظفين - Employee Actions
    // ========================================

    async refreshData() {
        await this.loadInitialData();
        this.renderEmployeesList();
    }

    viewEmployeeProfile(id) {
        // TODO: تنفيذ عرض ملف الموظف
        console.log('عرض ملف الموظف:', id);
        showInfo('عرض ملف الموظف - قيد التطوير');
    }

    editEmployee(id) {
        // TODO: تنفيذ تعديل الموظف
        console.log('تعديل الموظف:', id);
        showInfo('تعديل الموظف - قيد التطوير');
    }

    manageEmployeeCommissions(id) {
        // TODO: تنفيذ إدارة عمولات الموظف
        console.log('إدارة عمولات الموظف:', id);
        showInfo('إدارة العمولات - قيد التطوير');
    }

    generateEmployeePayroll(id) {
        // TODO: تنفيذ إنشاء كشف راتب
        console.log('إنشاء كشف راتب للموظف:', id);
        showInfo('إنشاء كشف الراتب - قيد التطوير');
    }

    async deleteEmployee(id) {
        if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
            try {
                await db.delete('employees', id);
                showSuccess('تم حذف الموظف بنجاح');
                await this.refreshData();
            } catch (error) {
                console.error('خطأ في حذف الموظف:', error);
                showError('فشل في حذف الموظف');
            }
        }
    }

    generatePayroll() {
        // TODO: تنفيذ حساب المرتبات العام
        console.log('حساب المرتبات العام');
        showInfo('حساب المرتبات - قيد التطوير');
    }

    exportEmployees() {
        // TODO: تنفيذ تصدير الموظفين
        console.log('تصدير الموظفين');
        showInfo('تصدير الموظفين - قيد التطوير');
    }

    // ========================================
    // عرض الأقسام المختلفة - Render Different Sections
    // ========================================

    renderPayroll() {
        const content = document.getElementById('payroll-content');
        if (!content) return;

        content.innerHTML = `
            <div class="payroll-placeholder">
                <i class="fas fa-calculator"></i>
                <h3>كشوف المرتبات</h3>
                <p>سيتم تطوير هذا القسم قريباً</p>
            </div>
        `;
    }

    renderCommissions() {
        const content = document.getElementById('commissions-content');
        if (!content) return;

        content.innerHTML = `
            <div class="commissions-placeholder">
                <i class="fas fa-percentage"></i>
                <h3>نظام العمولات المتقدم</h3>
                <p>سيتم تطوير هذا القسم قريباً</p>
            </div>
        `;
    }

    renderAttendance() {
        const content = document.getElementById('attendance-content');
        if (!content) return;

        content.innerHTML = `
            <div class="attendance-placeholder">
                <i class="fas fa-clock"></i>
                <h3>الحضور والغياب</h3>
                <p>سيتم تطوير هذا القسم قريباً</p>
            </div>
        `;
    }

    renderReports() {
        const content = document.getElementById('reports-content');
        if (!content) return;

        content.innerHTML = `
            <div class="reports-placeholder">
                <i class="fas fa-chart-bar"></i>
                <h3>تقارير الموظفين</h3>
                <p>سيتم تطوير هذا القسم قريباً</p>
            </div>
        `;
    }

    // ========================================
    // وظائف مؤقتة - Placeholder Functions
    // ========================================

    generateNewPayroll() {
        showInfo('إنشاء كشف راتب جديد - قيد التطوير');
    }

    manageCommissions() {
        showInfo('إدارة العمولات - قيد التطوير');
    }

    recordAttendance() {
        showInfo('تسجيل الحضور - قيد التطوير');
    }
}

// ========================================
// تهيئة وحدة الموظفين - Initialize Employees Module
// ========================================

// إنشاء مثيل من مدير الموظفين
const employeesManager = new EmployeesManager();

// دالة تهيئة الوحدة
async function initializeEmployeesModule() {
    try {
        const success = await employeesManager.init();
        if (success) {
            console.log('✅ تم تهيئة وحدة الموظفين بنجاح');
        } else {
            console.error('❌ فشل في تهيئة وحدة الموظفين');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة وحدة الموظفين:', error);
        return false;
    }
}

// تصدير الوحدة للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { employeesManager, EmployeesManager };
}

console.log('✅ تم تحميل وحدة إدارة الموظفين بنجاح');
