// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// إدارة المعامل الخارجية - External Labs Management
// ========================================

console.log('🏢 تحميل وحدة إدارة المعامل الخارجية...');

// ========================================
// فئة إدارة المعامل الخارجية - External Labs Manager
// ========================================

class ExternalLabsManager {
    constructor() {
        this.labs = [];
        this.orders = [];
        this.currentView = 'list'; // list, new, edit, details, orders
        this.currentLab = null;
        this.currentOrder = null;
        this.isInitialized = false;
    }

    // ========================================
    // تهيئة الوحدة - Initialize Module
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة وحدة المعامل الخارجية...');

            // تحميل البيانات
            await this.loadData();

            // إعداد واجهة المستخدم
            this.setupUI();

            // إعداد معالجات الأحداث
            this.setupEventHandlers();

            // تحديث الإحصائيات
            this.updateStats();

            this.isInitialized = true;
            console.log('✅ تم تهيئة وحدة المعامل الخارجية بنجاح');
            console.log(`📊 تم تحميل ${this.labs.length} معمل و ${this.orders.length} طلب`);

            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة وحدة المعامل الخارجية:', error);

            // في حالة الخطأ، إنشاء بيانات تجريبية
            await this.createSampleData();
            this.setupUI();
            this.updateStats();

            return true;
        }
    }

    // ========================================
    // إعداد واجهة المستخدم - Setup UI
    // ========================================

    setupUI() {
        // عرض الواجهة الرئيسية
        this.renderMainInterface();
    }

    // ========================================
    // عرض الوحدة - Render Module (للتوافق مع النظام)
    // ========================================

    async render() {
        if (!this.isInitialized) {
            await this.init();
        } else {
            this.renderMainInterface();
        }
    }

    // ========================================
    // تحميل البيانات - Load Data
    // ========================================

    async loadData() {
        try {
            // تحميل المعامل الخارجية
            this.labs = await db.findAll('external_labs') || [];

            // تحميل الطلبات
            this.orders = await db.findAll('external_orders') || [];

            // إذا لم توجد بيانات، إنشاء بيانات تجريبية
            if (this.labs.length === 0) {
                await this.createSampleData();
            }

            console.log(`📊 تم تحميل ${this.labs.length} معمل خارجي و ${this.orders.length} طلب`);
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            // إنشاء بيانات تجريبية في حالة الخطأ
            await this.createSampleData();
        }
    }

    // ========================================
    // إنشاء بيانات تجريبية - Create Sample Data
    // ========================================

    async createSampleData() {
        try {
            console.log('🔄 إنشاء بيانات تجريبية للمعامل الخارجية...');

            // بيانات المعامل التجريبية
            const sampleLabs = [
                {
                    id: 1,
                    name: 'معمل الأسنان المتقدم',
                    city: 'الرياض',
                    address: 'شارع الملك فهد، حي العليا',
                    phone: '0112345678',
                    email: '<EMAIL>',
                    contact_person: 'د. أحمد محمد',
                    specialties: 'تركيبات ثابتة، تركيبات متحركة، زراعة الأسنان',
                    delivery_time: 5,
                    quality_rating: 5,
                    payment_terms: 'credit_15',
                    credit_limit: 50000,
                    notes: 'معمل موثوق مع جودة عالية وسرعة في التسليم',
                    isActive: true,
                    created_at: '2024-01-01T00:00:00.000Z',
                    updated_at: '2024-01-01T00:00:00.000Z',
                    total_cases: 45,
                    total_revenue: 125000
                },
                {
                    id: 2,
                    name: 'مختبر الابتسامة الذهبية',
                    city: 'جدة',
                    address: 'طريق الملك عبدالعزيز، حي الروضة',
                    phone: '0123456789',
                    email: '<EMAIL>',
                    contact_person: 'أ. سارة أحمد',
                    specialties: 'تجميل الأسنان، فينير، لومينير',
                    delivery_time: 7,
                    quality_rating: 4,
                    payment_terms: 'credit_7',
                    credit_limit: 30000,
                    notes: 'متخصص في أعمال التجميل والفينير',
                    isActive: true,
                    created_at: '2024-01-15T00:00:00.000Z',
                    updated_at: '2024-01-15T00:00:00.000Z',
                    total_cases: 32,
                    total_revenue: 85000
                },
                {
                    id: 3,
                    name: 'معمل الدقة للتركيبات',
                    city: 'الدمام',
                    address: 'شارع الأمير محمد بن فهد',
                    phone: '0134567890',
                    email: '<EMAIL>',
                    contact_person: 'د. محمد علي',
                    specialties: 'تقويم الأسنان، أجهزة تقويمية',
                    delivery_time: 10,
                    quality_rating: 4,
                    payment_terms: 'credit_30',
                    credit_limit: 40000,
                    notes: 'متخصص في أجهزة التقويم والأطقم المتحركة',
                    isActive: true,
                    created_at: '2024-02-01T00:00:00.000Z',
                    updated_at: '2024-02-01T00:00:00.000Z',
                    total_cases: 28,
                    total_revenue: 72000
                },
                {
                    id: 4,
                    name: 'مختبر النخبة الطبي',
                    city: 'الرياض',
                    address: 'حي الملز، شارع التخصصي',
                    phone: '0145678901',
                    email: '<EMAIL>',
                    contact_person: 'د. فاطمة حسن',
                    specialties: 'زراعة الأسنان، جراحة الفم',
                    delivery_time: 3,
                    quality_rating: 5,
                    payment_terms: 'cash',
                    credit_limit: 60000,
                    notes: 'معمل سريع ومتخصص في أعمال الزراعة',
                    isActive: true,
                    created_at: '2024-02-15T00:00:00.000Z',
                    updated_at: '2024-02-15T00:00:00.000Z',
                    total_cases: 38,
                    total_revenue: 95000
                },
                {
                    id: 5,
                    name: 'معمل الأسنان الشامل',
                    city: 'مكة المكرمة',
                    address: 'شارع إبراهيم الخليل، العزيزية',
                    phone: '0156789012',
                    email: '<EMAIL>',
                    contact_person: 'أ. عبدالله محمد',
                    specialties: 'جميع أنواع التركيبات، طوارئ',
                    delivery_time: 6,
                    quality_rating: 3,
                    payment_terms: 'credit_15',
                    credit_limit: 25000,
                    notes: 'معمل شامل يقدم جميع الخدمات',
                    isActive: false,
                    created_at: '2024-03-01T00:00:00.000Z',
                    updated_at: '2024-03-01T00:00:00.000Z',
                    total_cases: 15,
                    total_revenue: 35000
                }
            ];

            // بيانات الطلبات التجريبية
            const sampleOrders = [
                {
                    id: 1,
                    lab_id: 1,
                    order_number: 'EXT-2024-001',
                    work_type: 'crown',
                    patient_name: 'أحمد محمد علي',
                    doctor_name: 'د. سعد الأحمد',
                    order_date: '2024-01-10',
                    delivery_date: '2024-01-15',
                    quantity: 2,
                    unit_price: 800,
                    total_amount: 1600,
                    description: 'تاج زيركونيا للأسنان الأمامية',
                    notes: 'لون A2، تطابق مع الأسنان الطبيعية',
                    status: 'completed',
                    created_at: '2024-01-10T00:00:00.000Z',
                    updated_at: '2024-01-15T00:00:00.000Z'
                },
                {
                    id: 2,
                    lab_id: 2,
                    order_number: 'EXT-2024-002',
                    work_type: 'bridge',
                    patient_name: 'فاطمة أحمد',
                    doctor_name: 'د. نورا السالم',
                    order_date: '2024-01-12',
                    delivery_date: '2024-01-19',
                    quantity: 1,
                    unit_price: 2400,
                    total_amount: 2400,
                    description: 'جسر من 3 وحدات',
                    notes: 'جسر معدني خزفي للأسنان الخلفية',
                    status: 'in_progress',
                    created_at: '2024-01-12T00:00:00.000Z',
                    updated_at: '2024-01-12T00:00:00.000Z'
                },
                {
                    id: 3,
                    lab_id: 1,
                    order_number: 'EXT-2024-003',
                    work_type: 'denture',
                    patient_name: 'محمد حسن',
                    doctor_name: 'د. علي الزهراني',
                    order_date: '2024-01-15',
                    delivery_date: '2024-01-20',
                    quantity: 1,
                    unit_price: 1800,
                    total_amount: 1800,
                    description: 'طقم أسنان كامل علوي',
                    notes: 'طقم أكريليك مع أسنان خزفية',
                    status: 'pending',
                    created_at: '2024-01-15T00:00:00.000Z',
                    updated_at: '2024-01-15T00:00:00.000Z'
                },
                {
                    id: 4,
                    lab_id: 3,
                    order_number: 'EXT-2024-004',
                    work_type: 'orthodontic',
                    patient_name: 'سارة محمد',
                    doctor_name: 'د. خالد العتيبي',
                    order_date: '2024-01-18',
                    delivery_date: '2024-01-28',
                    quantity: 1,
                    unit_price: 3200,
                    total_amount: 3200,
                    description: 'جهاز تقويم متحرك',
                    notes: 'جهاز هولي للفك العلوي',
                    status: 'delivered',
                    created_at: '2024-01-18T00:00:00.000Z',
                    updated_at: '2024-01-28T00:00:00.000Z'
                },
                {
                    id: 5,
                    lab_id: 4,
                    order_number: 'EXT-2024-005',
                    work_type: 'implant',
                    patient_name: 'عبدالله أحمد',
                    doctor_name: 'د. منى الشهري',
                    order_date: '2024-01-20',
                    delivery_date: '2024-01-23',
                    quantity: 1,
                    unit_price: 1200,
                    total_amount: 1200,
                    description: 'تاج زراعة على implant',
                    notes: 'تاج زيركونيا على قاعدة تيتانيوم',
                    status: 'completed',
                    created_at: '2024-01-20T00:00:00.000Z',
                    updated_at: '2024-01-23T00:00:00.000Z'
                }
            ];

            // حفظ البيانات في قاعدة البيانات
            for (const lab of sampleLabs) {
                await db.insert('external_labs', lab);
            }

            for (const order of sampleOrders) {
                await db.insert('external_orders', order);
            }

            // تحديث القوائم المحلية
            this.labs = sampleLabs;
            this.orders = sampleOrders;

            console.log('✅ تم إنشاء البيانات التجريبية بنجاح');

        } catch (error) {
            console.error('خطأ في إنشاء البيانات التجريبية:', error);

            // في حالة فشل قاعدة البيانات، استخدام البيانات محلياً
            this.labs = [
                {
                    id: 1,
                    name: 'معمل الأسنان المتقدم',
                    city: 'الرياض',
                    phone: '0112345678',
                    email: '<EMAIL>',
                    quality_rating: 5,
                    isActive: true,
                    total_cases: 45,
                    total_revenue: 125000
                },
                {
                    id: 2,
                    name: 'مختبر الابتسامة الذهبية',
                    city: 'جدة',
                    phone: '0123456789',
                    email: '<EMAIL>',
                    quality_rating: 4,
                    isActive: true,
                    total_cases: 32,
                    total_revenue: 85000
                }
            ];

            this.orders = [
                {
                    id: 1,
                    lab_id: 1,
                    order_number: 'EXT-2024-001',
                    work_type: 'crown',
                    total_amount: 1600,
                    status: 'completed',
                    order_date: '2024-01-10'
                },
                {
                    id: 2,
                    lab_id: 2,
                    order_number: 'EXT-2024-002',
                    work_type: 'bridge',
                    total_amount: 2400,
                    status: 'pending',
                    order_date: '2024-01-12'
                }
            ];
        }
    }

    // ========================================
    // إعداد واجهة المستخدم - Setup UI
    // ========================================

    setupUI() {
        this.renderMainInterface();
    }

    renderMainInterface() {
        // البحث عن المحتوى في أماكن مختلفة
        let content = document.getElementById('main-content') ||
                     document.getElementById('content-area') ||
                     document.getElementById('external-labs-module-container');

        if (!content) {
            console.error('❌ لم يتم العثور على حاوي المحتوى');
            return;
        }

        content.innerHTML = `
            <div class="external-labs-container">
                <!-- شريط الأدوات العلوي -->
                <div class="external-labs-toolbar">
                    <div class="toolbar-left">
                        <h1 class="page-title">
                            <i class="fas fa-building"></i>
                            إدارة المعامل الخارجية
                        </h1>
                        <div class="breadcrumb">
                            <span>الرئيسية</span>
                            <i class="fas fa-chevron-left"></i>
                            <span>المعامل الخارجية</span>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="externalLabsManager.showNewLabModal()">
                            <i class="fas fa-plus"></i>
                            إضافة معمل جديد
                        </button>
                        <button class="btn btn-outline" onclick="externalLabsManager.showNewOrderModal()">
                            <i class="fas fa-shopping-cart"></i>
                            طلب جديد
                        </button>
                        <button class="btn btn-outline" onclick="externalLabsManager.exportData()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <button class="btn btn-outline" onclick="externalLabsManager.refreshData()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-outline" onclick="externalLabsManager.resetSampleData()">
                            <i class="fas fa-database"></i>
                            إعادة تعيين البيانات
                        </button>
                        <button class="btn btn-outline" onclick="externalLabsManager.addMoreSampleData()">
                            <i class="fas fa-plus-circle"></i>
                            إضافة بيانات
                        </button>
                        <button class="btn btn-outline" onclick="externalLabsManager.showSettingsModal()">
                            <i class="fas fa-cog"></i>
                            إعدادات
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="total-labs">${this.labs.length}</div>
                            <div class="stat-label">إجمالي المعامل</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="active-labs">${this.labs.filter(lab => lab.isActive).length}</div>
                            <div class="stat-label">معامل نشطة</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="total-orders">${this.orders.length}</div>
                            <div class="stat-label">إجمالي الطلبات</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="pending-orders">${this.orders.filter(order => order.status === 'pending').length}</div>
                            <div class="stat-label">طلبات معلقة</div>
                        </div>
                    </div>
                </div>

                <!-- التبويبات -->
                <div class="nav-tabs">
                    <button class="nav-tab active" data-tab="labs" onclick="externalLabsManager.switchTab('labs')">
                        <i class="fas fa-building"></i>
                        المعامل الخارجية
                        <span class="tab-badge">${this.labs.length}</span>
                    </button>
                    <button class="nav-tab" data-tab="orders" onclick="externalLabsManager.switchTab('orders')">
                        <i class="fas fa-shopping-cart"></i>
                        الطلبات
                        <span class="tab-badge">${this.orders.length}</span>
                    </button>
                    <button class="nav-tab" data-tab="contracts" onclick="externalLabsManager.switchTab('contracts')">
                        <i class="fas fa-file-contract"></i>
                        العقود والاتفاقيات
                    </button>
                    <button class="nav-tab" data-tab="payments" onclick="externalLabsManager.switchTab('payments')">
                        <i class="fas fa-credit-card"></i>
                        المدفوعات
                    </button>
                    <button class="nav-tab" data-tab="quality" onclick="externalLabsManager.switchTab('quality')">
                        <i class="fas fa-star"></i>
                        تقييم الجودة
                    </button>
                    <button class="nav-tab" data-tab="reports" onclick="externalLabsManager.switchTab('reports')">
                        <i class="fas fa-chart-bar"></i>
                        التقارير
                    </button>
                    <button class="nav-tab" data-tab="settings" onclick="externalLabsManager.switchTab('settings')">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </button>
                </div>

                <!-- محتوى التبويبات -->
                <div class="tab-content">
                    <div id="labs-content" class="tab-pane active">
                        ${this.renderLabsList()}
                    </div>
                    <div id="orders-content" class="tab-pane">
                        ${this.renderOrdersList()}
                    </div>
                    <div id="contracts-content" class="tab-pane">
                        ${this.renderContracts()}
                    </div>
                    <div id="payments-content" class="tab-pane">
                        ${this.renderPayments()}
                    </div>
                    <div id="quality-content" class="tab-pane">
                        ${this.renderQuality()}
                    </div>
                    <div id="reports-content" class="tab-pane">
                        ${this.renderReports()}
                    </div>
                    <div id="settings-content" class="tab-pane">
                        ${this.renderSettings()}
                    </div>
                </div>
            </div>

            <!-- نوافذ منبثقة -->
            ${this.renderModals()}
        `;
    }

    // ========================================
    // عرض قائمة المعامل - Render Labs List
    // ========================================

    renderLabsList() {
        if (this.labs.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <h3>لا توجد معامل خارجية</h3>
                    <p>ابدأ بإضافة معمل خارجي جديد</p>
                    <button class="btn btn-primary" onclick="externalLabsManager.showNewLabModal()">
                        <i class="fas fa-plus"></i>
                        إضافة معمل جديد
                    </button>
                </div>
            `;
        }

        return `
            <div class="labs-list">
                <div class="search-filter-bar">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="labs-search" placeholder="البحث في المعامل..." onkeyup="externalLabsManager.filterLabs()">
                    </div>
                    <div class="filter-options">
                        <select id="labs-status-filter" onchange="externalLabsManager.filterLabs()">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                        <select id="labs-city-filter" onchange="externalLabsManager.filterLabs()">
                            <option value="">جميع المدن</option>
                            ${this.getUniqueCities().map(city => `<option value="${city}">${city}</option>`).join('')}
                        </select>
                    </div>
                </div>

                <div class="labs-grid" id="labs-grid">
                    ${this.labs.map(lab => this.renderLabCard(lab)).join('')}
                </div>
            </div>
        `;
    }

    renderLabCard(lab) {
        return `
            <div class="lab-card" data-lab-id="${lab.id}">
                <div class="lab-card-header">
                    <div class="lab-info">
                        <h3 class="lab-name">${lab.name}</h3>
                        <p class="lab-city">
                            <i class="fas fa-map-marker-alt"></i>
                            ${lab.city}
                        </p>
                    </div>
                    <div class="lab-status">
                        <span class="status-badge ${lab.isActive ? 'status-active' : 'status-inactive'}">
                            ${lab.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </div>
                </div>
                
                <div class="lab-card-body">
                    <div class="lab-contact">
                        <p><i class="fas fa-phone"></i> ${lab.phone || 'غير محدد'}</p>
                        <p><i class="fas fa-envelope"></i> ${lab.email || 'غير محدد'}</p>
                    </div>
                    
                    <div class="lab-stats">
                        <div class="stat-item">
                            <span class="stat-value">${this.getLabOrdersCount(lab.id)}</span>
                            <span class="stat-label">طلب</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${this.formatCurrency(this.getLabTotalAmount(lab.id))}</span>
                            <span class="stat-label">إجمالي</span>
                        </div>
                    </div>
                </div>
                
                <div class="lab-card-footer">
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="externalLabsManager.viewLabDetails(${lab.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="externalLabsManager.editLab(${lab.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="externalLabsManager.createOrder(${lab.id})" title="طلب جديد">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="externalLabsManager.deleteLab(${lab.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // وظائف مساعدة - Helper Functions
    // ========================================

    getUniqueCities() {
        const cities = [...new Set(this.labs.map(lab => lab.city).filter(city => city))];
        return cities.sort();
    }

    getLabOrdersCount(labId) {
        return this.orders.filter(order => order.lab_id === labId).length;
    }

    getLabTotalAmount(labId) {
        return this.orders
            .filter(order => order.lab_id === labId)
            .reduce((total, order) => total + (order.total_amount || 0), 0);
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 0
        }).format(amount || 0);
    }

    getTotalAmount() {
        return this.orders.reduce((total, order) => total + (order.total_amount || 0), 0);
    }

    getAverageOrderAmount() {
        if (this.orders.length === 0) return 0;
        return this.getTotalAmount() / this.orders.length;
    }

    // ========================================
    // إدارة التبويبات - Tab Management
    // ========================================

    switchTab(tabName) {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });

        // تفعيل التبويب المحدد
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}-content`).classList.add('active');

        // تحديث المحتوى حسب التبويب
        if (tabName === 'orders') {
            this.renderOrdersList();
        } else if (tabName === 'contracts') {
            this.renderContracts();
        } else if (tabName === 'payments') {
            this.renderPayments();
        } else if (tabName === 'quality') {
            this.renderQuality();
        } else if (tabName === 'reports') {
            this.renderReports();
        } else if (tabName === 'settings') {
            this.renderSettings();
        }
    }

    // ========================================
    // عرض قائمة الطلبات - Render Orders List
    // ========================================

    renderOrdersList() {
        const content = document.getElementById('orders-content');
        if (!content) return '';

        if (this.orders.length === 0) {
            content.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>لا توجد طلبات</h3>
                    <p>ابدأ بإنشاء طلب جديد</p>
                    <button class="btn btn-primary" onclick="externalLabsManager.showNewOrderModal()">
                        <i class="fas fa-plus"></i>
                        طلب جديد
                    </button>
                </div>
            `;
            return;
        }

        content.innerHTML = `
            <div class="orders-list">
                <div class="search-filter-bar">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="orders-search" placeholder="البحث في الطلبات..." onkeyup="externalLabsManager.filterOrders()">
                    </div>
                    <div class="filter-options">
                        <select id="orders-status-filter" onchange="externalLabsManager.filterOrders()">
                            <option value="">جميع الحالات</option>
                            <option value="pending">معلق</option>
                            <option value="in_progress">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="delivered">تم التسليم</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                        <select id="orders-lab-filter" onchange="externalLabsManager.filterOrders()">
                            <option value="">جميع المعامل</option>
                            ${this.labs.map(lab => `<option value="${lab.id}">${lab.name}</option>`).join('')}
                        </select>
                    </div>
                </div>

                <div class="orders-table-container">
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>المعمل</th>
                                <th>نوع العمل</th>
                                <th>تاريخ الطلب</th>
                                <th>تاريخ التسليم</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table-body">
                            ${this.orders.map(order => this.renderOrderRow(order)).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    renderOrderRow(order) {
        const lab = this.labs.find(l => l.id === order.lab_id);
        const statusClass = this.getStatusClass(order.status);
        const statusText = this.getStatusText(order.status);

        return `
            <tr data-order-id="${order.id}">
                <td>
                    <strong>#${order.order_number}</strong>
                </td>
                <td>
                    <div class="lab-info">
                        <strong>${lab ? lab.name : 'غير محدد'}</strong>
                        <small>${lab ? lab.city : ''}</small>
                    </div>
                </td>
                <td>${order.work_type}</td>
                <td>${this.formatDate(order.order_date)}</td>
                <td>${this.formatDate(order.delivery_date)}</td>
                <td><strong>${this.formatCurrency(order.total_amount)}</strong></td>
                <td>
                    <span class="status-badge ${statusClass}">
                        ${statusText}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="externalLabsManager.viewOrderDetails(${order.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="externalLabsManager.editOrder(${order.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="externalLabsManager.updateOrderStatus(${order.id})" title="تحديث الحالة">
                            <i class="fas fa-sync"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="externalLabsManager.deleteOrder(${order.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // ========================================
    // عرض التقارير - Render Reports
    // ========================================

    renderReports() {
        return `
            <div class="reports-section">
                <div class="reports-toolbar">
                    <h3>تقارير المعامل الخارجية</h3>
                    <div class="report-filters">
                        <input type="date" id="report-start-date" class="form-control">
                        <input type="date" id="report-end-date" class="form-control">
                        <button class="btn btn-primary" onclick="externalLabsManager.generateReport()">
                            <i class="fas fa-chart-bar"></i>
                            إنشاء تقرير
                        </button>
                    </div>
                </div>

                <div class="reports-grid">
                    <div class="report-card">
                        <h4>إحصائيات المعامل</h4>
                        <div class="report-stats">
                            <div class="stat-item">
                                <span class="stat-label">إجمالي المعامل:</span>
                                <span class="stat-value">${this.labs.length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">معامل نشطة:</span>
                                <span class="stat-value">${this.labs.filter(lab => lab.isActive).length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">معامل غير نشطة:</span>
                                <span class="stat-value">${this.labs.filter(lab => !lab.isActive).length}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-card">
                        <h4>إحصائيات الطلبات</h4>
                        <div class="report-stats">
                            <div class="stat-item">
                                <span class="stat-label">إجمالي الطلبات:</span>
                                <span class="stat-value">${this.orders.length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">طلبات معلقة:</span>
                                <span class="stat-value">${this.orders.filter(order => order.status === 'pending').length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">طلبات مكتملة:</span>
                                <span class="stat-value">${this.orders.filter(order => order.status === 'completed').length}</span>
                            </div>
                        </div>
                    </div>

                    <div class="report-card">
                        <h4>الإحصائيات المالية</h4>
                        <div class="report-stats">
                            <div class="stat-item">
                                <span class="stat-label">إجمالي المبالغ:</span>
                                <span class="stat-value">${this.formatCurrency(this.getTotalAmount())}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">متوسط قيمة الطلب:</span>
                                <span class="stat-value">${this.formatCurrency(this.getAverageOrderAmount())}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="orders-chart" width="400" height="200"></canvas>
                </div>
            </div>
        `;
    }

    // ========================================
    // عرض العقود والاتفاقيات - Render Contracts
    // ========================================

    renderContracts() {
        return `
            <div class="contracts-section">
                <div class="section-header">
                    <h3>العقود والاتفاقيات</h3>
                    <button class="btn btn-primary" onclick="externalLabsManager.showNewContractModal()">
                        <i class="fas fa-plus"></i>
                        عقد جديد
                    </button>
                </div>

                <div class="contracts-grid">
                    <div class="contract-card">
                        <div class="contract-header">
                            <h4>عقد التعاون الأساسي</h4>
                            <span class="status-badge status-active">نشط</span>
                        </div>
                        <div class="contract-body">
                            <p><strong>نوع العقد:</strong> تعاون طويل المدى</p>
                            <p><strong>مدة العقد:</strong> سنة واحدة</p>
                            <p><strong>تاريخ البداية:</strong> 2024-01-01</p>
                            <p><strong>تاريخ الانتهاء:</strong> 2024-12-31</p>
                        </div>
                        <div class="contract-footer">
                            <button class="btn btn-sm btn-outline">عرض</button>
                            <button class="btn btn-sm btn-outline">تعديل</button>
                            <button class="btn btn-sm btn-outline">تجديد</button>
                        </div>
                    </div>

                    <div class="contract-card">
                        <div class="contract-header">
                            <h4>اتفاقية الأسعار الخاصة</h4>
                            <span class="status-badge status-warning">ينتهي قريباً</span>
                        </div>
                        <div class="contract-body">
                            <p><strong>نوع العقد:</strong> تسعير خاص</p>
                            <p><strong>خصم متفق عليه:</strong> 15%</p>
                            <p><strong>الحد الأدنى للطلب:</strong> 10 قطع</p>
                            <p><strong>تاريخ الانتهاء:</strong> 2024-03-31</p>
                        </div>
                        <div class="contract-footer">
                            <button class="btn btn-sm btn-outline">عرض</button>
                            <button class="btn btn-sm btn-outline">تعديل</button>
                            <button class="btn btn-sm btn-primary">تجديد</button>
                        </div>
                    </div>
                </div>

                <div class="contract-templates">
                    <h4>قوالب العقود</h4>
                    <div class="template-list">
                        <div class="template-item">
                            <i class="fas fa-file-contract"></i>
                            <span>عقد تعاون أساسي</span>
                            <button class="btn btn-sm btn-outline">استخدام</button>
                        </div>
                        <div class="template-item">
                            <i class="fas fa-percentage"></i>
                            <span>اتفاقية خصومات</span>
                            <button class="btn btn-sm btn-outline">استخدام</button>
                        </div>
                        <div class="template-item">
                            <i class="fas fa-clock"></i>
                            <span>عقد مدة محددة</span>
                            <button class="btn btn-sm btn-outline">استخدام</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // عرض المدفوعات - Render Payments
    // ========================================

    renderPayments() {
        return `
            <div class="payments-section">
                <div class="section-header">
                    <h3>إدارة المدفوعات</h3>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="externalLabsManager.showNewPaymentModal()">
                            <i class="fas fa-plus"></i>
                            دفعة جديدة
                        </button>
                        <button class="btn btn-outline" onclick="externalLabsManager.generatePaymentReport()">
                            <i class="fas fa-file-invoice"></i>
                            تقرير المدفوعات
                        </button>
                    </div>
                </div>

                <div class="payment-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-value">${this.formatCurrency(125000)}</div>
                            <div class="summary-label">إجمالي المدفوعات</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-value">${this.formatCurrency(35000)}</div>
                            <div class="summary-label">مدفوعات معلقة</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-calendar-month"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-value">${this.formatCurrency(18000)}</div>
                            <div class="summary-label">مدفوعات هذا الشهر</div>
                        </div>
                    </div>
                </div>

                <div class="payments-table-container">
                    <table class="payments-table">
                        <thead>
                            <tr>
                                <th>رقم الدفعة</th>
                                <th>المعمل</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>#PAY-2024-001</strong></td>
                                <td>معمل الأسنان المتقدم</td>
                                <td><strong>${this.formatCurrency(15000)}</strong></td>
                                <td>تحويل بنكي</td>
                                <td>2024-01-15</td>
                                <td><span class="status-badge status-success">مكتمل</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>#PAY-2024-002</strong></td>
                                <td>مختبر الابتسامة</td>
                                <td><strong>${this.formatCurrency(8500)}</strong></td>
                                <td>نقداً</td>
                                <td>2024-01-18</td>
                                <td><span class="status-badge status-warning">معلق</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-success" title="تأكيد">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="payment-methods">
                    <h4>طرق الدفع المتاحة</h4>
                    <div class="methods-grid">
                        <div class="method-card">
                            <i class="fas fa-university"></i>
                            <h5>تحويل بنكي</h5>
                            <p>التحويل المباشر للحساب البنكي</p>
                        </div>
                        <div class="method-card">
                            <i class="fas fa-money-bill"></i>
                            <h5>نقداً</h5>
                            <p>الدفع النقدي المباشر</p>
                        </div>
                        <div class="method-card">
                            <i class="fas fa-credit-card"></i>
                            <h5>بطاقة ائتمان</h5>
                            <p>الدفع بالبطاقة الائتمانية</p>
                        </div>
                        <div class="method-card">
                            <i class="fas fa-file-invoice"></i>
                            <h5>شيك</h5>
                            <p>الدفع بالشيك المصرفي</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // عرض تقييم الجودة - Render Quality
    // ========================================

    renderQuality() {
        return `
            <div class="quality-section">
                <div class="section-header">
                    <h3>تقييم جودة المعامل</h3>
                    <button class="btn btn-primary" onclick="externalLabsManager.showNewEvaluationModal()">
                        <i class="fas fa-plus"></i>
                        تقييم جديد
                    </button>
                </div>

                <div class="quality-overview">
                    <div class="quality-card">
                        <div class="quality-header">
                            <h4>متوسط التقييم العام</h4>
                            <div class="overall-rating">
                                <span class="rating-value">4.2</span>
                                <div class="rating-stars">
                                    ${'⭐'.repeat(4)}⭐
                                </div>
                            </div>
                        </div>
                        <div class="quality-metrics">
                            <div class="metric-item">
                                <span class="metric-label">جودة العمل:</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 85%"></div>
                                </div>
                                <span class="metric-value">4.3/5</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">الالتزام بالمواعيد:</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 78%"></div>
                                </div>
                                <span class="metric-value">3.9/5</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">التواصل:</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 90%"></div>
                                </div>
                                <span class="metric-value">4.5/5</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">الأسعار:</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 82%"></div>
                                </div>
                                <span class="metric-value">4.1/5</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="labs-quality-list">
                    <h4>تقييم المعامل الفردية</h4>
                    <div class="quality-labs-grid">
                        ${this.labs.map(lab => `
                            <div class="lab-quality-card">
                                <div class="lab-quality-header">
                                    <h5>${lab.name}</h5>
                                    <div class="lab-rating">
                                        <span class="rating-value">${lab.quality_rating || 5}</span>
                                        <div class="rating-stars">
                                            ${'⭐'.repeat(lab.quality_rating || 5)}
                                        </div>
                                    </div>
                                </div>
                                <div class="lab-quality-details">
                                    <div class="quality-stat">
                                        <span>عدد التقييمات:</span>
                                        <strong>${Math.floor(Math.random() * 20) + 5}</strong>
                                    </div>
                                    <div class="quality-stat">
                                        <span>آخر تقييم:</span>
                                        <strong>منذ ${Math.floor(Math.random() * 30) + 1} يوم</strong>
                                    </div>
                                </div>
                                <div class="lab-quality-actions">
                                    <button class="btn btn-sm btn-outline" onclick="externalLabsManager.viewLabEvaluations(${lab.id})">
                                        عرض التقييمات
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="externalLabsManager.evaluateLab(${lab.id})">
                                        تقييم جديد
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="evaluation-criteria">
                    <h4>معايير التقييم</h4>
                    <div class="criteria-list">
                        <div class="criteria-item">
                            <i class="fas fa-gem"></i>
                            <div class="criteria-content">
                                <h5>جودة العمل</h5>
                                <p>دقة التنفيذ ومطابقة المواصفات</p>
                            </div>
                        </div>
                        <div class="criteria-item">
                            <i class="fas fa-clock"></i>
                            <div class="criteria-content">
                                <h5>الالتزام بالمواعيد</h5>
                                <p>تسليم العمل في الوقت المحدد</p>
                            </div>
                        </div>
                        <div class="criteria-item">
                            <i class="fas fa-comments"></i>
                            <div class="criteria-content">
                                <h5>التواصل</h5>
                                <p>سرعة الرد ووضوح التواصل</p>
                            </div>
                        </div>
                        <div class="criteria-item">
                            <i class="fas fa-dollar-sign"></i>
                            <div class="criteria-content">
                                <h5>الأسعار</h5>
                                <p>تنافسية الأسعار وشفافيتها</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // عرض الإعدادات - Render Settings
    // ========================================

    renderSettings() {
        return `
            <div class="settings-section">
                <div class="section-header">
                    <h3>إعدادات المعامل الخارجية</h3>
                    <button class="btn btn-primary" onclick="externalLabsManager.saveSettings()">
                        <i class="fas fa-save"></i>
                        حفظ الإعدادات
                    </button>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <h4><i class="fas fa-cog"></i> الإعدادات العامة</h4>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" checked>
                                <span>إرسال تنبيهات عند انتهاء العقود</span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" checked>
                                <span>تأكيد الطلبات تلقائياً</span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox">
                                <span>إرسال تقييم تلقائي بعد كل طلب</span>
                            </label>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4><i class="fas fa-bell"></i> إعدادات التنبيهات</h4>
                        <div class="setting-item">
                            <label class="setting-label">تنبيه قبل انتهاء العقد بـ:</label>
                            <select class="form-control">
                                <option value="7">7 أيام</option>
                                <option value="15" selected>15 يوم</option>
                                <option value="30">30 يوم</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">تنبيه تأخير التسليم بعد:</label>
                            <select class="form-control">
                                <option value="1">يوم واحد</option>
                                <option value="2" selected>يومين</option>
                                <option value="3">3 أيام</option>
                            </select>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4><i class="fas fa-money-bill"></i> إعدادات المدفوعات</h4>
                        <div class="setting-item">
                            <label class="setting-label">طريقة الدفع الافتراضية:</label>
                            <select class="form-control">
                                <option value="bank">تحويل بنكي</option>
                                <option value="cash" selected>نقداً</option>
                                <option value="card">بطاقة ائتمان</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">مدة الدفع الافتراضية:</label>
                            <select class="form-control">
                                <option value="immediate">فوري</option>
                                <option value="7" selected>7 أيام</option>
                                <option value="15">15 يوم</option>
                                <option value="30">30 يوم</option>
                            </select>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4><i class="fas fa-file-export"></i> إعدادات التصدير</h4>
                        <div class="setting-item">
                            <label class="setting-label">تنسيق التصدير الافتراضي:</label>
                            <select class="form-control">
                                <option value="excel" selected>Excel</option>
                                <option value="pdf">PDF</option>
                                <option value="csv">CSV</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" checked>
                                <span>تضمين المعلومات المالية في التصدير</span>
                            </label>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4><i class="fas fa-database"></i> إعدادات النسخ الاحتياطي</h4>
                        <div class="setting-item">
                            <label class="setting-label">تكرار النسخ الاحتياطي:</label>
                            <select class="form-control">
                                <option value="daily" selected>يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly">شهري</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" checked>
                                <span>نسخ احتياطي تلقائي</span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-outline">
                                <i class="fas fa-download"></i>
                                إنشاء نسخة احتياطية الآن
                            </button>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h4><i class="fas fa-shield-alt"></i> إعدادات الأمان</h4>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" checked>
                                <span>تسجيل جميع العمليات</span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox">
                                <span>تشفير البيانات الحساسة</span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-outline">
                                <i class="fas fa-eye"></i>
                                عرض سجل العمليات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // وظائف مساعدة للتقارير - Report Helper Functions
    // ========================================

    formatDate(dateString) {
        if (!dateString) return 'غير محدد';

        const date = new Date(dateString);
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(date);
    }

    getStatusText(status) {
        const statusMap = {
            'pending': 'معلق',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'delivered': 'تم التسليم',
            'cancelled': 'ملغي'
        };
        return statusMap[status] || status;
    }

    getStatusClass(status) {
        const classMap = {
            'pending': 'status-warning',
            'in_progress': 'status-info',
            'completed': 'status-success',
            'delivered': 'status-success',
            'cancelled': 'status-danger'
        };
        return classMap[status] || 'status-secondary';
    }

    getWorkTypeText(workType) {
        const typeMap = {
            'crown': 'تاج',
            'bridge': 'جسر',
            'denture': 'طقم أسنان',
            'orthodontic': 'تقويم',
            'implant': 'زراعة',
            'veneer': 'فينير',
            'inlay': 'حشوة تجميلية',
            'onlay': 'تركيبة جزئية'
        };
        return typeMap[workType] || workType;
    }

    getTotalAmount() {
        return this.orders.reduce((total, order) => total + (order.total_amount || 0), 0);
    }

    getAverageOrderAmount() {
        if (this.orders.length === 0) return 0;
        return this.getTotalAmount() / this.orders.length;
    }

    formatDate(date) {
        if (!date) return 'غير محدد';
        return new Date(date).toLocaleDateString('ar-SA');
    }

    getStatusClass(status) {
        const statusClasses = {
            'pending': 'status-warning',
            'in_progress': 'status-info',
            'completed': 'status-success',
            'delivered': 'status-success',
            'cancelled': 'status-danger'
        };
        return statusClasses[status] || 'status-secondary';
    }

    getStatusText(status) {
        const statusTexts = {
            'pending': 'معلق',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'delivered': 'تم التسليم',
            'cancelled': 'ملغي'
        };
        return statusTexts[status] || 'غير محدد';
    }

    // ========================================
    // النوافذ المنبثقة - Modals
    // ========================================

    renderModals() {
        return `
            <!-- نافذة إضافة معمل جديد -->
            <div id="new-lab-modal" class="modal hidden">
                <div class="modal-overlay" onclick="externalLabsManager.closeNewLabModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>إضافة معمل خارجي جديد</h2>
                        <button class="modal-close" onclick="externalLabsManager.closeNewLabModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="new-lab-form" class="form-grid">
                            <div class="form-group">
                                <label class="form-label">اسم المعمل *</label>
                                <input type="text" id="lab-name" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">المدينة *</label>
                                <input type="text" id="lab-city" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">العنوان</label>
                                <textarea id="lab-address" class="form-control" rows="3"></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" id="lab-phone" class="form-control">
                            </div>

                            <div class="form-group">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" id="lab-email" class="form-control">
                            </div>

                            <div class="form-group">
                                <label class="form-label">اسم المسؤول</label>
                                <input type="text" id="lab-contact-person" class="form-control">
                            </div>

                            <div class="form-group">
                                <label class="form-label">التخصصات</label>
                                <textarea id="lab-specialties" class="form-control" rows="2" placeholder="مثال: تركيبات ثابتة، تركيبات متحركة، تقويم"></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label">مدة التسليم (بالأيام)</label>
                                <input type="number" id="lab-delivery-time" class="form-control" min="1" value="7">
                            </div>

                            <div class="form-group">
                                <label class="form-label">تقييم الجودة</label>
                                <select id="lab-quality-rating" class="form-control">
                                    <option value="5">ممتاز (5 نجوم)</option>
                                    <option value="4">جيد جداً (4 نجوم)</option>
                                    <option value="3">جيد (3 نجوم)</option>
                                    <option value="2">مقبول (2 نجوم)</option>
                                    <option value="1">ضعيف (1 نجمة)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">شروط الدفع</label>
                                <select id="lab-payment-terms" class="form-control">
                                    <option value="cash">نقداً</option>
                                    <option value="credit_7">آجل 7 أيام</option>
                                    <option value="credit_15">آجل 15 يوم</option>
                                    <option value="credit_30">آجل 30 يوم</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">الحد الائتماني</label>
                                <input type="number" id="lab-credit-limit" class="form-control" min="0" step="0.01">
                            </div>

                            <div class="form-group full-width">
                                <label class="form-label">ملاحظات</label>
                                <textarea id="lab-notes" class="form-control" rows="3"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="lab-active" checked>
                                    <span class="checkbox-text">معمل نشط</span>
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="externalLabsManager.saveLab()">
                            <i class="fas fa-save"></i>
                            حفظ
                        </button>
                        <button class="btn btn-secondary" onclick="externalLabsManager.closeNewLabModal()">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة طلب جديد -->
            <div id="new-order-modal" class="modal hidden">
                <div class="modal-overlay" onclick="externalLabsManager.closeNewOrderModal()"></div>
                <div class="modal-content large-modal">
                    <div class="modal-header">
                        <h2>طلب جديد للمعمل الخارجي</h2>
                        <button class="modal-close" onclick="externalLabsManager.closeNewOrderModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="new-order-form" class="form-grid">
                            <div class="form-group">
                                <label class="form-label">المعمل الخارجي *</label>
                                <select id="order-lab" class="form-control" required>
                                    <option value="">اختر المعمل</option>
                                    ${this.labs.filter(lab => lab.isActive).map(lab =>
                                        `<option value="${lab.id}">${lab.name} - ${lab.city}</option>`
                                    ).join('')}
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">رقم الطلب</label>
                                <input type="text" id="order-number" class="form-control" readonly value="${this.generateOrderNumber()}">
                            </div>

                            <div class="form-group">
                                <label class="form-label">نوع العمل *</label>
                                <select id="order-work-type" class="form-control" required>
                                    <option value="">اختر نوع العمل</option>
                                    <option value="crown">تاج</option>
                                    <option value="bridge">جسر</option>
                                    <option value="denture">طقم أسنان</option>
                                    <option value="implant">زراعة</option>
                                    <option value="orthodontic">تقويم</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">اسم المريض</label>
                                <input type="text" id="order-patient-name" class="form-control">
                            </div>

                            <div class="form-group">
                                <label class="form-label">اسم الطبيب</label>
                                <input type="text" id="order-doctor-name" class="form-control">
                            </div>

                            <div class="form-group">
                                <label class="form-label">تاريخ الطلب</label>
                                <input type="date" id="order-date" class="form-control" value="${new Date().toISOString().split('T')[0]}">
                            </div>

                            <div class="form-group">
                                <label class="form-label">تاريخ التسليم المطلوب</label>
                                <input type="date" id="order-delivery-date" class="form-control">
                            </div>

                            <div class="form-group">
                                <label class="form-label">الكمية</label>
                                <input type="number" id="order-quantity" class="form-control" min="1" value="1">
                            </div>

                            <div class="form-group">
                                <label class="form-label">سعر الوحدة</label>
                                <input type="number" id="order-unit-price" class="form-control" min="0" step="0.01">
                            </div>

                            <div class="form-group">
                                <label class="form-label">المبلغ الإجمالي</label>
                                <input type="number" id="order-total-amount" class="form-control" min="0" step="0.01" readonly>
                            </div>

                            <div class="form-group full-width">
                                <label class="form-label">وصف العمل</label>
                                <textarea id="order-description" class="form-control" rows="3"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label class="form-label">ملاحظات خاصة</label>
                                <textarea id="order-notes" class="form-control" rows="2"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="externalLabsManager.saveOrder()">
                            <i class="fas fa-save"></i>
                            حفظ الطلب
                        </button>
                        <button class="btn btn-secondary" onclick="externalLabsManager.closeNewOrderModal()">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إدارة النوافذ المنبثقة - Modal Management
    // ========================================

    showNewLabModal() {
        const modal = document.getElementById('new-lab-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.resetNewLabForm();
        }
    }

    closeNewLabModal() {
        const modal = document.getElementById('new-lab-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    showNewOrderModal(labId = null) {
        const modal = document.getElementById('new-order-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.resetNewOrderForm();

            if (labId) {
                const labSelect = document.getElementById('order-lab');
                if (labSelect) {
                    labSelect.value = labId;
                }
            }
        }
    }

    closeNewOrderModal() {
        const modal = document.getElementById('new-order-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    resetNewLabForm() {
        const form = document.getElementById('new-lab-form');
        if (form) {
            form.reset();
            document.getElementById('lab-active').checked = true;
            document.getElementById('lab-delivery-time').value = 7;
            document.getElementById('lab-quality-rating').value = 5;
        }
    }

    resetNewOrderForm() {
        const form = document.getElementById('new-order-form');
        if (form) {
            form.reset();
            document.getElementById('order-number').value = this.generateOrderNumber();
            document.getElementById('order-date').value = new Date().toISOString().split('T')[0];
            document.getElementById('order-quantity').value = 1;
        }
    }

    generateOrderNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `EXT-${year}${month}${day}-${random}`;
    }

    // ========================================
    // العمليات الأساسية - Basic Operations
    // ========================================

    async saveLab() {
        try {
            const formData = this.getLabFormData();
            if (!formData) return;

            // إضافة معرف فريد وتاريخ الإنشاء
            formData.id = Date.now();
            formData.created_at = new Date().toISOString();
            formData.updated_at = new Date().toISOString();

            // حفظ في قاعدة البيانات
            await db.insert('external_labs', formData);

            // إضافة للقائمة المحلية
            this.labs.push(formData);

            // تحديث الواجهة
            this.updateStats();
            this.renderLabsList();

            // إغلاق النافذة وإظهار رسالة نجاح
            this.closeNewLabModal();
            showSuccess('تم إضافة المعمل الخارجي بنجاح');
        } catch (error) {
            console.error('خطأ في حفظ المعمل:', error);
            showError('فشل في حفظ المعمل الخارجي');
        }
    }

    async saveOrder() {
        try {
            const formData = this.getOrderFormData();
            if (!formData) return;

            // إضافة معرف فريد وتاريخ الإنشاء
            formData.id = Date.now();
            formData.status = 'pending';
            formData.created_at = new Date().toISOString();
            formData.updated_at = new Date().toISOString();

            // حفظ في قاعدة البيانات
            await db.insert('external_orders', formData);

            // إضافة للقائمة المحلية
            this.orders.push(formData);

            // تحديث الواجهة
            this.updateStats();
            if (this.currentView === 'orders') {
                this.renderOrdersList();
            }

            // إغلاق النافذة وإظهار رسالة نجاح
            this.closeNewOrderModal();
            showSuccess('تم إنشاء الطلب بنجاح');
        } catch (error) {
            console.error('خطأ في حفظ الطلب:', error);
            showError('فشل في حفظ الطلب');
        }
    }

    getLabFormData() {
        const name = document.getElementById('lab-name').value.trim();
        const city = document.getElementById('lab-city').value.trim();

        if (!name || !city) {
            showError('يرجى ملء الحقول المطلوبة');
            return null;
        }

        return {
            name: name,
            city: city,
            address: document.getElementById('lab-address').value.trim(),
            phone: document.getElementById('lab-phone').value.trim(),
            email: document.getElementById('lab-email').value.trim(),
            contact_person: document.getElementById('lab-contact-person').value.trim(),
            specialties: document.getElementById('lab-specialties').value.trim(),
            delivery_time: parseInt(document.getElementById('lab-delivery-time').value) || 7,
            quality_rating: parseInt(document.getElementById('lab-quality-rating').value) || 5,
            payment_terms: document.getElementById('lab-payment-terms').value,
            credit_limit: parseFloat(document.getElementById('lab-credit-limit').value) || 0,
            notes: document.getElementById('lab-notes').value.trim(),
            isActive: document.getElementById('lab-active').checked
        };
    }

    getOrderFormData() {
        const labId = document.getElementById('order-lab').value;
        const workType = document.getElementById('order-work-type').value;
        const quantity = parseInt(document.getElementById('order-quantity').value);
        const unitPrice = parseFloat(document.getElementById('order-unit-price').value);

        if (!labId || !workType || !quantity || !unitPrice) {
            showError('يرجى ملء الحقول المطلوبة');
            return null;
        }

        const totalAmount = quantity * unitPrice;

        return {
            lab_id: parseInt(labId),
            order_number: document.getElementById('order-number').value,
            work_type: workType,
            patient_name: document.getElementById('order-patient-name').value.trim(),
            doctor_name: document.getElementById('order-doctor-name').value.trim(),
            order_date: document.getElementById('order-date').value,
            delivery_date: document.getElementById('order-delivery-date').value,
            quantity: quantity,
            unit_price: unitPrice,
            total_amount: totalAmount,
            description: document.getElementById('order-description').value.trim(),
            notes: document.getElementById('order-notes').value.trim()
        };
    }

    // ========================================
    // عرض التفاصيل - View Details
    // ========================================

    async viewLabDetails(labId) {
        try {
            const lab = this.labs.find(l => l.id === labId);
            if (!lab) {
                showError('المعمل غير موجود');
                return;
            }

            const modal = this.createLabDetailsModal(lab);
            document.body.appendChild(modal);
            modal.classList.remove('hidden');
        } catch (error) {
            console.error('خطأ في عرض تفاصيل المعمل:', error);
            showError('فشل في عرض تفاصيل المعمل');
        }
    }

    async viewOrderDetails(orderId) {
        try {
            const order = this.orders.find(o => o.id === orderId);
            if (!order) {
                showError('الطلب غير موجود');
                return;
            }

            const modal = this.createOrderDetailsModal(order);
            document.body.appendChild(modal);
            modal.classList.remove('hidden');
        } catch (error) {
            console.error('خطأ في عرض تفاصيل الطلب:', error);
            showError('فشل في عرض تفاصيل الطلب');
        }
    }

    // ========================================
    // التعديل والحذف - Edit and Delete
    // ========================================

    async editLab(labId) {
        try {
            const lab = this.labs.find(l => l.id === labId);
            if (!lab) {
                showError('المعمل غير موجود');
                return;
            }

            // ملء النموذج ببيانات المعمل
            this.fillLabEditForm(lab);

            // إظهار نافذة التعديل
            const modal = document.getElementById('new-lab-modal');
            if (modal) {
                modal.classList.remove('hidden');

                // تغيير عنوان النافذة
                const title = modal.querySelector('.modal-header h2');
                if (title) title.textContent = 'تعديل بيانات المعمل';

                // تغيير نص الزر
                const saveBtn = modal.querySelector('.btn-primary');
                if (saveBtn) {
                    saveBtn.textContent = 'حفظ التعديلات';
                    saveBtn.onclick = () => this.updateLab(labId);
                }
            }
        } catch (error) {
            console.error('خطأ في تعديل المعمل:', error);
            showError('فشل في تحميل بيانات المعمل');
        }
    }

    async deleteLab(labId) {
        if (!confirm('هل أنت متأكد من حذف هذا المعمل؟\nسيتم حذف جميع الطلبات المرتبطة به أيضاً.')) {
            return;
        }

        try {
            // حذف من قاعدة البيانات
            await db.delete('external_labs', labId);

            // حذف الطلبات المرتبطة
            const relatedOrders = this.orders.filter(order => order.lab_id === labId);
            for (const order of relatedOrders) {
                await db.delete('external_orders', order.id);
            }

            // حذف من القوائم المحلية
            this.labs = this.labs.filter(lab => lab.id !== labId);
            this.orders = this.orders.filter(order => order.lab_id !== labId);

            // تحديث الواجهة
            this.updateStats();
            this.renderLabsList();

            showSuccess('تم حذف المعمل بنجاح');
        } catch (error) {
            console.error('خطأ في حذف المعمل:', error);
            showError('فشل في حذف المعمل');
        }
    }

    // ========================================
    // تحديث الإحصائيات - Update Statistics
    // ========================================

    updateStats() {
        const totalLabsEl = document.getElementById('total-labs');
        const activeLabsEl = document.getElementById('active-labs');
        const totalOrdersEl = document.getElementById('total-orders');
        const pendingOrdersEl = document.getElementById('pending-orders');

        if (totalLabsEl) totalLabsEl.textContent = this.labs.length;
        if (activeLabsEl) activeLabsEl.textContent = this.labs.filter(lab => lab.isActive).length;
        if (totalOrdersEl) totalOrdersEl.textContent = this.orders.length;
        if (pendingOrdersEl) pendingOrdersEl.textContent = this.orders.filter(order => order.status === 'pending').length;
    }

    // ========================================
    // إعداد معالجات الأحداث - Setup Event Handlers
    // ========================================

    setupEventHandlers() {
        // معالج تحديث المبلغ الإجمالي في نموذج الطلب
        document.addEventListener('input', (e) => {
            if (e.target.id === 'order-quantity' || e.target.id === 'order-unit-price') {
                this.updateOrderTotal();
            }
        });
    }

    updateOrderTotal() {
        const quantity = parseFloat(document.getElementById('order-quantity').value) || 0;
        const unitPrice = parseFloat(document.getElementById('order-unit-price').value) || 0;
        const total = quantity * unitPrice;

        const totalField = document.getElementById('order-total-amount');
        if (totalField) {
            totalField.value = total.toFixed(2);
        }
    }

    // ========================================
    // التصفية والبحث - Filtering and Search
    // ========================================

    filterLabs() {
        const searchTerm = document.getElementById('labs-search').value.toLowerCase();
        const statusFilter = document.getElementById('labs-status-filter').value;
        const cityFilter = document.getElementById('labs-city-filter').value;

        let filteredLabs = this.labs;

        // تصفية حسب النص
        if (searchTerm) {
            filteredLabs = filteredLabs.filter(lab =>
                lab.name.toLowerCase().includes(searchTerm) ||
                lab.city.toLowerCase().includes(searchTerm) ||
                (lab.contact_person && lab.contact_person.toLowerCase().includes(searchTerm))
            );
        }

        // تصفية حسب الحالة
        if (statusFilter) {
            filteredLabs = filteredLabs.filter(lab => {
                if (statusFilter === 'active') return lab.isActive;
                if (statusFilter === 'inactive') return !lab.isActive;
                return true;
            });
        }

        // تصفية حسب المدينة
        if (cityFilter) {
            filteredLabs = filteredLabs.filter(lab => lab.city === cityFilter);
        }

        // تحديث العرض
        this.renderFilteredLabs(filteredLabs);
    }

    renderFilteredLabs(labs) {
        const grid = document.getElementById('labs-grid');
        if (!grid) return;

        if (labs.length === 0) {
            grid.innerHTML = `
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد نتائج</h3>
                    <p>لم يتم العثور على معامل تطابق معايير البحث</p>
                </div>
            `;
        } else {
            grid.innerHTML = labs.map(lab => this.renderLabCard(lab)).join('');
        }
    }

    filterOrders() {
        const searchTerm = document.getElementById('orders-search').value.toLowerCase();
        const statusFilter = document.getElementById('orders-status-filter').value;
        const labFilter = document.getElementById('orders-lab-filter').value;

        let filteredOrders = this.orders;

        // تصفية حسب النص
        if (searchTerm) {
            filteredOrders = filteredOrders.filter(order =>
                order.order_number.toLowerCase().includes(searchTerm) ||
                order.work_type.toLowerCase().includes(searchTerm) ||
                (order.patient_name && order.patient_name.toLowerCase().includes(searchTerm)) ||
                (order.doctor_name && order.doctor_name.toLowerCase().includes(searchTerm))
            );
        }

        // تصفية حسب الحالة
        if (statusFilter) {
            filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
        }

        // تصفية حسب المعمل
        if (labFilter) {
            filteredOrders = filteredOrders.filter(order => order.lab_id == labFilter);
        }

        // تحديث العرض
        this.renderFilteredOrders(filteredOrders);
    }

    renderFilteredOrders(orders) {
        const tbody = document.getElementById('orders-table-body');
        if (!tbody) return;

        if (orders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 2rem;">
                        <div class="empty-state">
                            <i class="fas fa-search"></i>
                            <h3>لا توجد نتائج</h3>
                            <p>لم يتم العثور على طلبات تطابق معايير البحث</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            tbody.innerHTML = orders.map(order => this.renderOrderRow(order)).join('');
        }
    }

    // ========================================
    // إنشاء النوافذ التفصيلية - Create Detail Modals
    // ========================================

    createLabDetailsModal(lab) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'lab-details-modal';

        const labOrders = this.orders.filter(order => order.lab_id === lab.id);
        const totalAmount = labOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);

        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2>تفاصيل المعمل - ${lab.name}</h2>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="lab-details-content">
                        <div class="details-section">
                            <h3><i class="fas fa-building"></i> المعلومات الأساسية</h3>
                            <div class="details-grid">
                                <div class="detail-item">
                                    <label>اسم المعمل:</label>
                                    <span>${lab.name}</span>
                                </div>
                                <div class="detail-item">
                                    <label>المدينة:</label>
                                    <span>${lab.city}</span>
                                </div>
                                <div class="detail-item">
                                    <label>العنوان:</label>
                                    <span>${lab.address || 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>الهاتف:</label>
                                    <span>${lab.phone || 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>البريد الإلكتروني:</label>
                                    <span>${lab.email || 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>المسؤول:</label>
                                    <span>${lab.contact_person || 'غير محدد'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="details-section">
                            <h3><i class="fas fa-cogs"></i> معلومات العمل</h3>
                            <div class="details-grid">
                                <div class="detail-item">
                                    <label>التخصصات:</label>
                                    <span>${lab.specialties || 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>مدة التسليم:</label>
                                    <span>${lab.delivery_time} أيام</span>
                                </div>
                                <div class="detail-item">
                                    <label>تقييم الجودة:</label>
                                    <span>${'⭐'.repeat(lab.quality_rating)} (${lab.quality_rating}/5)</span>
                                </div>
                                <div class="detail-item">
                                    <label>شروط الدفع:</label>
                                    <span>${this.getPaymentTermsText(lab.payment_terms)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>الحد الائتماني:</label>
                                    <span>${this.formatCurrency(lab.credit_limit)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>الحالة:</label>
                                    <span class="status-badge ${lab.isActive ? 'status-active' : 'status-inactive'}">
                                        ${lab.isActive ? 'نشط' : 'غير نشط'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="details-section">
                            <h3><i class="fas fa-chart-bar"></i> الإحصائيات</h3>
                            <div class="stats-row">
                                <div class="stat-box">
                                    <div class="stat-value">${labOrders.length}</div>
                                    <div class="stat-label">إجمالي الطلبات</div>
                                </div>
                                <div class="stat-box">
                                    <div class="stat-value">${this.formatCurrency(totalAmount)}</div>
                                    <div class="stat-label">إجمالي المبالغ</div>
                                </div>
                                <div class="stat-box">
                                    <div class="stat-value">${labOrders.filter(o => o.status === 'pending').length}</div>
                                    <div class="stat-label">طلبات معلقة</div>
                                </div>
                                <div class="stat-box">
                                    <div class="stat-value">${labOrders.filter(o => o.status === 'completed').length}</div>
                                    <div class="stat-label">طلبات مكتملة</div>
                                </div>
                            </div>
                        </div>

                        ${lab.notes ? `
                        <div class="details-section">
                            <h3><i class="fas fa-sticky-note"></i> ملاحظات</h3>
                            <p class="notes-text">${lab.notes}</p>
                        </div>
                        ` : ''}

                        <div class="details-section">
                            <h3><i class="fas fa-history"></i> آخر الطلبات</h3>
                            ${labOrders.length > 0 ? `
                                <div class="recent-orders">
                                    ${labOrders.slice(-5).map(order => `
                                        <div class="order-item">
                                            <div class="order-info">
                                                <strong>#${order.order_number}</strong>
                                                <span>${order.work_type}</span>
                                            </div>
                                            <div class="order-meta">
                                                <span>${this.formatDate(order.order_date)}</span>
                                                <span class="status-badge ${this.getStatusClass(order.status)}">
                                                    ${this.getStatusText(order.status)}
                                                </span>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            ` : '<p>لا توجد طلبات</p>'}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="externalLabsManager.editLab(${lab.id}); this.closest('.modal').remove();">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-outline" onclick="externalLabsManager.createOrder(${lab.id}); this.closest('.modal').remove();">
                        <i class="fas fa-plus"></i>
                        طلب جديد
                    </button>
                    <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        return modal;
    }

    getPaymentTermsText(terms) {
        const termsMap = {
            'cash': 'نقداً',
            'credit_7': 'آجل 7 أيام',
            'credit_15': 'آجل 15 يوم',
            'credit_30': 'آجل 30 يوم'
        };
        return termsMap[terms] || 'غير محدد';
    }

    // ========================================
    // وظائف إضافية - Additional Functions
    // ========================================

    async createOrder(labId) {
        this.showNewOrderModal(labId);
    }

    async updateOrderStatus(orderId) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        showInfo('تحديث حالة الطلب - قيد التطوير');
    }

    async editOrder(orderId) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        showInfo('تعديل الطلب - قيد التطوير');
    }

    async deleteOrder(orderId) {
        if (!confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
            return;
        }

        try {
            await db.delete('external_orders', orderId);
            this.orders = this.orders.filter(order => order.id !== orderId);
            this.updateStats();
            this.renderOrdersList();
            showSuccess('تم حذف الطلب بنجاح');
        } catch (error) {
            console.error('خطأ في حذف الطلب:', error);
            showError('فشل في حذف الطلب');
        }
    }

    async exportData() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        showInfo('تصدير البيانات - قيد التطوير');
    }

    async generateReport() {
        // سيتم تطوير هذه الوظيفة لاحقاً
        showInfo('إنشاء التقرير - قيد التطوير');
    }

    fillLabEditForm(lab) {
        const fields = {
            'lab-name': lab.name,
            'lab-city': lab.city,
            'lab-address': lab.address,
            'lab-phone': lab.phone,
            'lab-email': lab.email,
            'lab-contact-person': lab.contact_person,
            'lab-specialties': lab.specialties,
            'lab-delivery-time': lab.delivery_time,
            'lab-quality-rating': lab.quality_rating,
            'lab-payment-terms': lab.payment_terms,
            'lab-credit-limit': lab.credit_limit,
            'lab-notes': lab.notes
        };

        Object.entries(fields).forEach(([fieldId, value]) => {
            const field = document.getElementById(fieldId);
            if (field && value !== null && value !== undefined) {
                field.value = value;
            }
        });

        const activeCheckbox = document.getElementById('lab-active');
        if (activeCheckbox) {
            activeCheckbox.checked = lab.isActive;
        }
    }

    async updateLab(labId) {
        try {
            const formData = this.getLabFormData();
            if (!formData) return;

            formData.updated_at = new Date().toISOString();

            await db.update('external_labs', labId, formData);

            // تحديث القائمة المحلية
            const labIndex = this.labs.findIndex(lab => lab.id === labId);
            if (labIndex !== -1) {
                this.labs[labIndex] = { ...this.labs[labIndex], ...formData };
            }

            this.updateStats();
            this.renderLabsList();
            this.closeNewLabModal();
            showSuccess('تم تحديث بيانات المعمل بنجاح');
        } catch (error) {
            console.error('خطأ في تحديث المعمل:', error);
            showError('فشل في تحديث بيانات المعمل');
        }
    }

    async refreshData() {
        await this.loadData();
        this.updateStats();
        this.renderMainInterface();
    }

    // ========================================
    // إعادة تعيين البيانات التجريبية - Reset Sample Data
    // ========================================

    async resetSampleData() {
        try {
            // حذف البيانات الحالية
            await db.clear('external_labs');
            await db.clear('external_orders');

            // إنشاء بيانات تجريبية جديدة
            await this.createSampleData();

            // تحديث الواجهة
            this.renderMainInterface();

            showSuccess('تم إعادة تعيين البيانات التجريبية بنجاح');
        } catch (error) {
            console.error('خطأ في إعادة تعيين البيانات:', error);
            showError('فشل في إعادة تعيين البيانات');
        }
    }

    // ========================================
    // وظائف التبويبات الجديدة - New Tab Functions
    // ========================================

    showNewContractModal() {
        showInfo('إضافة عقد جديد - قيد التطوير');
    }

    showNewPaymentModal() {
        showInfo('إضافة دفعة جديدة - قيد التطوير');
    }

    generatePaymentReport() {
        showInfo('تقرير المدفوعات - قيد التطوير');
    }

    showNewEvaluationModal() {
        showInfo('تقييم جديد - قيد التطوير');
    }

    viewLabEvaluations(labId) {
        showInfo('عرض تقييمات المعمل - قيد التطوير');
    }

    evaluateLab(labId) {
        showInfo('تقييم المعمل - قيد التطوير');
    }

    saveSettings() {
        showSuccess('تم حفظ الإعدادات بنجاح');
    }

    showSettingsModal() {
        this.switchTab('settings');
    }

    // ========================================
    // إضافة المزيد من البيانات التجريبية - Add More Sample Data
    // ========================================

    async addMoreSampleData() {
        try {
            const additionalLabs = [
                {
                    id: Date.now() + 1,
                    name: 'مختبر الإبداع الطبي',
                    city: 'المدينة المنورة',
                    address: 'شارع قباء، حي العوالي',
                    phone: '0148765432',
                    email: '<EMAIL>',
                    contact_person: 'د. يوسف الأنصاري',
                    specialties: 'طب أسنان الأطفال، تقويم الأسنان',
                    delivery_time: 8,
                    quality_rating: 4,
                    payment_terms: 'credit_15',
                    credit_limit: 35000,
                    notes: 'متخصص في أسنان الأطفال والتقويم',
                    isActive: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    total_cases: 22,
                    total_revenue: 58000
                },
                {
                    id: Date.now() + 2,
                    name: 'معمل التميز الذهبي',
                    city: 'الطائف',
                    address: 'طريق الملك فيصل، حي الشهداء',
                    phone: '0127654321',
                    email: '<EMAIL>',
                    contact_person: 'أ. هند محمد',
                    specialties: 'تركيبات ذهبية، أعمال البورسلين',
                    delivery_time: 12,
                    quality_rating: 5,
                    payment_terms: 'credit_30',
                    credit_limit: 70000,
                    notes: 'معمل فاخر متخصص في الأعمال الذهبية',
                    isActive: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    total_cases: 18,
                    total_revenue: 95000
                }
            ];

            const additionalOrders = [
                {
                    id: Date.now() + 10,
                    lab_id: additionalLabs[0].id,
                    order_number: this.generateOrderNumber(),
                    work_type: 'orthodontic',
                    patient_name: 'عبدالرحمن أحمد',
                    doctor_name: 'د. أمل الحربي',
                    order_date: new Date().toISOString().split('T')[0],
                    delivery_date: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    quantity: 1,
                    unit_price: 2800,
                    total_amount: 2800,
                    description: 'جهاز تقويم للأطفال',
                    notes: 'جهاز ملون للطفل',
                    status: 'pending',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ];

            // إضافة البيانات الجديدة
            for (const lab of additionalLabs) {
                await db.insert('external_labs', lab);
                this.labs.push(lab);
            }

            for (const order of additionalOrders) {
                await db.insert('external_orders', order);
                this.orders.push(order);
            }

            // تحديث الواجهة
            this.renderMainInterface();
            showSuccess(`تم إضافة ${additionalLabs.length} معمل و ${additionalOrders.length} طلب جديد`);

        } catch (error) {
            console.error('خطأ في إضافة البيانات:', error);
            showError('فشل في إضافة البيانات الإضافية');
        }
    }

    // ========================================
    // وظائف إضافية للعقود - Contract Functions
    // ========================================

    async createContract(labId, contractData) {
        try {
            const contract = {
                id: Date.now(),
                lab_id: labId,
                ...contractData,
                created_at: new Date().toISOString(),
                status: 'active'
            };

            await db.insert('lab_contracts', contract);
            showSuccess('تم إنشاء العقد بنجاح');
            this.renderContracts();
        } catch (error) {
            console.error('خطأ في إنشاء العقد:', error);
            showError('فشل في إنشاء العقد');
        }
    }

    async renewContract(contractId) {
        try {
            const contract = await db.findById('lab_contracts', contractId);
            if (!contract) {
                showError('العقد غير موجود');
                return;
            }

            // إنشاء عقد جديد بناءً على العقد الحالي
            const newContract = {
                ...contract,
                id: Date.now(),
                start_date: new Date().toISOString(),
                end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // سنة من الآن
                created_at: new Date().toISOString(),
                status: 'active'
            };

            await db.insert('lab_contracts', newContract);

            // تحديث العقد القديم
            await db.update('lab_contracts', contractId, { status: 'renewed' });

            showSuccess('تم تجديد العقد بنجاح');
            this.renderContracts();
        } catch (error) {
            console.error('خطأ في تجديد العقد:', error);
            showError('فشل في تجديد العقد');
        }
    }

    // ========================================
    // وظائف إضافية للمدفوعات - Payment Functions
    // ========================================

    async recordPayment(paymentData) {
        try {
            const payment = {
                id: Date.now(),
                ...paymentData,
                created_at: new Date().toISOString(),
                status: 'completed'
            };

            await db.insert('lab_payments', payment);
            showSuccess('تم تسجيل الدفعة بنجاح');
            this.renderPayments();
        } catch (error) {
            console.error('خطأ في تسجيل الدفعة:', error);
            showError('فشل في تسجيل الدفعة');
        }
    }

    async confirmPayment(paymentId) {
        try {
            await db.update('lab_payments', paymentId, {
                status: 'confirmed',
                confirmed_at: new Date().toISOString()
            });

            showSuccess('تم تأكيد الدفعة بنجاح');
            this.renderPayments();
        } catch (error) {
            console.error('خطأ في تأكيد الدفعة:', error);
            showError('فشل في تأكيد الدفعة');
        }
    }

    // ========================================
    // وظائف إضافية للتقييم - Evaluation Functions
    // ========================================

    async submitEvaluation(labId, evaluationData) {
        try {
            const evaluation = {
                id: Date.now(),
                lab_id: labId,
                ...evaluationData,
                created_at: new Date().toISOString()
            };

            await db.insert('lab_evaluations', evaluation);

            // تحديث متوسط تقييم المعمل
            await this.updateLabRating(labId);

            showSuccess('تم إرسال التقييم بنجاح');
            this.renderQuality();
        } catch (error) {
            console.error('خطأ في إرسال التقييم:', error);
            showError('فشل في إرسال التقييم');
        }
    }

    async updateLabRating(labId) {
        try {
            const evaluations = await db.findMany('lab_evaluations', { lab_id: labId });

            if (evaluations.length > 0) {
                const totalRating = evaluations.reduce((sum, eval) => sum + eval.overall_rating, 0);
                const averageRating = Math.round((totalRating / evaluations.length) * 10) / 10;

                await db.update('external_labs', labId, {
                    quality_rating: averageRating,
                    total_evaluations: evaluations.length
                });

                // تحديث القائمة المحلية
                const labIndex = this.labs.findIndex(lab => lab.id === labId);
                if (labIndex !== -1) {
                    this.labs[labIndex].quality_rating = averageRating;
                    this.labs[labIndex].total_evaluations = evaluations.length;
                }
            }
        } catch (error) {
            console.error('خطأ في تحديث تقييم المعمل:', error);
        }
    }

    // ========================================
    // وظائف إضافية للإعدادات - Settings Functions
    // ========================================

    async loadSettings() {
        try {
            const settings = await db.findOne('external_labs_settings') || this.getDefaultSettings();
            return settings;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            return this.getDefaultSettings();
        }
    }

    getDefaultSettings() {
        return {
            notifications: {
                contract_expiry_alert: true,
                auto_confirm_orders: true,
                auto_evaluation: false,
                contract_expiry_days: 15,
                delivery_delay_days: 2
            },
            payments: {
                default_method: 'cash',
                default_terms: 7
            },
            export: {
                default_format: 'excel',
                include_financial: true
            },
            backup: {
                frequency: 'daily',
                auto_backup: true
            },
            security: {
                log_operations: true,
                encrypt_data: false
            }
        };
    }

    async updateSettings(newSettings) {
        try {
            await db.upsert('external_labs_settings', newSettings);
            showSuccess('تم حفظ الإعدادات بنجاح');
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            showError('فشل في حفظ الإعدادات');
        }
    }

    // ========================================
    // النوافذ المنبثقة - Modal Functions
    // ========================================

    showNewLabModal() {
        showInfo('إضافة معمل جديد - قيد التطوير');
    }

    showNewOrderModal(labId = null) {
        showInfo('إنشاء طلب جديد - قيد التطوير');
    }

    closeNewLabModal() {
        // إغلاق نافذة المعمل الجديد
        const modal = document.getElementById('new-lab-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    closeNewOrderModal() {
        // إغلاق نافذة الطلب الجديد
        const modal = document.getElementById('new-order-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // ========================================
    // وظائف إضافية - Additional Functions
    // ========================================

    async exportData() {
        try {
            const data = {
                labs: this.labs,
                orders: this.orders,
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `external-labs-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showSuccess('تم تصدير البيانات بنجاح');
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            showError('فشل في تصدير البيانات');
        }
    }

    async generateReport() {
        showInfo('إنشاء التقرير - قيد التطوير');
    }

    // ========================================
    // إنشاء نافذة تفاصيل الطلب - Create Order Details Modal
    // ========================================

    createOrderDetailsModal(order) {
        const lab = this.labs.find(l => l.id === order.lab_id);

        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'order-details-modal';

        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2>تفاصيل الطلب - ${order.order_number}</h2>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="order-details-content">
                        <div class="details-section">
                            <h3><i class="fas fa-info-circle"></i> معلومات الطلب</h3>
                            <div class="details-grid">
                                <div class="detail-item">
                                    <label>رقم الطلب:</label>
                                    <span>${order.order_number}</span>
                                </div>
                                <div class="detail-item">
                                    <label>نوع العمل:</label>
                                    <span>${this.getWorkTypeText(order.work_type)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>الوصف:</label>
                                    <span>${order.description || 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>الحالة:</label>
                                    <span class="status-badge ${this.getStatusClass(order.status)}">
                                        ${this.getStatusText(order.status)}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="details-section">
                            <h3><i class="fas fa-building"></i> معلومات المعمل</h3>
                            <div class="details-grid">
                                <div class="detail-item">
                                    <label>اسم المعمل:</label>
                                    <span>${lab ? lab.name : 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>المدينة:</label>
                                    <span>${lab ? lab.city : 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>الهاتف:</label>
                                    <span>${lab ? lab.phone : 'غير محدد'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="details-section">
                            <h3><i class="fas fa-user"></i> معلومات المريض والطبيب</h3>
                            <div class="details-grid">
                                <div class="detail-item">
                                    <label>اسم المريض:</label>
                                    <span>${order.patient_name || 'غير محدد'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>اسم الطبيب:</label>
                                    <span>${order.doctor_name || 'غير محدد'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="details-section">
                            <h3><i class="fas fa-calendar"></i> التواريخ</h3>
                            <div class="details-grid">
                                <div class="detail-item">
                                    <label>تاريخ الطلب:</label>
                                    <span>${this.formatDate(order.order_date)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>تاريخ التسليم المتوقع:</label>
                                    <span>${this.formatDate(order.delivery_date)}</span>
                                </div>
                            </div>
                        </div>

                        <div class="details-section">
                            <h3><i class="fas fa-money-bill"></i> المعلومات المالية</h3>
                            <div class="details-grid">
                                <div class="detail-item">
                                    <label>الكمية:</label>
                                    <span>${order.quantity || 1}</span>
                                </div>
                                <div class="detail-item">
                                    <label>سعر الوحدة:</label>
                                    <span>${this.formatCurrency(order.unit_price)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>المبلغ الإجمالي:</label>
                                    <span><strong>${this.formatCurrency(order.total_amount)}</strong></span>
                                </div>
                            </div>
                        </div>

                        ${order.notes ? `
                        <div class="details-section">
                            <h3><i class="fas fa-sticky-note"></i> ملاحظات</h3>
                            <p class="notes-text">${order.notes}</p>
                        </div>
                        ` : ''}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="externalLabsManager.editOrder(${order.id}); this.closest('.modal').remove();">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-outline" onclick="externalLabsManager.updateOrderStatus(${order.id}); this.closest('.modal').remove();">
                        <i class="fas fa-sync"></i>
                        تحديث الحالة
                    </button>
                    <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        return modal;
    }
}

// ========================================
// تهيئة وحدة المعامل الخارجية - Initialize External Labs Module
// ========================================

// إنشاء مثيل من مدير المعامل الخارجية
const externalLabsManager = new ExternalLabsManager();

// دالة تهيئة الوحدة
async function initializeExternalLabsModule() {
    try {
        const success = await externalLabsManager.init();
        if (success) {
            console.log('✅ تم تهيئة وحدة المعامل الخارجية بنجاح');
        } else {
            console.error('❌ فشل في تهيئة وحدة المعامل الخارجية');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة وحدة المعامل الخارجية:', error);
        return false;
    }
}

// ========================================
// إنشاء مثيل من مدير المعامل الخارجية - Create External Labs Manager Instance
// ========================================

// إنشاء مثيل عام من مدير المعامل الخارجية
let externalLabsManager = null;

// دالة تهيئة المعامل الخارجية
async function initializeExternalLabs() {
    try {
        console.log('🔄 بدء تهيئة وحدة المعامل الخارجية...');

        if (!externalLabsManager) {
            externalLabsManager = new ExternalLabsManager();
        }

        const success = await externalLabsManager.init();

        if (success) {
            console.log('✅ تم تهيئة وحدة المعامل الخارجية بنجاح');

            // إضافة المدير إلى النطاق العام
            window.externalLabsManager = externalLabsManager;

            return true;
        } else {
            console.error('❌ فشل في تهيئة وحدة المعامل الخارجية');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في تهيئة وحدة المعامل الخارجية:', error);
        return false;
    }
}

// تصدير الفئة والدوال للاستخدام العام
window.ExternalLabsManager = ExternalLabsManager;
window.initializeExternalLabs = initializeExternalLabs;

console.log('✅ تم تحميل وحدة إدارة المعامل الخارجية بنجاح');
