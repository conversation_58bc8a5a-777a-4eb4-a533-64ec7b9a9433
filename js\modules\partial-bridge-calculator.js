// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// حاسبة البرشل الجزئي المتقدمة - Advanced Partial Bridge Calculator
// ========================================

console.log('🧮 تحميل حاسبة البرشل الجزئي المتقدمة...');

// ========================================
// فئة حاسبة البرشل الجزئي - Partial Bridge Calculator Class
// ========================================

class PartialBridgeCalculator {
    constructor() {
        this.pricingTiers = {
            // نظام التدرج في الأسعار حسب عدد الأسنان
            standard: [
                { min: 1, max: 3, discount: 0, description: 'الأسنان الأولى (1-3)' },
                { min: 4, max: 6, discount: 0.1, description: 'الأسنان المتوسطة (4-6)' },
                { min: 7, max: 10, discount: 0.2, description: 'الأسنان الكثيرة (7-10)' },
                { min: 11, max: Infinity, discount: 0.3, description: 'الأسنان الإضافية (11+)' }
            ],
            
            // نظام خاص للبرشل المعقد
            complex: [
                { min: 1, max: 2, discount: 0, description: 'برشل بسيط (1-2)' },
                { min: 3, max: 5, discount: 0.15, description: 'برشل متوسط (3-5)' },
                { min: 6, max: 8, discount: 0.25, description: 'برشل معقد (6-8)' },
                { min: 9, max: Infinity, discount: 0.35, description: 'برشل شامل (9+)' }
            ]
        };

        this.materialMultipliers = {
            'metal': 1.0,           // معدن عادي
            'vitallium': 1.2,       // فيتاليوم
            'titanium': 1.5,        // تيتانيوم
            'gold': 2.0,            // ذهب
            'ceramic': 1.3,         // سيراميك
            'zirconia': 1.4         // زيركون
        };

        this.complexityFactors = {
            'simple': 1.0,          // بسيط
            'moderate': 1.2,        // متوسط
            'complex': 1.5,         // معقد
            'very_complex': 1.8     // معقد جداً
        };

        this.locationFactors = {
            'anterior': 1.3,        // أمامي
            'posterior': 1.0,       // خلفي
            'mixed': 1.15           // مختلط
        };
    }

    // ========================================
    // حساب سعر البرشل الجزئي - Calculate Partial Bridge Price
    // ========================================

    calculatePartialBridgePrice(options) {
        const {
            teethCount,
            basePrice,
            material = 'metal',
            complexity = 'simple',
            location = 'posterior',
            pricingType = 'standard',
            additionalWork = [],
            doctorDiscount = 0
        } = options;

        if (!teethCount || teethCount <= 0 || !basePrice || basePrice <= 0) {
            throw new Error('عدد الأسنان والسعر الأساسي مطلوبان');
        }

        // 1. حساب السعر الأساسي مع التدرج
        const tieredPrice = this.calculateTieredPrice(teethCount, basePrice, pricingType);

        // 2. تطبيق معامل المادة
        const materialPrice = tieredPrice * (this.materialMultipliers[material] || 1.0);

        // 3. تطبيق معامل التعقيد
        const complexityPrice = materialPrice * (this.complexityFactors[complexity] || 1.0);

        // 4. تطبيق معامل الموقع
        const locationPrice = complexityPrice * (this.locationFactors[location] || 1.0);

        // 5. إضافة الأعمال الإضافية
        const additionalPrice = this.calculateAdditionalWork(additionalWork);

        // 6. السعر الإجمالي قبل الخصم
        const totalBeforeDiscount = locationPrice + additionalPrice;

        // 7. تطبيق خصم الطبيب
        const discountAmount = (totalBeforeDiscount * doctorDiscount) / 100;
        const finalPrice = totalBeforeDiscount - discountAmount;

        return {
            breakdown: {
                basePrice: basePrice,
                teethCount: teethCount,
                tieredPrice: tieredPrice,
                materialMultiplier: this.materialMultipliers[material] || 1.0,
                materialPrice: materialPrice,
                complexityMultiplier: this.complexityFactors[complexity] || 1.0,
                complexityPrice: complexityPrice,
                locationMultiplier: this.locationFactors[location] || 1.0,
                locationPrice: locationPrice,
                additionalPrice: additionalPrice,
                totalBeforeDiscount: totalBeforeDiscount,
                doctorDiscount: doctorDiscount,
                discountAmount: discountAmount,
                finalPrice: finalPrice
            },
            summary: {
                teethCount: teethCount,
                material: material,
                complexity: complexity,
                location: location,
                basePrice: basePrice,
                finalPrice: finalPrice,
                savings: discountAmount,
                pricePerTooth: finalPrice / teethCount
            },
            details: this.generatePriceDetails(options, tieredPrice, finalPrice)
        };
    }

    // ========================================
    // حساب السعر المتدرج - Calculate Tiered Price
    // ========================================

    calculateTieredPrice(teethCount, basePrice, pricingType = 'standard') {
        const tiers = this.pricingTiers[pricingType] || this.pricingTiers.standard;
        let totalPrice = 0;
        let remainingTeeth = teethCount;

        for (const tier of tiers) {
            if (remainingTeeth <= 0) break;

            const teethInTier = Math.min(remainingTeeth, tier.max - tier.min + 1);
            const tierPrice = teethInTier * basePrice * (1 - tier.discount);
            
            totalPrice += tierPrice;
            remainingTeeth -= teethInTier;

            if (remainingTeeth <= 0) break;
        }

        return totalPrice;
    }

    // ========================================
    // حساب الأعمال الإضافية - Calculate Additional Work
    // ========================================

    calculateAdditionalWork(additionalWork = []) {
        let totalAdditional = 0;

        additionalWork.forEach(work => {
            if (work.price && work.quantity) {
                totalAdditional += work.price * work.quantity;
            }
        });

        return totalAdditional;
    }

    // ========================================
    // إنشاء تفاصيل السعر - Generate Price Details
    // ========================================

    generatePriceDetails(options, tieredPrice, finalPrice) {
        const { teethCount, basePrice, pricingType = 'standard' } = options;
        const tiers = this.pricingTiers[pricingType] || this.pricingTiers.standard;
        const details = [];

        let remainingTeeth = teethCount;
        let runningTotal = 0;

        for (const tier of tiers) {
            if (remainingTeeth <= 0) break;

            const teethInTier = Math.min(remainingTeeth, tier.max - tier.min + 1);
            const tierPrice = teethInTier * basePrice * (1 - tier.discount);
            
            details.push({
                description: tier.description,
                teethCount: teethInTier,
                basePrice: basePrice,
                discount: tier.discount * 100,
                pricePerTooth: basePrice * (1 - tier.discount),
                totalPrice: tierPrice
            });

            runningTotal += tierPrice;
            remainingTeeth -= teethInTier;

            if (remainingTeeth <= 0) break;
        }

        return details;
    }

    // ========================================
    // حساب توفير التكلفة - Calculate Cost Savings
    // ========================================

    calculateCostSavings(teethCount, basePrice, pricingType = 'standard') {
        // حساب السعر بدون تدرج (سعر ثابت لكل سن)
        const flatPrice = teethCount * basePrice;
        
        // حساب السعر مع التدرج
        const tieredPrice = this.calculateTieredPrice(teethCount, basePrice, pricingType);
        
        // حساب التوفير
        const savings = flatPrice - tieredPrice;
        const savingsPercentage = (savings / flatPrice) * 100;

        return {
            flatPrice: flatPrice,
            tieredPrice: tieredPrice,
            savings: savings,
            savingsPercentage: savingsPercentage
        };
    }

    // ========================================
    // اقتراح أفضل خيار - Suggest Best Option
    // ========================================

    suggestBestOption(teethCount, basePrice) {
        const options = ['standard', 'complex'];
        const suggestions = [];

        options.forEach(pricingType => {
            const price = this.calculateTieredPrice(teethCount, basePrice, pricingType);
            const savings = this.calculateCostSavings(teethCount, basePrice, pricingType);
            
            suggestions.push({
                type: pricingType,
                price: price,
                savings: savings.savings,
                savingsPercentage: savings.savingsPercentage,
                description: pricingType === 'standard' ? 'نظام التسعير العادي' : 'نظام التسعير المعقد'
            });
        });

        // ترتيب حسب أفضل توفير
        suggestions.sort((a, b) => b.savings - a.savings);

        return {
            recommended: suggestions[0],
            alternatives: suggestions.slice(1),
            comparison: suggestions
        };
    }

    // ========================================
    // تحليل الربحية - Profitability Analysis
    // ========================================

    analyzeProfitability(options, costPrice) {
        const result = this.calculatePartialBridgePrice(options);
        const profit = result.summary.finalPrice - costPrice;
        const profitMargin = (profit / result.summary.finalPrice) * 100;

        return {
            revenue: result.summary.finalPrice,
            cost: costPrice,
            profit: profit,
            profitMargin: profitMargin,
            breakeven: costPrice,
            recommendation: this.getProfitabilityRecommendation(profitMargin)
        };
    }

    // ========================================
    // توصية الربحية - Profitability Recommendation
    // ========================================

    getProfitabilityRecommendation(profitMargin) {
        if (profitMargin >= 50) {
            return { level: 'excellent', message: 'ربحية ممتازة - يُنصح بالقبول' };
        } else if (profitMargin >= 30) {
            return { level: 'good', message: 'ربحية جيدة - مقبول' };
        } else if (profitMargin >= 15) {
            return { level: 'fair', message: 'ربحية متوسطة - يحتاج مراجعة' };
        } else if (profitMargin >= 5) {
            return { level: 'low', message: 'ربحية منخفضة - غير مُنصح به' };
        } else {
            return { level: 'loss', message: 'خسارة - يجب رفض الطلب' };
        }
    }

    // ========================================
    // إنشاء تقرير مفصل - Generate Detailed Report
    // ========================================

    generateDetailedReport(options, costPrice = null) {
        const calculation = this.calculatePartialBridgePrice(options);
        const suggestions = this.suggestBestOption(options.teethCount, options.basePrice);
        
        let profitability = null;
        if (costPrice) {
            profitability = this.analyzeProfitability(options, costPrice);
        }

        return {
            calculation: calculation,
            suggestions: suggestions,
            profitability: profitability,
            timestamp: new Date().toISOString(),
            options: options
        };
    }
}

// ========================================
// دوال مساعدة للواجهة - UI Helper Functions
// ========================================

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

// تنسيق النسبة المئوية
function formatPercentage(percentage) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(percentage / 100);
}

// ========================================
// تهيئة الحاسبة - Initialize Calculator
// ========================================

// إنشاء مثيل من الحاسبة
const partialBridgeCalculator = new PartialBridgeCalculator();

// تصدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PartialBridgeCalculator, partialBridgeCalculator };
}

// إتاحة عامة
window.partialBridgeCalculator = partialBridgeCalculator;
window.PartialBridgeCalculator = PartialBridgeCalculator;

console.log('✅ تم تحميل حاسبة البرشل الجزئي المتقدمة بنجاح');
