// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// نظام التركيبات المتقدم - Advanced Prosthetics System
// ========================================

console.log('🦷 تحميل نظام التركيبات المتقدم...');

// ========================================
// فئة إدارة التركيبات المتقدمة - Advanced Prosthetics Manager
// ========================================

class AdvancedProstheticsManager {
    constructor() {
        this.prosthetics = [];
        this.doctors = [];
        this.prostheticTypes = [];
        this.selectedTeeth = [];
        this.currentProsthetic = null;
        this.currentView = 'list'; // list, new, edit, details
        this.teethMap = this.initializeTeethMap();
        this.priceCalculator = new PriceCalculator();
    }

    // ========================================
    // تهيئة خريطة الأسنان - Initialize Teeth Map
    // ========================================

    initializeTeethMap() {
        return {
            // الفك العلوي (1-16)
            upper: {
                1: { name: 'الضرس الثالث الأيمن العلوي', type: 'molar', position: 'upper_right' },
                2: { name: 'الضرس الثاني الأيمن العلوي', type: 'molar', position: 'upper_right' },
                3: { name: 'الضرس الأول الأيمن العلوي', type: 'molar', position: 'upper_right' },
                4: { name: 'الضاحك الثاني الأيمن العلوي', type: 'premolar', position: 'upper_right' },
                5: { name: 'الضاحك الأول الأيمن العلوي', type: 'premolar', position: 'upper_right' },
                6: { name: 'الناب الأيمن العلوي', type: 'canine', position: 'upper_right' },
                7: { name: 'الثنية الجانبية اليمنى العلوية', type: 'incisor', position: 'upper_right' },
                8: { name: 'الثنية الوسطى اليمنى العلوية', type: 'incisor', position: 'upper_right' },
                9: { name: 'الثنية الوسطى اليسرى العلوية', type: 'incisor', position: 'upper_left' },
                10: { name: 'الثنية الجانبية اليسرى العلوية', type: 'incisor', position: 'upper_left' },
                11: { name: 'الناب الأيسر العلوي', type: 'canine', position: 'upper_left' },
                12: { name: 'الضاحك الأول الأيسر العلوي', type: 'premolar', position: 'upper_left' },
                13: { name: 'الضاحك الثاني الأيسر العلوي', type: 'premolar', position: 'upper_left' },
                14: { name: 'الضرس الأول الأيسر العلوي', type: 'molar', position: 'upper_left' },
                15: { name: 'الضرس الثاني الأيسر العلوي', type: 'molar', position: 'upper_left' },
                16: { name: 'الضرس الثالث الأيسر العلوي', type: 'molar', position: 'upper_left' }
            },
            // الفك السفلي (17-32)
            lower: {
                17: { name: 'الضرس الثالث الأيسر السفلي', type: 'molar', position: 'lower_left' },
                18: { name: 'الضرس الثاني الأيسر السفلي', type: 'molar', position: 'lower_left' },
                19: { name: 'الضرس الأول الأيسر السفلي', type: 'molar', position: 'lower_left' },
                20: { name: 'الضاحك الثاني الأيسر السفلي', type: 'premolar', position: 'lower_left' },
                21: { name: 'الضاحك الأول الأيسر السفلي', type: 'premolar', position: 'lower_left' },
                22: { name: 'الناب الأيسر السفلي', type: 'canine', position: 'lower_left' },
                23: { name: 'الثنية الجانبية اليسرى السفلية', type: 'incisor', position: 'lower_left' },
                24: { name: 'الثنية الوسطى اليسرى السفلية', type: 'incisor', position: 'lower_left' },
                25: { name: 'الثنية الوسطى اليمنى السفلية', type: 'incisor', position: 'lower_right' },
                26: { name: 'الثنية الجانبية اليمنى السفلية', type: 'incisor', position: 'lower_right' },
                27: { name: 'الناب الأيمن السفلي', type: 'canine', position: 'lower_right' },
                28: { name: 'الضاحك الأول الأيمن السفلي', type: 'premolar', position: 'lower_right' },
                29: { name: 'الضاحك الثاني الأيمن السفلي', type: 'premolar', position: 'lower_right' },
                30: { name: 'الضرس الأول الأيمن السفلي', type: 'molar', position: 'lower_right' },
                31: { name: 'الضرس الثاني الأيمن السفلي', type: 'molar', position: 'lower_right' },
                32: { name: 'الضرس الثالث الأيمن السفلي', type: 'molar', position: 'lower_right' }
            }
        };
    }

    // ========================================
    // تهيئة النظام - Initialize System
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة نظام التركيبات المتقدم...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            console.log('✅ تم تهيئة نظام التركيبات المتقدم بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام التركيبات المتقدم:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل التركيبات
            this.prosthetics = await db.findAll('prosthetics');
            
            // تحميل الأطباء
            this.doctors = await db.findAll('doctors');
            
            // تحميل أنواع التركيبات
            this.prostheticTypes = await db.findAll('prosthetic_types');
            
            console.log(`📊 تم تحميل ${this.prosthetics.length} تركيبة`);
            console.log(`👨‍⚕️ تم تحميل ${this.doctors.length} طبيب`);
            console.log(`🦷 تم تحميل ${this.prostheticTypes.length} نوع تركيبة`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // توليد رقم الحالة التلقائي - Generate Case Number
    // ========================================

    async generateCaseNumber() {
        try {
            // الحصول على إعدادات الترقيم
            const formatSetting = await db.findAll('system_settings', {
                where: { setting_key: 'case_number_format' }
            });
            
            const nextNumberSetting = await db.findAll('system_settings', {
                where: { setting_key: 'next_case_number' }
            });
            
            const prefixSetting = await db.findAll('system_settings', {
                where: { setting_key: 'case_number_prefix' }
            });
            
            // القيم الافتراضية
            const format = formatSetting.length > 0 ? formatSetting[0].setting_value : 'YYYYMM-###';
            let nextNumber = nextNumberSetting.length > 0 ? parseInt(nextNumberSetting[0].setting_value) : 1;
            const prefix = prefixSetting.length > 0 ? prefixSetting[0].setting_value : '';
            
            // تاريخ اليوم
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            
            // البحث عن آخر رقم في الشهر الحالي
            const currentMonthPrefix = `${year}${month}`;
            const existingCases = await db.findAll('prosthetics', {
                where: { case_number: { like: `%${currentMonthPrefix}%` } }
            });
            
            // حساب الرقم التالي للشهر الحالي
            let monthlyNumber = 1;
            if (existingCases.length > 0) {
                const numbers = existingCases
                    .map(p => {
                        const match = p.case_number.match(/-(\d+)$/);
                        return match ? parseInt(match[1]) : 0;
                    })
                    .filter(n => n > 0);
                
                if (numbers.length > 0) {
                    monthlyNumber = Math.max(...numbers) + 1;
                }
            }
            
            // تكوين رقم الحالة
            let caseNumber = format
                .replace('YYYY', year.toString())
                .replace('MM', month)
                .replace('###', String(monthlyNumber).padStart(3, '0'));
            
            if (prefix) {
                caseNumber = prefix + caseNumber;
            }
            
            // تحديث الرقم التالي في الإعدادات
            if (nextNumberSetting.length > 0) {
                await db.update('system_settings', nextNumberSetting[0].id, {
                    setting_value: (nextNumber + 1).toString(),
                    updatedAt: new Date().toISOString()
                });
            }
            
            return caseNumber;
            
        } catch (error) {
            console.error('خطأ في توليد رقم الحالة:', error);
            // رقم احتياطي
            const now = new Date();
            const timestamp = now.getTime().toString().slice(-6);
            return `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}-${timestamp}`;
        }
    }

    // ========================================
    // عرض النظام - Render System
    // ========================================

    async render() {
        try {
            const contentArea = document.getElementById('content-area');
            if (!contentArea) return;

            // إنشاء HTML للنظام
            contentArea.innerHTML = this.getAdvancedProstheticsHTML();
            
            // تحميل البيانات وعرضها
            await this.loadInitialData();
            
            // عرض القائمة الافتراضية
            this.renderProstheticsList();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
        } catch (error) {
            console.error('خطأ في عرض نظام التركيبات المتقدم:', error);
            showError('فشل في تحميل نظام التركيبات المتقدم');
        }
    }

    // ========================================
    // إنشاء HTML للنظام - Get Advanced Prosthetics HTML
    // ========================================

    getAdvancedProstheticsHTML() {
        return `
            <div class="advanced-prosthetics-container">
                <!-- شريط الأدوات العلوي -->
                <div class="prosthetics-toolbar">
                    <div class="toolbar-left">
                        <h1 class="page-title">
                            <i class="fas fa-tooth"></i>
                            نظام التركيبات المتقدم
                        </h1>
                        <p class="page-subtitle">إدارة شاملة للتركيبات مع نظام ترقيم تلقائي واختيار الأسنان التفاعلي</p>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="advancedProstheticsManager.showNewProstheticModal()">
                            <i class="fas fa-plus"></i>
                            تركيبة جديدة
                        </button>
                        <button class="btn btn-outline" onclick="advancedProstheticsManager.generateBulkInvoice()">
                            <i class="fas fa-file-invoice"></i>
                            فاتورة مجمعة
                        </button>
                        <button class="btn btn-outline" onclick="advancedProstheticsManager.generateDoctorStatement()">
                            <i class="fas fa-file-medical"></i>
                            كشف حساب طبيب
                        </button>
                        <button class="btn btn-outline" onclick="advancedProstheticsManager.refreshData()">
                            <i class="fas fa-sync"></i>
                            تحديث
                        </button>
                        <button class="btn btn-outline" onclick="advancedProstheticsManager.exportProsthetics()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="prosthetics-stats">
                    <div class="stat-card stat-card-pending">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pending-count" class="stat-value">0</h3>
                            <p class="stat-title">في الانتظار</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-progress">
                        <div class="stat-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="progress-count" class="stat-value">0</h3>
                            <p class="stat-title">قيد التنفيذ</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-completed">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completed-count" class="stat-value">0</h3>
                            <p class="stat-title">مكتملة</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-delivered">
                        <div class="stat-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="delivered-count" class="stat-value">0</h3>
                            <p class="stat-title">تم التسليم</p>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث والتصفية -->
                <div class="prosthetics-filters">
                    <div class="filter-group">
                        <input type="search" id="prosthetics-search" class="form-control" placeholder="البحث في رقم الحالة، اسم المريض، أو الطبيب...">
                    </div>
                    <div class="filter-group">
                        <select id="status-filter" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="in_progress">قيد التنفيذ</option>
                            <option value="completed">مكتملة</option>
                            <option value="delivered">تم التسليم</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <select id="doctor-filter" class="form-control">
                            <option value="">جميع الأطباء</option>
                            <!-- سيتم ملؤها ديناميكياً -->
                        </select>
                    </div>
                    <div class="filter-group">
                        <select id="category-filter" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="porcelain">البورسلين</option>
                            <option value="zirconia">الزيركون</option>
                            <option value="metal">المعدن</option>
                            <option value="dentures">الأطقم المتحركة</option>
                            <option value="orthodontics">التقويم</option>
                            <option value="additional">أعمال إضافية</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <input type="date" id="date-from" class="form-control" placeholder="من تاريخ">
                    </div>
                    <div class="filter-group">
                        <input type="date" id="date-to" class="form-control" placeholder="إلى تاريخ">
                    </div>
                </div>

                <!-- قائمة التركيبات -->
                <div class="prosthetics-list-container">
                    <div class="table-container">
                        <table class="table prosthetics-table">
                            <thead>
                                <tr>
                                    <th onclick="advancedProstheticsManager.sortTable('case_number')">
                                        رقم الحالة <i class="fas fa-sort"></i>
                                    </th>
                                    <th onclick="advancedProstheticsManager.sortTable('patient_name')">
                                        المريض <i class="fas fa-sort"></i>
                                    </th>
                                    <th onclick="advancedProstheticsManager.sortTable('doctor_id')">
                                        الطبيب <i class="fas fa-sort"></i>
                                    </th>
                                    <th onclick="advancedProstheticsManager.sortTable('prosthetic_type')">
                                        نوع التركيبة <i class="fas fa-sort"></i>
                                    </th>
                                    <th onclick="advancedProstheticsManager.sortTable('teeth_count')">
                                        عدد الأسنان <i class="fas fa-sort"></i>
                                    </th>
                                    <th onclick="advancedProstheticsManager.sortTable('final_price')">
                                        السعر النهائي <i class="fas fa-sort"></i>
                                    </th>
                                    <th onclick="advancedProstheticsManager.sortTable('expected_delivery')">
                                        التسليم المتوقع <i class="fas fa-sort"></i>
                                    </th>
                                    <th onclick="advancedProstheticsManager.sortTable('status')">
                                        الحالة <i class="fas fa-sort"></i>
                                    </th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="prosthetics-table-body">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>

            <!-- نافذة تركيبة جديدة -->
            <div id="new-prosthetic-modal" class="modal hidden">
                <div class="modal-overlay"></div>
                <div class="modal-content extra-large-modal">
                    <div class="modal-header">
                        <h2>تسجيل تركيبة جديدة</h2>
                        <button class="modal-close" onclick="advancedProstheticsManager.closeNewProstheticModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="new-prosthetic-form" class="modal-body">
                        <!-- سيتم إضافة محتوى النموذج هنا -->
                    </form>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="advancedProstheticsManager.closeNewProstheticModal()">
                            إلغاء
                        </button>
                        <button type="submit" form="new-prosthetic-form" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التركيبة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إعداد الأحداث - Setup Event Listeners
    // ========================================

    setupEventListeners() {
        // نموذج تركيبة جديدة
        const newProstheticForm = document.getElementById('new-prosthetic-form');
        if (newProstheticForm) {
            newProstheticForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveProsthetic();
            });
        }

        // البحث والتصفية
        const searchInput = document.getElementById('prosthetics-search');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(() => {
                this.filterProsthetics();
            }, 300));
        }

        // تصفية الحالة
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filterProsthetics();
            });
        }

        // تصفية الطبيب
        const doctorFilter = document.getElementById('doctor-filter');
        if (doctorFilter) {
            doctorFilter.addEventListener('change', () => {
                this.filterProsthetics();
            });
        }

        // تصفية النوع
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.filterProsthetics();
            });
        }

        // تصفية التاريخ
        const dateFromFilter = document.getElementById('date-from');
        const dateToFilter = document.getElementById('date-to');
        if (dateFromFilter && dateToFilter) {
            dateFromFilter.addEventListener('change', () => this.filterProsthetics());
            dateToFilter.addEventListener('change', () => this.filterProsthetics());
        }
    }

    // ========================================
    // عرض قائمة التركيبات - Render Prosthetics List
    // ========================================

    renderProstheticsList() {
        const tableBody = document.getElementById('prosthetics-table-body');
        if (!tableBody) return;

        if (this.prosthetics.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-tooth"></i>
                            <h3>لا توجد تركيبات مسجلة</h3>
                            <p>ابدأ بإضافة أول تركيبة</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.prosthetics.map(prosthetic => {
            const doctor = this.doctors.find(d => d.id === prosthetic.doctor_id);
            const statusClass = this.getStatusClass(prosthetic.status);
            const statusText = this.getStatusText(prosthetic.status);

            return `
                <tr data-prosthetic-id="${prosthetic.id}">
                    <td>
                        <strong>${prosthetic.case_number}</strong>
                    </td>
                    <td>
                        <div class="patient-info">
                            <strong>${prosthetic.patient_name}</strong>
                            ${prosthetic.patient_phone ? `<br><small>${prosthetic.patient_phone}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="doctor-name">${doctor ? doctor.name : 'غير محدد'}</span>
                    </td>
                    <td>
                        <div class="prosthetic-type">
                            <strong>${prosthetic.prosthetic_type}</strong>
                            ${prosthetic.material ? `<br><small>${prosthetic.material}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="teeth-count">${prosthetic.teeth_count || 0}</span>
                    </td>
                    <td>
                        <span class="price">${formatCurrency(prosthetic.final_price || 0)}</span>
                    </td>
                    <td>
                        <span class="delivery-date">${formatDate(prosthetic.expected_delivery)}</span>
                    </td>
                    <td>
                        <span class="status-badge status-${statusClass}">${statusText}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline" onclick="advancedProstheticsManager.viewProsthetic(${prosthetic.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="advancedProstheticsManager.editProsthetic(${prosthetic.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="advancedProstheticsManager.updateStatus(${prosthetic.id})" title="تحديث الحالة">
                                <i class="fas fa-sync"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="advancedProstheticsManager.printProsthetic(${prosthetic.id})" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="advancedProstheticsManager.deleteProsthetic(${prosthetic.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // تحديث الإحصائيات
        this.updateStatistics();

        // تحديث قائمة الأطباء في التصفية
        this.updateDoctorFilter();
    }

    // ========================================
    // تحديث الإحصائيات - Update Statistics
    // ========================================

    updateStatistics() {
        const pendingCount = this.prosthetics.filter(p => p.status === 'pending').length;
        const progressCount = this.prosthetics.filter(p => p.status === 'in_progress').length;
        const completedCount = this.prosthetics.filter(p => p.status === 'completed').length;
        const deliveredCount = this.prosthetics.filter(p => p.status === 'delivered').length;

        const pendingElement = document.getElementById('pending-count');
        const progressElement = document.getElementById('progress-count');
        const completedElement = document.getElementById('completed-count');
        const deliveredElement = document.getElementById('delivered-count');

        if (pendingElement) pendingElement.textContent = pendingCount;
        if (progressElement) progressElement.textContent = progressCount;
        if (completedElement) completedElement.textContent = completedCount;
        if (deliveredElement) deliveredElement.textContent = deliveredCount;
    }

    // ========================================
    // تحديث قائمة الأطباء في التصفية - Update Doctor Filter
    // ========================================

    updateDoctorFilter() {
        const doctorFilter = document.getElementById('doctor-filter');
        if (!doctorFilter) return;

        // الاحتفاظ بالقيمة المختارة
        const selectedValue = doctorFilter.value;

        // مسح الخيارات الحالية (عدا الخيار الأول)
        doctorFilter.innerHTML = '<option value="">جميع الأطباء</option>';

        // إضافة الأطباء
        this.doctors.forEach(doctor => {
            const option = document.createElement('option');
            option.value = doctor.id;
            option.textContent = doctor.name;
            doctorFilter.appendChild(option);
        });

        // استعادة القيمة المختارة
        doctorFilter.value = selectedValue;
    }

    // ========================================
    // عرض نافذة تركيبة جديدة - Show New Prosthetic Modal
    // ========================================

    async showNewProstheticModal() {
        const modal = document.getElementById('new-prosthetic-modal');
        const form = document.getElementById('new-prosthetic-form');

        if (modal && form) {
            // إنشاء محتوى النموذج
            form.innerHTML = await this.getNewProstheticFormHTML();

            // عرض النافذة
            modal.classList.remove('hidden');

            // إعداد أحداث النموذج
            this.setupNewProstheticForm();

            // توليد رقم الحالة
            await this.generateAndSetCaseNumber();
        }
    }

    // ========================================
    // إنشاء HTML لنموذج التركيبة الجديدة - Get New Prosthetic Form HTML
    // ========================================

    async getNewProstheticFormHTML() {
        return `
            <!-- البيانات الأساسية -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    البيانات الأساسية
                </h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="case-number" class="form-label">رقم الحالة *</label>
                        <input type="text" id="case-number" class="form-control" readonly>
                        <small class="form-help">يتم توليده تلقائياً بصيغة YYYYMM-###</small>
                    </div>
                    <div class="form-group">
                        <label for="doctor-select" class="form-label">الطبيب *</label>
                        <select id="doctor-select" class="form-control" required>
                            <option value="">اختر الطبيب</option>
                            ${this.doctors.map(doctor =>
                                `<option value="${doctor.id}">${doctor.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="patient-name" class="form-label">اسم المريض *</label>
                        <input type="text" id="patient-name" class="form-control" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="patient-phone" class="form-label">هاتف المريض</label>
                        <input type="tel" id="patient-phone" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="expected-delivery" class="form-label">تاريخ التسليم المتوقع *</label>
                        <input type="date" id="expected-delivery" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="priority" class="form-label">الأولوية</label>
                        <select id="priority" class="form-control">
                            <option value="normal">عادي</option>
                            <option value="high">عالي</option>
                            <option value="urgent">عاجل</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- تحديد نوع التركيبة -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-tooth"></i>
                    تحديد نوع التركيبة
                </h3>
                <div class="prosthetic-categories">
                    <div class="category-tabs">
                        <button type="button" class="category-tab active" data-category="porcelain">
                            <i class="fas fa-gem"></i>
                            البورسلين
                        </button>
                        <button type="button" class="category-tab" data-category="zirconia">
                            <i class="fas fa-diamond"></i>
                            الزيركون
                        </button>
                        <button type="button" class="category-tab" data-category="metal">
                            <i class="fas fa-cog"></i>
                            المعدن
                        </button>
                        <button type="button" class="category-tab" data-category="dentures">
                            <i class="fas fa-smile"></i>
                            الأطقم المتحركة
                        </button>
                        <button type="button" class="category-tab" data-category="orthodontics">
                            <i class="fas fa-teeth"></i>
                            التقويم
                        </button>
                    </div>

                    <div class="category-content">
                        <!-- محتوى البورسلين -->
                        <div id="porcelain-content" class="category-panel active">
                            <div class="material-options">
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="بورسلين فيتا">
                                    <span class="material-card">
                                        <i class="fas fa-gem"></i>
                                        <strong>بورسلين فيتا</strong>
                                        <small>جودة عالية ومقاومة</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="بورسلين جى سرام">
                                    <span class="material-card">
                                        <i class="fas fa-gem"></i>
                                        <strong>بورسلين جى سرام</strong>
                                        <small>مقاومة فائقة</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="بورسلين فيس">
                                    <span class="material-card">
                                        <i class="fas fa-gem"></i>
                                        <strong>بورسلين فيس</strong>
                                        <small>للقشور التجميلية</small>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <!-- محتوى الزيركون -->
                        <div id="zirconia-content" class="category-panel">
                            <div class="material-options">
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Zircon Full Anatomy">
                                    <span class="material-card">
                                        <i class="fas fa-diamond"></i>
                                        <strong>Zircon Full Anatomy</strong>
                                        <small>زيركون كامل</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Zircon Copy + Porcelain">
                                    <span class="material-card">
                                        <i class="fas fa-diamond"></i>
                                        <strong>Zircon Copy + Porcelain</strong>
                                        <small>زيركون مع بورسلين</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Zircon Onlay">
                                    <span class="material-card">
                                        <i class="fas fa-diamond"></i>
                                        <strong>Zircon Onlay</strong>
                                        <small>حشوة زيركون خارجية</small>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <!-- محتوى المعدن -->
                        <div id="metal-content" class="category-panel">
                            <div class="material-options">
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="معدن عادي">
                                    <span class="material-card">
                                        <i class="fas fa-cog"></i>
                                        <strong>معدن عادي</strong>
                                        <small>اقتصادي ومتين</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Vitallium">
                                    <span class="material-card">
                                        <i class="fas fa-cog"></i>
                                        <strong>Vitallium</strong>
                                        <small>معدن عالي الجودة</small>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <!-- محتوى الأطقم المتحركة -->
                        <div id="dentures-content" class="category-panel">
                            <div class="material-options">
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="طقم متحرك كامل">
                                    <span class="material-card">
                                        <i class="fas fa-smile"></i>
                                        <strong>طقم متحرك كامل</strong>
                                        <small>للفك الكامل</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="طقم متحرك سفلى/علوى">
                                    <span class="material-card">
                                        <i class="fas fa-smile"></i>
                                        <strong>طقم متحرك سفلى/علوى</strong>
                                        <small>لفك واحد</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="برشل جزئى">
                                    <span class="material-card">
                                        <i class="fas fa-smile"></i>
                                        <strong>برشل جزئى</strong>
                                        <small>حساب خاص للأسعار</small>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <!-- محتوى التقويم -->
                        <div id="orthodontics-content" class="category-panel">
                            <div class="material-options">
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Vacuum Retainer">
                                    <span class="material-card">
                                        <i class="fas fa-teeth"></i>
                                        <strong>Vacuum Retainer</strong>
                                        <small>حافظ مكان شفاف</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Retainer وير">
                                    <span class="material-card">
                                        <i class="fas fa-teeth"></i>
                                        <strong>Retainer وير</strong>
                                        <small>حافظ مكان سلكي</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Lingual Arch">
                                    <span class="material-card">
                                        <i class="fas fa-teeth"></i>
                                        <strong>Lingual Arch</strong>
                                        <small>قوس لساني</small>
                                    </span>
                                </label>
                                <label class="material-option">
                                    <input type="radio" name="prosthetic-material" value="Night Guard">
                                    <span class="material-card">
                                        <i class="fas fa-teeth"></i>
                                        <strong>Night Guard</strong>
                                        <small>واقي ليلي</small>
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختيار الأسنان -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-mouse-pointer"></i>
                    اختيار الأسنان
                    <span class="teeth-counter">عدد الأسنان المختارة: <span id="selected-teeth-count">0</span></span>
                </h3>

                <!-- أزرار التحكم السريع -->
                <div class="controls">
                    <button type="button" class="control-btn primary" onclick="prostheticsAdvancedManager.selectAllTeeth()">
                        <i class="fas fa-check-double"></i>
                        تحديد الكل
                    </button>
                    <button type="button" class="control-btn secondary" onclick="prostheticsAdvancedManager.clearAllTeeth()">
                        <i class="fas fa-times"></i>
                        إلغاء التحديد
                    </button>
                    <button type="button" class="control-btn secondary" onclick="prostheticsAdvancedManager.selectUpperJaw()">
                        <i class="fas fa-arrow-up"></i>
                        الفك العلوي
                    </button>
                    <button type="button" class="control-btn secondary" onclick="prostheticsAdvancedManager.selectLowerJaw()">
                        <i class="fas fa-arrow-down"></i>
                        الفك السفلي
                    </button>
                </div>

                <!-- إحصائيات الاختيار -->
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-value" id="selected-count">0</div>
                        <div class="stat-label">أسنان مختارة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="total-teeth">32</div>
                        <div class="stat-label">إجمالي الأسنان</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="selection-percentage">0%</div>
                        <div class="stat-label">نسبة الاختيار</div>
                    </div>
                </div>

                <div class="teeth-selector">
                    ${this.getTeethSelectorHTML()}
                </div>
            </div>

            <!-- حساب السعر -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-calculator"></i>
                    حساب السعر
                </h3>
                <div class="price-calculation">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="unit-price" class="form-label">السعر لكل سن *</label>
                            <input type="number" id="unit-price" class="form-control" min="0" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="discount-percentage" class="form-label">نسبة الخصم (%)</label>
                            <input type="number" id="discount-percentage" class="form-control" min="0" max="100" step="0.1">
                        </div>
                        <div class="form-group">
                            <label for="discount-amount" class="form-label">مبلغ الخصم</label>
                            <input type="number" id="discount-amount" class="form-control" min="0" step="0.01" readonly>
                        </div>
                    </div>
                    <div class="price-summary">
                        <div class="price-row">
                            <span>عدد الأسنان:</span>
                            <span id="calc-teeth-count">0</span>
                        </div>
                        <div class="price-row">
                            <span>السعر الأساسي:</span>
                            <span id="calc-base-price">0.00 جنيه</span>
                        </div>
                        <div class="price-row">
                            <span>الخصم:</span>
                            <span id="calc-discount">0.00 جنيه</span>
                        </div>
                        <div class="price-row total">
                            <span>السعر النهائي:</span>
                            <span id="calc-final-price">0.00 جنيه</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-sticky-note"></i>
                    ملاحظات إضافية
                </h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="special-instructions" class="form-label">تعليمات خاصة</label>
                        <textarea id="special-instructions" class="form-control" rows="3" placeholder="أي تعليمات خاصة للمعمل..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="notes" class="form-label">ملاحظات عامة</label>
                        <textarea id="notes" class="form-control" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إنشاء واجهة اختيار الأسنان - Get Teeth Selector HTML
    // ========================================

    getTeethSelectorHTML() {
        return `
            <div class="teeth-diagram">
                <!-- الفك العلوي -->
                <div class="jaw upper-jaw">
                    <h4 class="jaw-title">الفك العلوي (UPPER JAW)</h4>

                    <!-- تسميات الجوانب للفك العلوي -->
                    <div class="jaw-sides-labels">
                        <span class="side-label right-side">الجانب الأيمن</span>
                        <span class="side-label left-side">الجانب الأيسر</span>
                    </div>

                    <div class="teeth-row upper-teeth">
                        ${this.getUpperTeethHTML()}
                    </div>
                </div>

                <!-- الفك السفلي -->
                <div class="jaw lower-jaw">
                    <h4 class="jaw-title">الفك السفلي (LOWER JAW)</h4>

                    <!-- تسميات الجوانب للفك السفلي -->
                    <div class="jaw-sides-labels">
                        <span class="side-label right-side">الجانب الأيمن</span>
                        <span class="side-label left-side">الجانب الأيسر</span>
                    </div>

                    <div class="teeth-row lower-teeth">
                        ${this.getLowerTeethHTML()}
                    </div>
                </div>

                <!-- أدوات التحكم -->
                <div class="teeth-controls">
                    <button type="button" class="btn btn-outline" onclick="advancedProstheticsManager.selectAllTeeth()">
                        <i class="fas fa-check-double"></i>
                        تحديد الكل
                    </button>
                    <button type="button" class="btn btn-outline" onclick="advancedProstheticsManager.clearAllTeeth()">
                        <i class="fas fa-times"></i>
                        إلغاء التحديد
                    </button>
                    <button type="button" class="btn btn-outline" onclick="advancedProstheticsManager.selectUpperJaw()">
                        <i class="fas fa-arrow-up"></i>
                        الفك العلوي
                    </button>
                    <button type="button" class="btn btn-outline" onclick="advancedProstheticsManager.selectLowerJaw()">
                        <i class="fas fa-arrow-down"></i>
                        الفك السفلي
                    </button>
                </div>

                <!-- قائمة الأسنان المختارة -->
                <div class="selected-teeth-list">
                    <h5>الأسنان المختارة:</h5>
                    <div id="selected-teeth-display" class="teeth-tags">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إنشاء HTML للأسنان العلوية - Get Upper Teeth HTML
    // ========================================

    getUpperTeethHTML() {
        const rightSide = [8, 7, 6, 5, 4, 3, 2, 1]; // الجانب الأيمن
        const leftSide = [1, 2, 3, 4, 5, 6, 7, 8];  // الجانب الأيسر

        let html = '';

        // الجانب الأيمن
        rightSide.forEach(number => {
            const tooth = this.getToothInfoByPosition(number, 'upper_right');
            html += this.createToothHTML(tooth, `upper_right_${number}`);
        });

        // الجانب الأيسر
        leftSide.forEach(number => {
            const tooth = this.getToothInfoByPosition(number, 'upper_left');
            html += this.createToothHTML(tooth, `upper_left_${number}`);
        });

        return html;
    }

    // ========================================
    // إنشاء HTML للأسنان السفلية - Get Lower Teeth HTML
    // ========================================

    getLowerTeethHTML() {
        const rightSide = [8, 7, 6, 5, 4, 3, 2, 1]; // الجانب الأيمن
        const leftSide = [1, 2, 3, 4, 5, 6, 7, 8];  // الجانب الأيسر

        let html = '';

        // الجانب الأيمن
        rightSide.forEach(number => {
            const tooth = this.getToothInfoByPosition(number, 'lower_right');
            html += this.createToothHTML(tooth, `lower_right_${number}`);
        });

        // الجانب الأيسر
        leftSide.forEach(number => {
            const tooth = this.getToothInfoByPosition(number, 'lower_left');
            html += this.createToothHTML(tooth, `lower_left_${number}`);
        });

        return html;
    }

    // ========================================
    // إنشاء HTML لسن واحد - Create Tooth HTML
    // ========================================

    createToothHTML(tooth, uniqueId) {
        const toothIcon = this.getToothIcon(tooth.type);
        return `
            <div class="tooth" data-tooth="${uniqueId}" data-number="${tooth.number}" data-section="${tooth.section}" title="${tooth.name}">
                <div class="tooth-visual ${tooth.type}">
                    <div class="tooth-shape">${toothIcon}</div>
                    <span class="tooth-number">${tooth.number}</span>
                </div>
                <div class="tooth-label">${tooth.number}</div>
            </div>
        `;
    }

    // ========================================
    // الحصول على أيقونة السن - Get Tooth Icon
    // ========================================

    getToothIcon(type) {
        // استخدام اللوجو الطبي الجديد
        return `<svg width="24" height="24" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <!-- الأشعة الذهبية -->
          <g stroke="#F59E0B" stroke-width="1.5" stroke-linecap="round" opacity="0.8">
            <line x1="50" y1="8" x2="50" y2="15" />
            <line x1="58" y1="9" x2="57" y2="16" />
            <line x1="66" y1="12" x2="64" y2="18" />
            <line x1="73" y1="17" x2="70" y2="22" />
            <line x1="79" y1="24" x2="75" y2="28" />
            <line x1="84" y1="32" x2="79" y2="35" />
            <line x1="42" y1="9" x2="43" y2="16" />
            <line x1="34" y1="12" x2="36" y2="18" />
            <line x1="27" y1="17" x2="30" y2="22" />
            <line x1="21" y1="24" x2="25" y2="28" />
            <line x1="16" y1="32" x2="21" y2="35" />
          </g>

          <!-- الصليب الطبي -->
          <g fill="#2563EB">
            <rect x="75" y="25" width="6" height="15" rx="1" />
            <rect x="72" y="28" width="12" height="6" rx="1" />
          </g>

          <!-- السن الرئيسي -->
          <path d="M28 38 C28 28, 35 23, 50 23 C65 23, 72 28, 72 38 L72 55 C72 63, 68 70, 63 74 C58 78, 54 79, 50 77 C46 79, 42 78, 37 74 C32 70, 28 63, 28 55 Z"
                fill="white"
                stroke="#2563EB"
                stroke-width="2"/>

          <!-- التفاصيل الداخلية -->
          <path d="M33 42 C33 35, 38 31, 50 31 C62 31, 67 35, 67 42 L67 50 C67 55, 63 60, 58 62 C55 64, 52 64, 50 62 C48 64, 45 64, 42 62 C37 60, 33 55, 33 50 Z"
                fill="#F0F9FF"
                opacity="0.7"/>

          <!-- الجذور -->
          <ellipse cx="43" cy="70" rx="3" ry="8" fill="#2563EB" opacity="0.7"/>
          <ellipse cx="57" cy="70" rx="3" ry="8" fill="#2563EB" opacity="0.7"/>

          <!-- اللمعة -->
          <ellipse cx="45" cy="38" rx="4" ry="6" fill="white" opacity="0.5"/>
        </svg>`;
    }

    // ========================================
    // الحصول على معلومات السن حسب الموضع - Get Tooth Info By Position
    // ========================================

    getToothInfoByPosition(number, section) {
        const teethTypes = {
            1: 'incisor',    // قاطع مركزي
            2: 'incisor',    // قاطع جانبي
            3: 'canine',     // ناب
            4: 'premolar',   // ضاحك أول
            5: 'premolar',   // ضاحك ثاني
            6: 'molar',      // ضرس أول
            7: 'molar',      // ضرس ثاني
            8: 'molar'       // ضرس العقل
        };

        const teethNames = {
            1: 'القاطع المركزي',
            2: 'القاطع الجانبي',
            3: 'الناب',
            4: 'الضاحك الأول',
            5: 'الضاحك الثاني',
            6: 'الضرس الأول',
            7: 'الضرس الثاني',
            8: 'ضرس العقل'
        };

        const sectionNames = {
            'upper_right': 'الأيمن العلوي',
            'upper_left': 'الأيسر العلوي',
            'lower_right': 'الأيمن السفلي',
            'lower_left': 'الأيسر السفلي'
        };

        return {
            number: number,
            type: teethTypes[number] || 'incisor',
            name: `${teethNames[number]} - ${sectionNames[section]}`,
            section: section
        };
    }

    // ========================================
    // الحصول على معلومات السن - Get Tooth Info
    // ========================================

    getToothInfo(toothNumber) {
        // جميع الأسنان بنفس الشكل (مثل اللوجو)
        const type = 'tooth';

        // تحديد الفك والجهة
        const jaw = toothNumber <= 16 ? 'العلوي' : 'السفلي';
        const side = this.getToothSide(toothNumber);

        return {
            name: `السن رقم ${toothNumber} - ${side} ${jaw}`,
            type: type,
            position: toothNumber,
            jaw: jaw,
            side: side
        };
    }

    getToothSide(toothNumber) {
        // تحديد الجهة بناءً على رقم السن
        if (toothNumber <= 16) {
            // الفك العلوي: 1-8 يمين، 9-16 يسار
            return toothNumber <= 8 ? 'الأيمن' : 'الأيسر';
        } else {
            // الفك السفلي: 17-24 يمين، 25-32 يسار
            return toothNumber <= 24 ? 'الأيمن' : 'الأيسر';
        }
    }

    // ========================================
    // إعداد نموذج التركيبة الجديدة - Setup New Prosthetic Form
    // ========================================

    setupNewProstheticForm() {
        // إعداد تبديل الفئات
        this.setupCategoryTabs();

        // إعداد اختيار الأسنان
        this.setupTeethSelection();

        // إعداد حساب الأسعار
        this.setupPriceCalculation();

        // تحديث الإحصائيات الأولية
        this.updateStats();

        // تعيين تاريخ التسليم الافتراضي (أسبوع من اليوم)
        const deliveryDate = new Date();
        deliveryDate.setDate(deliveryDate.getDate() + 7);
        const deliveryInput = document.getElementById('expected-delivery');
        if (deliveryInput) {
            deliveryInput.value = deliveryDate.toISOString().split('T')[0];
        }
    }

    // ========================================
    // إعداد تبديل الفئات - Setup Category Tabs
    // ========================================

    setupCategoryTabs() {
        const categoryTabs = document.querySelectorAll('.category-tab');
        const categoryPanels = document.querySelectorAll('.category-panel');

        categoryTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const category = tab.dataset.category;

                // إزالة الفئة النشطة
                categoryTabs.forEach(t => t.classList.remove('active'));
                categoryPanels.forEach(p => p.classList.remove('active'));

                // تفعيل الفئة المختارة
                tab.classList.add('active');
                document.getElementById(`${category}-content`).classList.add('active');

                // مسح اختيار المواد السابق
                document.querySelectorAll('input[name="prosthetic-material"]').forEach(input => {
                    input.checked = false;
                });
            });
        });

        // إعداد اختيار المواد
        document.querySelectorAll('input[name="prosthetic-material"]').forEach(input => {
            input.addEventListener('change', () => {
                this.updateProstheticType();
                this.calculatePrice();
            });
        });
    }

    // ========================================
    // إعداد اختيار الأسنان - Setup Teeth Selection
    // ========================================

    setupTeethSelection() {
        const teeth = document.querySelectorAll('.tooth');

        teeth.forEach(tooth => {
            tooth.addEventListener('click', () => {
                const toothId = tooth.dataset.tooth;
                this.toggleToothById(toothId);
                this.updateStats();
            });
        });
    }

    // ========================================
    // دوال التحكم السريع في الأسنان
    // ========================================

    selectAllTeeth() {
        const teeth = document.querySelectorAll('.tooth');
        this.selectedTeeth = [];

        teeth.forEach(tooth => {
            const toothId = tooth.dataset.tooth;
            const toothData = this.getToothDataById(toothId);
            if (toothData) {
                tooth.classList.add('selected');
                this.selectedTeeth.push(toothData);
            }
        });

        this.updateSelectedTeethDisplay();
        this.updateStats();
        this.calculatePrice();
    }

    clearAllTeeth() {
        const teeth = document.querySelectorAll('.tooth');
        teeth.forEach(tooth => {
            tooth.classList.remove('selected');
        });

        this.selectedTeeth = [];
        this.updateSelectedTeethDisplay();
        this.updateStats();
        this.calculatePrice();
    }

    selectUpperJaw() {
        const upperTeeth = document.querySelectorAll('.upper-teeth .tooth');

        upperTeeth.forEach(tooth => {
            const toothId = tooth.dataset.tooth;
            const toothData = this.getToothDataById(toothId);
            if (toothData && !this.selectedTeeth.find(t => t.uniqueId === toothId)) {
                tooth.classList.add('selected');
                this.selectedTeeth.push(toothData);
            }
        });

        this.updateSelectedTeethDisplay();
        this.updateStats();
        this.calculatePrice();
    }

    selectLowerJaw() {
        const lowerTeeth = document.querySelectorAll('.lower-teeth .tooth');

        lowerTeeth.forEach(tooth => {
            const toothId = tooth.dataset.tooth;
            const toothData = this.getToothDataById(toothId);
            if (toothData && !this.selectedTeeth.find(t => t.uniqueId === toothId)) {
                tooth.classList.add('selected');
                this.selectedTeeth.push(toothData);
            }
        });

        this.updateSelectedTeethDisplay();
        this.updateStats();
        this.calculatePrice();
    }

    // ========================================
    // تحديث الإحصائيات
    // ========================================

    updateStats() {
        const selectedCount = this.selectedTeeth.length;
        const totalTeeth = document.querySelectorAll('.tooth').length;
        const percentage = Math.round((selectedCount / totalTeeth) * 100);

        const selectedCountEl = document.getElementById('selected-count');
        const totalTeethEl = document.getElementById('total-teeth');
        const percentageEl = document.getElementById('selection-percentage');
        const selectedTeethCountEl = document.getElementById('selected-teeth-count');

        if (selectedCountEl) selectedCountEl.textContent = selectedCount;
        if (totalTeethEl) totalTeethEl.textContent = totalTeeth;
        if (percentageEl) percentageEl.textContent = percentage + '%';
        if (selectedTeethCountEl) selectedTeethCountEl.textContent = selectedCount;
    }

    // ========================================
    // تبديل اختيار السن بالمعرف - Toggle Tooth Selection By ID
    // ========================================

    toggleToothById(toothId) {
        const toothElement = document.querySelector(`[data-tooth="${toothId}"]`);
        if (!toothElement) return;

        const index = this.selectedTeeth.indexOf(toothId);

        if (index > -1) {
            // إلغاء اختيار السن
            this.selectedTeeth.splice(index, 1);
            toothElement.classList.remove('selected');
        } else {
            // اختيار السن
            this.selectedTeeth.push(toothId);
            toothElement.classList.add('selected');
        }

        // تحديث العداد والعرض
        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    // ========================================
    // تبديل اختيار السن (للتوافق مع النظام القديم) - Toggle Tooth Selection
    // ========================================

    toggleTooth(toothNumber) {
        // البحث عن السن بالرقم في النظام الجديد
        const toothElement = document.querySelector(`[data-number="${toothNumber}"]`);
        if (toothElement) {
            const toothId = toothElement.dataset.tooth;
            this.toggleToothById(toothId);
        }
    }

    // ========================================
    // تحديث عرض الأسنان المختارة - Update Selected Teeth Display
    // ========================================

    updateSelectedTeethDisplay() {
        const countElement = document.getElementById('selected-teeth-count');
        const displayElement = document.getElementById('selected-teeth-display');
        const calcCountElement = document.getElementById('calc-teeth-count');

        const count = this.selectedTeeth.length;

        if (countElement) countElement.textContent = count;
        if (calcCountElement) calcCountElement.textContent = count;

        if (displayElement) {
            if (count === 0) {
                displayElement.innerHTML = '<span class="no-teeth">لم يتم اختيار أي أسنان</span>';
            } else {
                displayElement.innerHTML = this.selectedTeeth.map(toothId => {
                    const toothElement = document.querySelector(`[data-tooth="${toothId}"]`);
                    const toothNumber = toothElement ? toothElement.dataset.number : toothId;
                    return `<span class="tooth-tag" onclick="advancedProstheticsManager.toggleToothById('${toothId}')">
                        ${toothNumber} <i class="fas fa-times"></i>
                    </span>`;
                }).join('');
            }
        }
    }

    // ========================================
    // اختيار جميع الأسنان - Select All Teeth
    // ========================================

    selectAllTeeth() {
        this.selectedTeeth = [];
        document.querySelectorAll('.tooth').forEach(tooth => {
            this.selectedTeeth.push(tooth.dataset.tooth);
        });
        this.updateTeethVisual();
        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    // ========================================
    // إلغاء اختيار جميع الأسنان - Clear All Teeth
    // ========================================

    clearAllTeeth() {
        this.selectedTeeth = [];
        this.updateTeethVisual();
        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    // ========================================
    // اختيار الفك العلوي - Select Upper Jaw
    // ========================================

    selectUpperJaw() {
        this.selectedTeeth = [];
        document.querySelectorAll('.upper-teeth .tooth').forEach(tooth => {
            this.selectedTeeth.push(tooth.dataset.tooth);
        });
        this.updateTeethVisual();
        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    // ========================================
    // اختيار الفك السفلي - Select Lower Jaw
    // ========================================

    selectLowerJaw() {
        this.selectedTeeth = [];
        document.querySelectorAll('.lower-teeth .tooth').forEach(tooth => {
            this.selectedTeeth.push(tooth.dataset.tooth);
        });
        this.updateTeethVisual();
        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    // ========================================
    // تحديث المظهر البصري للأسنان - Update Teeth Visual
    // ========================================

    updateTeethVisual() {
        document.querySelectorAll('.tooth').forEach(tooth => {
            const toothId = tooth.dataset.tooth;
            if (this.selectedTeeth.includes(toothId)) {
                tooth.classList.add('selected');
            } else {
                tooth.classList.remove('selected');
            }
        });
    }

    // ========================================
    // إعداد حساب الأسعار - Setup Price Calculation
    // ========================================

    setupPriceCalculation() {
        const unitPriceInput = document.getElementById('unit-price');
        const discountPercentageInput = document.getElementById('discount-percentage');
        const discountAmountInput = document.getElementById('discount-amount');

        if (unitPriceInput) {
            unitPriceInput.addEventListener('input', () => {
                this.calculatePrice();
            });
        }

        if (discountPercentageInput) {
            discountPercentageInput.addEventListener('input', () => {
                this.calculateDiscountAmount();
                this.calculatePrice();
            });
        }

        if (discountAmountInput) {
            discountAmountInput.addEventListener('input', () => {
                this.calculateDiscountPercentage();
                this.calculatePrice();
            });
        }
    }

    // ========================================
    // حساب السعر - Calculate Price
    // ========================================

    async calculatePrice() {
        const unitPrice = parseFloat(document.getElementById('unit-price')?.value || 0);
        const discountPercentage = parseFloat(document.getElementById('discount-percentage')?.value || 0);
        const teethCount = this.selectedTeeth.length;

        let basePrice = 0;
        let finalPrice = 0;

        // التحقق من نوع التركيبة المختار
        const selectedMaterial = document.querySelector('input[name="prosthetic-material"]:checked')?.value;

        if (selectedMaterial === 'برشل جزئى' && teethCount > 0) {
            // حساب خاص للبرشل الجزئي
            const firstToothPrice = await this.getSystemSetting('partial_denture_first_tooth_price', 150);
            const additionalToothPrice = await this.getSystemSetting('partial_denture_additional_tooth_price', 90);

            basePrice = firstToothPrice + ((teethCount - 1) * additionalToothPrice);
        } else {
            // حساب عادي
            basePrice = teethCount * unitPrice;
        }

        // حساب الخصم
        const discountAmount = (basePrice * discountPercentage) / 100;
        finalPrice = basePrice - discountAmount;

        // تحديث العرض
        this.updatePriceDisplay(teethCount, basePrice, discountAmount, finalPrice);
    }

    // ========================================
    // تحديث عرض الأسعار - Update Price Display
    // ========================================

    updatePriceDisplay(teethCount, basePrice, discountAmount, finalPrice) {
        const elements = {
            'calc-teeth-count': teethCount,
            'calc-base-price': `${basePrice.toFixed(2)} جنيه`,
            'calc-discount': `${discountAmount.toFixed(2)} جنيه`,
            'calc-final-price': `${finalPrice.toFixed(2)} جنيه`
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });

        // تحديث حقل مبلغ الخصم
        const discountAmountInput = document.getElementById('discount-amount');
        if (discountAmountInput) {
            discountAmountInput.value = discountAmount.toFixed(2);
        }
    }

    // ========================================
    // حساب مبلغ الخصم من النسبة - Calculate Discount Amount
    // ========================================

    calculateDiscountAmount() {
        const unitPrice = parseFloat(document.getElementById('unit-price')?.value || 0);
        const discountPercentage = parseFloat(document.getElementById('discount-percentage')?.value || 0);
        const teethCount = this.selectedTeeth.length;
        const basePrice = teethCount * unitPrice;
        const discountAmount = (basePrice * discountPercentage) / 100;

        const discountAmountInput = document.getElementById('discount-amount');
        if (discountAmountInput) {
            discountAmountInput.value = discountAmount.toFixed(2);
        }
    }

    // ========================================
    // حساب نسبة الخصم من المبلغ - Calculate Discount Percentage
    // ========================================

    calculateDiscountPercentage() {
        const unitPrice = parseFloat(document.getElementById('unit-price')?.value || 0);
        const discountAmount = parseFloat(document.getElementById('discount-amount')?.value || 0);
        const teethCount = this.selectedTeeth.length;
        const basePrice = teethCount * unitPrice;

        if (basePrice > 0) {
            const discountPercentage = (discountAmount / basePrice) * 100;
            const discountPercentageInput = document.getElementById('discount-percentage');
            if (discountPercentageInput) {
                discountPercentageInput.value = discountPercentage.toFixed(1);
            }
        }
    }

    // ========================================
    // تحديث نوع التركيبة - Update Prosthetic Type
    // ========================================

    updateProstheticType() {
        const selectedMaterial = document.querySelector('input[name="prosthetic-material"]:checked');
        if (selectedMaterial) {
            const activeTab = document.querySelector('.category-tab.active');
            if (activeTab) {
                const category = activeTab.dataset.category;
                // يمكن إضافة منطق إضافي هنا حسب الحاجة
            }
        }
    }

    // ========================================
    // الحصول على إعداد النظام - Get System Setting
    // ========================================

    async getSystemSetting(key, defaultValue) {
        try {
            const settings = await db.findAll('system_settings', {
                where: { setting_key: key }
            });

            if (settings.length > 0) {
                const value = settings[0].setting_value;
                return settings[0].setting_type === 'number' ? parseFloat(value) : value;
            }

            return defaultValue;
        } catch (error) {
            console.error('خطأ في الحصول على إعداد النظام:', error);
            return defaultValue;
        }
    }

    // ========================================
    // توليد وتعيين رقم الحالة - Generate and Set Case Number
    // ========================================

    async generateAndSetCaseNumber() {
        const caseNumber = await this.generateCaseNumber();
        const caseNumberInput = document.getElementById('case-number');
        if (caseNumberInput) {
            caseNumberInput.value = caseNumber;
        }
    }

    // ========================================
    // حفظ التركيبة - Save Prosthetic
    // ========================================

    async saveProsthetic() {
        try {
            // التحقق من صحة البيانات
            const validationResult = this.validateProstheticData();
            if (!validationResult.isValid) {
                showError(validationResult.message);
                return;
            }

            // جمع البيانات
            const prostheticData = this.collectProstheticData();

            // حفظ في قاعدة البيانات
            const result = await db.insert('prosthetics', prostheticData);

            if (result) {
                showSuccess('تم حفظ التركيبة بنجاح');
                this.closeNewProstheticModal();
                await this.refreshData();

                // تسجيل النشاط
                await this.logActivity('prosthetic_created',
                    `تم إنشاء تركيبة جديدة: ${prostheticData.prosthetic_type} للمريض ${prostheticData.patient_name}`,
                    'prosthetics', result.id);
            } else {
                showError('فشل في حفظ التركيبة');
            }

        } catch (error) {
            console.error('خطأ في حفظ التركيبة:', error);
            showError('حدث خطأ أثناء حفظ التركيبة');
        }
    }

    // ========================================
    // التحقق من صحة بيانات التركيبة - Validate Prosthetic Data
    // ========================================

    validateProstheticData() {
        const caseNumber = document.getElementById('case-number')?.value.trim();
        const doctorId = document.getElementById('doctor-select')?.value;
        const patientName = document.getElementById('patient-name')?.value.trim();
        const selectedMaterial = document.querySelector('input[name="prosthetic-material"]:checked');
        const expectedDelivery = document.getElementById('expected-delivery')?.value;
        const unitPrice = document.getElementById('unit-price')?.value;

        if (!caseNumber) {
            return { isValid: false, message: 'رقم الحالة مطلوب' };
        }

        if (!doctorId) {
            return { isValid: false, message: 'يرجى اختيار الطبيب' };
        }

        if (!patientName) {
            return { isValid: false, message: 'يرجى إدخال اسم المريض' };
        }

        if (!selectedMaterial) {
            return { isValid: false, message: 'يرجى اختيار نوع التركيبة' };
        }

        if (this.selectedTeeth.length === 0) {
            return { isValid: false, message: 'يرجى اختيار الأسنان المطلوبة' };
        }

        if (!expectedDelivery) {
            return { isValid: false, message: 'يرجى تحديد تاريخ التسليم المتوقع' };
        }

        if (!unitPrice || parseFloat(unitPrice) <= 0) {
            return { isValid: false, message: 'يرجى إدخال سعر صحيح' };
        }

        return { isValid: true };
    }

    // ========================================
    // جمع بيانات التركيبة - Collect Prosthetic Data
    // ========================================

    collectProstheticData() {
        const selectedMaterial = document.querySelector('input[name="prosthetic-material"]:checked').value;
        const activeCategory = document.querySelector('.category-tab.active').dataset.category;
        const unitPrice = parseFloat(document.getElementById('unit-price').value);
        const discountPercentage = parseFloat(document.getElementById('discount-percentage')?.value || 0);
        const teethCount = this.selectedTeeth.length;

        // حساب الأسعار
        let totalPrice = 0;
        let specialCalculation = null;

        if (selectedMaterial === 'برشل جزئى' && teethCount > 0) {
            // حساب خاص للبرشل الجزئي
            const firstToothPrice = 150; // سيتم الحصول عليه من الإعدادات
            const additionalToothPrice = 90;
            totalPrice = firstToothPrice + ((teethCount - 1) * additionalToothPrice);

            specialCalculation = {
                type: 'partial_denture',
                first_tooth_price: firstToothPrice,
                additional_tooth_price: additionalToothPrice,
                calculation: `${firstToothPrice} + (${teethCount - 1} × ${additionalToothPrice}) = ${totalPrice}`
            };
        } else {
            totalPrice = teethCount * unitPrice;
        }

        const discountAmount = (totalPrice * discountPercentage) / 100;
        const finalPrice = totalPrice - discountAmount;

        return {
            case_number: document.getElementById('case-number').value.trim(),
            doctor_id: parseInt(document.getElementById('doctor-select').value),
            patient_name: document.getElementById('patient-name').value.trim(),
            patient_phone: document.getElementById('patient-phone')?.value.trim() || null,
            prosthetic_category: activeCategory,
            prosthetic_type: selectedMaterial,
            material: selectedMaterial,
            selected_teeth: JSON.stringify(this.selectedTeeth),
            teeth_count: teethCount,
            unit_price: unitPrice,
            total_price: totalPrice,
            discount_percentage: discountPercentage,
            discount_amount: discountAmount,
            final_price: finalPrice,
            special_calculation: specialCalculation ? JSON.stringify(specialCalculation) : null,
            expected_delivery: document.getElementById('expected-delivery').value,
            priority: document.getElementById('priority').value,
            special_instructions: document.getElementById('special-instructions')?.value.trim() || null,
            notes: document.getElementById('notes')?.value.trim() || null,
            status: 'pending',
            payment_status: 'pending',
            remaining_amount: finalPrice,
            created_by: getCurrentUser()?.name || 'النظام'
        };
    }

    // ========================================
    // إغلاق نافذة التركيبة الجديدة - Close New Prosthetic Modal
    // ========================================

    closeNewProstheticModal() {
        const modal = document.getElementById('new-prosthetic-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetNewProstheticForm();
        }
    }

    // ========================================
    // إعادة تعيين نموذج التركيبة الجديدة - Reset New Prosthetic Form
    // ========================================

    resetNewProstheticForm() {
        this.selectedTeeth = [];
        this.currentProsthetic = null;

        const form = document.getElementById('new-prosthetic-form');
        if (form) {
            form.reset();
        }
    }

    // ========================================
    // تسجيل النشاط - Log Activity
    // ========================================

    async logActivity(type, description, entityType = null, entityId = null) {
        try {
            const currentUser = getCurrentUser();
            const activityData = {
                activity_type: type,
                description: description,
                entity_type: entityType,
                entity_id: entityId,
                user_name: currentUser ? currentUser.name : 'مستخدم غير معروف',
                user_role: currentUser ? currentUser.role : 'unknown',
                details: JSON.stringify({
                    timestamp: new Date().toISOString(),
                    module: 'prosthetics'
                }),
                priority: 'normal',
                status: 'success'
            };

            await db.insert('activity_logs', activityData);
        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    // ========================================
    // تحديث البيانات - Refresh Data
    // ========================================

    async refreshData() {
        await this.loadInitialData();
        this.renderProstheticsList();
    }

    // ========================================
    // الحصول على فئة الحالة - Get Status Class
    // ========================================

    getStatusClass(status) {
        const statusClasses = {
            'pending': 'warning',
            'in_progress': 'info',
            'completed': 'success',
            'delivered': 'primary',
            'cancelled': 'error'
        };
        return statusClasses[status] || 'secondary';
    }

    // ========================================
    // الحصول على نص الحالة - Get Status Text
    // ========================================

    getStatusText(status) {
        const statusTexts = {
            'pending': 'في الانتظار',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتملة',
            'delivered': 'تم التسليم',
            'cancelled': 'ملغية'
        };
        return statusTexts[status] || 'غير محدد';
    }

    // ========================================
    // تصفية التركيبات - Filter Prosthetics
    // ========================================

    filterProsthetics() {
        const searchTerm = document.getElementById('prosthetics-search')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('status-filter')?.value || '';
        const doctorFilter = document.getElementById('doctor-filter')?.value || '';
        const categoryFilter = document.getElementById('category-filter')?.value || '';
        const dateFrom = document.getElementById('date-from')?.value || '';
        const dateTo = document.getElementById('date-to')?.value || '';

        let filteredProsthetics = [...this.prosthetics];

        // تطبيق البحث النصي
        if (searchTerm) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic => {
                const doctor = this.doctors.find(d => d.id === prosthetic.doctor_id);
                return prosthetic.case_number.toLowerCase().includes(searchTerm) ||
                       prosthetic.patient_name.toLowerCase().includes(searchTerm) ||
                       (doctor && doctor.name.toLowerCase().includes(searchTerm));
            });
        }

        // تطبيق تصفية الحالة
        if (statusFilter) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic =>
                prosthetic.status === statusFilter
            );
        }

        // تطبيق تصفية الطبيب
        if (doctorFilter) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic =>
                prosthetic.doctor_id === parseInt(doctorFilter)
            );
        }

        // تطبيق تصفية النوع
        if (categoryFilter) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic =>
                prosthetic.prosthetic_category === categoryFilter
            );
        }

        // تطبيق تصفية التاريخ
        if (dateFrom || dateTo) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic => {
                const prostheticDate = new Date(prosthetic.createdAt);
                const fromDate = dateFrom ? new Date(dateFrom) : new Date('1900-01-01');
                const toDate = dateTo ? new Date(dateTo) : new Date('2100-12-31');

                return prostheticDate >= fromDate && prostheticDate <= toDate;
            });
        }

        // تحديث العرض
        const originalProsthetics = this.prosthetics;
        this.prosthetics = filteredProsthetics;
        this.renderProstheticsList();
        this.prosthetics = originalProsthetics;
    }

    // ========================================
    // وظائف الإجراءات - Action Functions
    // ========================================

    viewProsthetic(id) {
        // TODO: تنفيذ عرض تفاصيل التركيبة
        console.log('عرض تفاصيل التركيبة:', id);
        showInfo('عرض التفاصيل - قيد التطوير');
    }

    editProsthetic(id) {
        // TODO: تنفيذ تعديل التركيبة
        console.log('تعديل التركيبة:', id);
        showInfo('تعديل التركيبة - قيد التطوير');
    }

    updateStatus(id) {
        // TODO: تنفيذ تحديث حالة التركيبة
        console.log('تحديث حالة التركيبة:', id);
        showInfo('تحديث الحالة - قيد التطوير');
    }

    printProsthetic(id) {
        // TODO: تنفيذ طباعة التركيبة
        console.log('طباعة التركيبة:', id);
        showInfo('طباعة التركيبة - قيد التطوير');
    }

    async deleteProsthetic(id) {
        if (confirm('هل أنت متأكد من حذف هذه التركيبة؟')) {
            try {
                await db.delete('prosthetics', id);
                showSuccess('تم حذف التركيبة بنجاح');
                await this.refreshData();

                // تسجيل النشاط
                await this.logActivity('prosthetic_deleted',
                    `تم حذف التركيبة رقم ${id}`, 'prosthetics', id);
            } catch (error) {
                console.error('خطأ في حذف التركيبة:', error);
                showError('فشل في حذف التركيبة');
            }
        }
    }

    generateBulkInvoice() {
        // TODO: تنفيذ الفاتورة المجمعة
        showInfo('الفاتورة المجمعة - قيد التطوير');
    }

    generateDoctorStatement() {
        // TODO: تنفيذ كشف حساب الطبيب
        showInfo('كشف حساب الطبيب - قيد التطوير');
    }

    exportProsthetics() {
        // TODO: تنفيذ تصدير التركيبات
        showInfo('تصدير التركيبات - قيد التطوير');
    }

    sortTable(column) {
        // TODO: تنفيذ ترتيب الجدول
        console.log('ترتيب حسب:', column);
        showInfo('ترتيب الجدول - قيد التطوير');
    }
}

// ========================================
// فئة حساب الأسعار المتقدمة - Advanced Price Calculator
// ========================================

class PriceCalculator {
    constructor() {
        this.specialRules = {
            'برشل جزئى': {
                type: 'partial_denture',
                firstToothPrice: 150,
                additionalToothPrice: 90,
                calculate: (teethCount) => {
                    if (teethCount === 0) return 0;
                    return this.specialRules['برشل جزئى'].firstToothPrice +
                           ((teethCount - 1) * this.specialRules['برشل جزئى'].additionalToothPrice);
                }
            }
        };
    }

    // ========================================
    // حساب السعر الأساسي - Calculate Base Price
    // ========================================

    calculateBasePrice(material, teethCount, unitPrice) {
        if (this.specialRules[material]) {
            return this.specialRules[material].calculate(teethCount);
        }

        return teethCount * unitPrice;
    }

    // ========================================
    // حساب الخصم - Calculate Discount
    // ========================================

    calculateDiscount(basePrice, discountPercentage, discountAmount = null) {
        if (discountAmount !== null) {
            return {
                amount: discountAmount,
                percentage: basePrice > 0 ? (discountAmount / basePrice) * 100 : 0
            };
        }

        const amount = (basePrice * discountPercentage) / 100;
        return {
            amount: amount,
            percentage: discountPercentage
        };
    }

    // ========================================
    // حساب السعر النهائي - Calculate Final Price
    // ========================================

    calculateFinalPrice(basePrice, discount) {
        return Math.max(0, basePrice - discount.amount);
    }

    // ========================================
    // حساب شامل - Complete Calculation
    // ========================================

    calculate(material, teethCount, unitPrice, discountPercentage = 0, discountAmount = null) {
        const basePrice = this.calculateBasePrice(material, teethCount, unitPrice);
        const discount = this.calculateDiscount(basePrice, discountPercentage, discountAmount);
        const finalPrice = this.calculateFinalPrice(basePrice, discount);

        return {
            teethCount,
            unitPrice,
            basePrice,
            discount,
            finalPrice,
            specialCalculation: this.specialRules[material] ? {
                type: this.specialRules[material].type,
                details: this.getSpecialCalculationDetails(material, teethCount)
            } : null
        };
    }

    // ========================================
    // تفاصيل الحساب الخاص - Get Special Calculation Details
    // ========================================

    getSpecialCalculationDetails(material, teethCount) {
        if (material === 'برشل جزئى') {
            const rule = this.specialRules[material];
            return {
                firstToothPrice: rule.firstToothPrice,
                additionalToothPrice: rule.additionalToothPrice,
                calculation: `${rule.firstToothPrice} + (${teethCount - 1} × ${rule.additionalToothPrice})`
            };
        }

        return null;
    }
}

// ========================================
// دوال مساعدة - Helper Functions
// ========================================

// دالة تأخير للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2
    }).format(amount);
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';

    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// الحصول على المستخدم الحالي
function getCurrentUser() {
    // محاكاة - سيتم استبدالها بالنظام الحقيقي
    return {
        name: 'مدير النظام',
        role: 'admin'
    };
}

// عرض رسائل النجاح
function showSuccess(message) {
    console.log('✅ نجح:', message);
    // TODO: إضافة نظام الإشعارات
}

// عرض رسائل الخطأ
function showError(message) {
    console.error('❌ خطأ:', message);
    // TODO: إضافة نظام الإشعارات
}

// عرض رسائل المعلومات
function showInfo(message) {
    console.log('ℹ️ معلومات:', message);
    // TODO: إضافة نظام الإشعارات
}

// ========================================
// تهيئة النظام - System Initialization
// ========================================

// إنشاء مثيل من مدير التركيبات المتقدم
let advancedProstheticsManager;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    try {
        advancedProstheticsManager = new AdvancedProstheticsManager();
        await advancedProstheticsManager.init();
        console.log('✅ تم تهيئة نظام التركيبات المتقدم بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تهيئة نظام التركيبات المتقدم:', error);
    }
});

console.log('✅ تم تحميل نظام التركيبات المتقدم بنجاح');
