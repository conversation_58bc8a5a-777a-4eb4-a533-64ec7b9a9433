// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// وحدة إدارة التركيبات - Prosthetics Module
// ========================================

console.log('🦷 تحميل وحدة إدارة التركيبات...');

// ========================================
// فئة إدارة التركيبات - Prosthetics Manager
// ========================================

class ProstheticsManager {
    constructor() {
        this.prosthetics = [];
        this.prostheticTypes = [];
        this.doctors = [];
        this.selectedTeeth = [];
        this.currentProsthetic = null;
        this.teethChart = null;
        this.priceCalculator = new ProstheticPriceCalculator();
    }

    // ========================================
    // تهيئة الوحدة - Initialize Module
    // ========================================

    async init() {
        try {
            console.log('🔄 تهيئة وحدة التركيبات...');
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            // إعداد نظام اختيار الأسنان
            this.setupTeethSelection();
            
            console.log('✅ تم تهيئة وحدة التركيبات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة وحدة التركيبات:', error);
            return false;
        }
    }

    // ========================================
    // تحميل البيانات الأولية - Load Initial Data
    // ========================================

    async loadInitialData() {
        try {
            // تحميل التركيبات
            this.prosthetics = await db.findAll('prosthetics');
            
            // تحميل أنواع التركيبات
            this.prostheticTypes = await db.findAll('prosthetic_types');
            
            // تحميل الأطباء
            this.doctors = await db.findAll('doctors');
            
            console.log(`📊 تم تحميل ${this.prosthetics.length} تركيبة`);
            console.log(`📋 تم تحميل ${this.prostheticTypes.length} نوع تركيبة`);
            console.log(`👨‍⚕️ تم تحميل ${this.doctors.length} طبيب`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            throw error;
        }
    }

    // ========================================
    // عرض وحدة التركيبات - Render Prosthetics Module
    // ========================================

    async render() {
        try {
            const contentArea = document.getElementById('content-area');
            if (!contentArea) return;

            // إنشاء HTML للوحدة
            contentArea.innerHTML = this.getProstheticsHTML();
            
            // تحميل البيانات وعرضها
            await this.loadInitialData();
            
            // عرض قائمة التركيبات
            this.renderProstheticsList();
            
            // عرض أنواع التركيبات
            this.renderProstheticTypes();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
            // إعداد نظام اختيار الأسنان
            this.initializeTeethChart();
            
        } catch (error) {
            console.error('خطأ في عرض وحدة التركيبات:', error);
            showError('فشل في تحميل وحدة التركيبات');
        }
    }

    // ========================================
    // إنشاء HTML للوحدة - Get Prosthetics HTML
    // ========================================

    getProstheticsHTML() {
        return `
            <div class="prosthetics-container">
                <!-- شريط الأدوات العلوي -->
                <div class="prosthetics-toolbar">
                    <div class="toolbar-left">
                        <h1 class="page-title">
                            <i class="fas fa-tooth"></i>
                            إدارة التركيبات
                        </h1>
                        <p class="page-subtitle">تسجيل وإدارة جميع أنواع التركيبات</p>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="prostheticsManager.showNewProstheticModal()">
                            <i class="fas fa-plus"></i>
                            تركيبة جديدة
                        </button>
                        <button class="btn btn-outline" onclick="prostheticsManager.refreshData()">
                            <i class="fas fa-sync"></i>
                            تحديث
                        </button>
                        <button class="btn btn-outline" onclick="prostheticsManager.exportProsthetics()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="prosthetics-stats">
                    <div class="stat-card stat-card-primary">
                        <div class="stat-icon">
                            <i class="fas fa-tooth"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-prosthetics" class="stat-value">0</h3>
                            <p class="stat-title">إجمالي التركيبات</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pending-prosthetics" class="stat-value">0</h3>
                            <p class="stat-title">قيد التنفيذ</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-success">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completed-prosthetics" class="stat-value">0</h3>
                            <p class="stat-title">مكتملة</p>
                        </div>
                    </div>
                    <div class="stat-card stat-card-info">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="monthly-revenue" class="stat-value">0</h3>
                            <p class="stat-title">إيرادات الشهر</p>
                        </div>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="prosthetics-content">
                    
                    <!-- شريط البحث والتصفية -->
                    <div class="prosthetics-filters">
                        <div class="filter-group">
                            <input type="search" id="prosthetics-search" class="form-control" placeholder="البحث في التركيبات...">
                        </div>
                        <div class="filter-group">
                            <select id="status-filter" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="pending">قيد التنفيذ</option>
                                <option value="in_progress">جاري العمل</option>
                                <option value="completed">مكتمل</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <select id="category-filter" class="form-control">
                                <option value="">جميع الأنواع</option>
                                <option value="porcelain">بورسلين</option>
                                <option value="zirconia">زيركون</option>
                                <option value="metal">معدن</option>
                                <option value="dentures">أطقم</option>
                                <option value="orthodontics">تقويم</option>
                                <option value="additional">إضافية</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <select id="doctor-filter" class="form-control">
                                <option value="">جميع الأطباء</option>
                            </select>
                        </div>
                    </div>

                    <!-- قائمة التركيبات -->
                    <div class="prosthetics-list-container">
                        <div class="table-container">
                            <table class="table prosthetics-table">
                                <thead>
                                    <tr>
                                        <th>رقم التركيبة</th>
                                        <th>اسم المريض</th>
                                        <th>الطبيب</th>
                                        <th>نوع التركيبة</th>
                                        <th>الأسنان</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسليم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="prosthetics-table-body">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>

            <!-- نافذة تركيبة جديدة -->
            <div id="new-prosthetic-modal" class="modal hidden">
                <div class="modal-overlay"></div>
                <div class="modal-content large-modal">
                    <div class="modal-header">
                        <h2>تسجيل تركيبة جديدة</h2>
                        <button class="modal-close" onclick="prostheticsManager.closeNewProstheticModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="new-prosthetic-form" class="modal-body">
                        <!-- معلومات المريض والطبيب -->
                        <div class="form-section">
                            <h3 class="section-title">معلومات المريض والطبيب</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="patient-name" class="form-label">اسم المريض *</label>
                                    <input type="text" id="patient-name" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="patient-phone" class="form-label">هاتف المريض</label>
                                    <input type="tel" id="patient-phone" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="doctor-select" class="form-label">الطبيب *</label>
                                    <select id="doctor-select" class="form-control" required>
                                        <option value="">اختر الطبيب</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- نوع التركيبة -->
                        <div class="form-section">
                            <h3 class="section-title">نوع التركيبة</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="prosthetic-category" class="form-label">فئة التركيبة *</label>
                                    <select id="prosthetic-category" class="form-control" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="porcelain">تركيبات البورسلين</option>
                                        <option value="zirconia">تركيبات الزيركون</option>
                                        <option value="metal">التركيبات المعدنية</option>
                                        <option value="dentures">الأطقم المتحركة</option>
                                        <option value="orthodontics">أجهزة التقويم والحفظ</option>
                                        <option value="additional">أعمال إضافية متخصصة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="prosthetic-type" class="form-label">نوع التركيبة *</label>
                                    <select id="prosthetic-type" class="form-control" required>
                                        <option value="">اختر النوع</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="prosthetic-material" class="form-label">المادة</label>
                                    <input type="text" id="prosthetic-material" class="form-control" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار الأسنان -->
                        <div class="form-section">
                            <h3 class="section-title">اختيار الأسنان</h3>
                            <div class="teeth-selection-container">
                                <div id="teeth-chart" class="teeth-chart">
                                    <!-- سيتم إنشاؤها ديناميكياً -->
                                </div>
                                <div class="selected-teeth-info">
                                    <h4>الأسنان المختارة:</h4>
                                    <div id="selected-teeth-list" class="selected-teeth-list">
                                        <p class="no-teeth-selected">لم يتم اختيار أسنان بعد</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل إضافية -->
                        <div class="form-section">
                            <h3 class="section-title">تفاصيل إضافية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="color-shade" class="form-label">درجة اللون</label>
                                    <select id="color-shade" class="form-control">
                                        <option value="">اختر اللون</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select id="priority" class="form-control">
                                        <option value="normal">عادية</option>
                                        <option value="high">عالية</option>
                                        <option value="urgent">عاجلة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="delivery-date" class="form-label">تاريخ التسليم</label>
                                    <input type="date" id="delivery-date" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- حساب السعر -->
                        <div class="form-section">
                            <h3 class="section-title">حساب السعر</h3>
                            <div class="price-calculation">
                                <div class="price-row">
                                    <span class="price-label">عدد الأسنان:</span>
                                    <span id="teeth-count" class="price-value">0</span>
                                </div>
                                <div class="price-row">
                                    <span class="price-label">سعر الوحدة:</span>
                                    <span id="unit-price" class="price-value">0 ريال</span>
                                </div>
                                <div class="price-row">
                                    <span class="price-label">السعر الإجمالي:</span>
                                    <span id="total-price" class="price-value">0 ريال</span>
                                </div>
                                <div class="price-row">
                                    <span class="price-label">خصم الطبيب:</span>
                                    <span id="doctor-discount" class="price-value">0%</span>
                                </div>
                                <div class="price-row">
                                    <span class="price-label">مبلغ الخصم:</span>
                                    <span id="discount-amount" class="price-value">0 ريال</span>
                                </div>
                                <div class="price-row total-row">
                                    <span class="price-label">السعر النهائي:</span>
                                    <span id="final-price" class="price-value">0 ريال</span>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="form-section">
                            <h3 class="section-title">ملاحظات وتعليمات</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="special-instructions" class="form-label">تعليمات خاصة</label>
                                    <textarea id="special-instructions" class="form-control" rows="3" placeholder="أي تعليمات خاصة للمعمل..."></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="notes" class="form-label">ملاحظات عامة</label>
                                    <textarea id="notes" class="form-control" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="prostheticsManager.closeNewProstheticModal()">
                            إلغاء
                        </button>
                        <button type="submit" form="new-prosthetic-form" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التركيبة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // ========================================
    // إعداد الأحداث - Setup Event Listeners
    // ========================================

    setupEventListeners() {
        // نموذج تركيبة جديدة
        const newProstheticForm = document.getElementById('new-prosthetic-form');
        if (newProstheticForm) {
            newProstheticForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveProsthetic();
            });
        }

        // تغيير فئة التركيبة
        const categorySelect = document.getElementById('prosthetic-category');
        if (categorySelect) {
            categorySelect.addEventListener('change', () => {
                this.updateProstheticTypes();
            });
        }

        // تغيير نوع التركيبة
        const typeSelect = document.getElementById('prosthetic-type');
        if (typeSelect) {
            typeSelect.addEventListener('change', () => {
                this.updateProstheticDetails();
                this.calculatePrice();
            });
        }

        // تغيير الطبيب
        const doctorSelect = document.getElementById('doctor-select');
        if (doctorSelect) {
            doctorSelect.addEventListener('change', () => {
                this.calculatePrice();
            });
        }

        // البحث والتصفية
        const searchInput = document.getElementById('prosthetics-search');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(() => {
                this.filterProsthetics();
            }, 300));
        }

        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filterProsthetics();
            });
        }

        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.filterProsthetics();
            });
        }

        const doctorFilter = document.getElementById('doctor-filter');
        if (doctorFilter) {
            doctorFilter.addEventListener('change', () => {
                this.filterProsthetics();
            });
        }
    }

    // ========================================
    // عرض قائمة التركيبات - Render Prosthetics List
    // ========================================

    renderProstheticsList() {
        const tableBody = document.getElementById('prosthetics-table-body');
        if (!tableBody) return;

        if (this.prosthetics.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-tooth"></i>
                            <h3>لا توجد تركيبات مسجلة</h3>
                            <p>ابدأ بتسجيل أول تركيبة</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.prosthetics.map(prosthetic => {
            const doctor = this.doctors.find(d => d.id === prosthetic.doctor_id);
            const statusClass = this.getStatusClass(prosthetic.status);
            const statusText = this.getStatusText(prosthetic.status);

            return `
                <tr data-prosthetic-id="${prosthetic.id}">
                    <td>
                        <span class="prosthetic-id">#${prosthetic.id.toString().padStart(4, '0')}</span>
                    </td>
                    <td>
                        <div class="patient-info">
                            <strong>${prosthetic.patient_name}</strong>
                            ${prosthetic.patient_phone ? `<br><small>${prosthetic.patient_phone}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="doctor-name">${doctor ? doctor.name : 'غير محدد'}</span>
                    </td>
                    <td>
                        <div class="prosthetic-type">
                            <strong>${prosthetic.prosthetic_type}</strong>
                            <br><small class="text-secondary">${prosthetic.prosthetic_category}</small>
                        </div>
                    </td>
                    <td>
                        <span class="teeth-count">${this.getTeethDisplay(prosthetic.selected_teeth)}</span>
                    </td>
                    <td>
                        <span class="price">${formatCurrency(prosthetic.final_price)}</span>
                    </td>
                    <td>
                        <span class="status-badge status-${statusClass}">${statusText}</span>
                    </td>
                    <td>
                        <span class="delivery-date">${prosthetic.delivery_date ? formatDate(prosthetic.delivery_date) : '-'}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline" onclick="prostheticsManager.viewProsthetic(${prosthetic.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="prostheticsManager.editProsthetic(${prosthetic.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="prostheticsManager.printProsthetic(${prosthetic.id})" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="prostheticsManager.deleteProsthetic(${prosthetic.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // تحديث الإحصائيات
        this.updateStatistics();
    }

    // ========================================
    // تحديث الإحصائيات - Update Statistics
    // ========================================

    updateStatistics() {
        const totalElement = document.getElementById('total-prosthetics');
        const pendingElement = document.getElementById('pending-prosthetics');
        const completedElement = document.getElementById('completed-prosthetics');
        const revenueElement = document.getElementById('monthly-revenue');

        if (totalElement) totalElement.textContent = this.prosthetics.length;

        const pending = this.prosthetics.filter(p => p.status === 'pending' || p.status === 'in_progress').length;
        if (pendingElement) pendingElement.textContent = pending;

        const completed = this.prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered').length;
        if (completedElement) completedElement.textContent = completed;

        // حساب إيرادات الشهر الحالي
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        const monthlyRevenue = this.prosthetics
            .filter(p => {
                const date = new Date(p.createdAt);
                return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
            })
            .reduce((sum, p) => sum + (p.final_price || 0), 0);

        if (revenueElement) revenueElement.textContent = formatCurrency(monthlyRevenue);
    }

    // ========================================
    // عرض أنواع التركيبات - Render Prosthetic Types
    // ========================================

    renderProstheticTypes() {
        // ملء قائمة الأطباء
        const doctorSelects = document.querySelectorAll('#doctor-select, #doctor-filter');
        doctorSelects.forEach(select => {
            if (select.id === 'doctor-filter') {
                select.innerHTML = '<option value="">جميع الأطباء</option>';
            } else {
                select.innerHTML = '<option value="">اختر الطبيب</option>';
            }

            this.doctors.forEach(doctor => {
                const option = document.createElement('option');
                option.value = doctor.id;
                option.textContent = doctor.name;
                select.appendChild(option);
            });
        });
    }

    // ========================================
    // تحديث أنواع التركيبات حسب الفئة - Update Prosthetic Types by Category
    // ========================================

    updateProstheticTypes() {
        const categorySelect = document.getElementById('prosthetic-category');
        const typeSelect = document.getElementById('prosthetic-type');

        if (!categorySelect || !typeSelect) return;

        const selectedCategory = categorySelect.value;
        typeSelect.innerHTML = '<option value="">اختر النوع</option>';

        if (selectedCategory) {
            const filteredTypes = this.prostheticTypes.filter(type => type.category === selectedCategory);
            filteredTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id;
                option.textContent = type.type_name;
                option.dataset.material = type.material;
                option.dataset.price = type.unit_price;
                option.dataset.colors = type.color_options;
                option.dataset.isPerTooth = type.is_per_tooth;
                option.dataset.specialCalculation = type.special_calculation;
                typeSelect.appendChild(option);
            });
        }

        // مسح التفاصيل
        this.clearProstheticDetails();
    }

    // ========================================
    // تحديث تفاصيل التركيبة - Update Prosthetic Details
    // ========================================

    updateProstheticDetails() {
        const typeSelect = document.getElementById('prosthetic-type');
        const materialInput = document.getElementById('prosthetic-material');
        const colorSelect = document.getElementById('color-shade');

        if (!typeSelect || !materialInput || !colorSelect) return;

        const selectedOption = typeSelect.selectedOptions[0];
        if (!selectedOption || !selectedOption.value) {
            this.clearProstheticDetails();
            return;
        }

        // تحديث المادة
        materialInput.value = selectedOption.dataset.material || '';

        // تحديث خيارات الألوان
        colorSelect.innerHTML = '<option value="">اختر اللون</option>';
        const colors = selectedOption.dataset.colors;
        if (colors) {
            try {
                const colorOptions = JSON.parse(colors);
                colorOptions.forEach(color => {
                    const option = document.createElement('option');
                    option.value = color;
                    option.textContent = color;
                    colorSelect.appendChild(option);
                });
            } catch (error) {
                console.warn('خطأ في تحليل خيارات الألوان:', error);
            }
        }
    }

    // ========================================
    // مسح تفاصيل التركيبة - Clear Prosthetic Details
    // ========================================

    clearProstheticDetails() {
        const materialInput = document.getElementById('prosthetic-material');
        const colorSelect = document.getElementById('color-shade');

        if (materialInput) materialInput.value = '';
        if (colorSelect) colorSelect.innerHTML = '<option value="">اختر اللون</option>';

        this.calculatePrice();
    }

    // ========================================
    // نظام اختيار الأسنان التفاعلي - Interactive Teeth Selection System
    // ========================================

    initializeTeethChart() {
        const teethChart = document.getElementById('teeth-chart');
        if (!teethChart) return;

        teethChart.innerHTML = this.createTeethChartHTML();
        this.setupTeethSelection();
    }

    createTeethChartHTML() {
        // تعريف الأسنان بالترقيم العالمي
        const upperTeeth = [
            { number: 18, name: 'ضرس العقل الأيمن العلوي', type: 'molar' },
            { number: 17, name: 'الضرس الثاني الأيمن العلوي', type: 'molar' },
            { number: 16, name: 'الضرس الأول الأيمن العلوي', type: 'molar' },
            { number: 15, name: 'الضاحك الثاني الأيمن العلوي', type: 'premolar' },
            { number: 14, name: 'الضاحك الأول الأيمن العلوي', type: 'premolar' },
            { number: 13, name: 'الناب الأيمن العلوي', type: 'canine' },
            { number: 12, name: 'الثنية الجانبية اليمنى العلوية', type: 'incisor' },
            { number: 11, name: 'الثنية الوسطى اليمنى العلوية', type: 'incisor' },
            { number: 21, name: 'الثنية الوسطى اليسرى العلوية', type: 'incisor' },
            { number: 22, name: 'الثنية الجانبية اليسرى العلوية', type: 'incisor' },
            { number: 23, name: 'الناب الأيسر العلوي', type: 'canine' },
            { number: 24, name: 'الضاحك الأول الأيسر العلوي', type: 'premolar' },
            { number: 25, name: 'الضاحك الثاني الأيسر العلوي', type: 'premolar' },
            { number: 26, name: 'الضرس الأول الأيسر العلوي', type: 'molar' },
            { number: 27, name: 'الضرس الثاني الأيسر العلوي', type: 'molar' },
            { number: 28, name: 'ضرس العقل الأيسر العلوي', type: 'molar' }
        ];

        const lowerTeeth = [
            { number: 48, name: 'ضرس العقل الأيمن السفلي', type: 'molar' },
            { number: 47, name: 'الضرس الثاني الأيمن السفلي', type: 'molar' },
            { number: 46, name: 'الضرس الأول الأيمن السفلي', type: 'molar' },
            { number: 45, name: 'الضاحك الثاني الأيمن السفلي', type: 'premolar' },
            { number: 44, name: 'الضاحك الأول الأيمن السفلي', type: 'premolar' },
            { number: 43, name: 'الناب الأيمن السفلي', type: 'canine' },
            { number: 42, name: 'الثنية الجانبية اليمنى السفلية', type: 'incisor' },
            { number: 41, name: 'الثنية الوسطى اليمنى السفلية', type: 'incisor' },
            { number: 31, name: 'الثنية الوسطى اليسرى السفلية', type: 'incisor' },
            { number: 32, name: 'الثنية الجانبية اليسرى السفلية', type: 'incisor' },
            { number: 33, name: 'الناب الأيسر السفلي', type: 'canine' },
            { number: 34, name: 'الضاحك الأول الأيسر السفلي', type: 'premolar' },
            { number: 35, name: 'الضاحك الثاني الأيسر السفلي', type: 'premolar' },
            { number: 36, name: 'الضرس الأول الأيسر السفلي', type: 'molar' },
            { number: 37, name: 'الضرس الثاني الأيسر السفلي', type: 'molar' },
            { number: 38, name: 'ضرس العقل الأيسر السفلي', type: 'molar' }
        ];

        return `
            <div class="teeth-chart-container">
                <div class="teeth-chart-title">اختر الأسنان المطلوبة للتركيبة</div>

                <!-- الأسنان العلوية -->
                <div class="teeth-row upper-teeth">
                    <div class="teeth-label">الفك العلوي</div>
                    <div class="teeth-container">
                        ${upperTeeth.map(tooth => `
                            <div class="tooth tooth-${tooth.type}"
                                 data-tooth-number="${tooth.number}"
                                 data-tooth-name="${tooth.name}"
                                 data-tooth-type="${tooth.type}"
                                 title="${tooth.name} (${tooth.number})">
                                <span class="tooth-number">${tooth.number}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- الأسنان السفلية -->
                <div class="teeth-row lower-teeth">
                    <div class="teeth-label">الفك السفلي</div>
                    <div class="teeth-container">
                        ${lowerTeeth.map(tooth => `
                            <div class="tooth tooth-${tooth.type}"
                                 data-tooth-number="${tooth.number}"
                                 data-tooth-name="${tooth.name}"
                                 data-tooth-type="${tooth.type}"
                                 title="${tooth.name} (${tooth.number})">
                                <span class="tooth-number">${tooth.number}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="teeth-controls">
                    <button type="button" class="btn btn-sm btn-outline" onclick="prostheticsManager.selectAllTeeth()">
                        اختيار الكل
                    </button>
                    <button type="button" class="btn btn-sm btn-outline" onclick="prostheticsManager.clearSelectedTeeth()">
                        مسح الاختيار
                    </button>
                    <button type="button" class="btn btn-sm btn-outline" onclick="prostheticsManager.selectUpperTeeth()">
                        الفك العلوي
                    </button>
                    <button type="button" class="btn btn-sm btn-outline" onclick="prostheticsManager.selectLowerTeeth()">
                        الفك السفلي
                    </button>
                </div>
            </div>
        `;
    }

    setupTeethSelection() {
        const teeth = document.querySelectorAll('.tooth');
        teeth.forEach(tooth => {
            tooth.addEventListener('click', () => {
                this.toggleToothSelection(tooth);
            });
        });
    }

    toggleToothSelection(toothElement) {
        const toothNumber = parseInt(toothElement.dataset.toothNumber);
        const toothName = toothElement.dataset.toothName;
        const toothType = toothElement.dataset.toothType;

        if (toothElement.classList.contains('selected')) {
            // إلغاء الاختيار
            toothElement.classList.remove('selected');
            this.selectedTeeth = this.selectedTeeth.filter(t => t.number !== toothNumber);
        } else {
            // إضافة للاختيار
            toothElement.classList.add('selected');
            this.selectedTeeth.push({
                number: toothNumber,
                name: toothName,
                type: toothType
            });
        }

        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    updateSelectedTeethDisplay() {
        const selectedTeethList = document.getElementById('selected-teeth-list');
        if (!selectedTeethList) return;

        if (this.selectedTeeth.length === 0) {
            selectedTeethList.innerHTML = '<p class="no-teeth-selected">لم يتم اختيار أسنان بعد</p>';
            return;
        }

        // ترتيب الأسنان حسب الرقم
        const sortedTeeth = [...this.selectedTeeth].sort((a, b) => a.number - b.number);

        selectedTeethList.innerHTML = `
            <div class="selected-teeth-grid">
                ${sortedTeeth.map(tooth => `
                    <div class="selected-tooth-item">
                        <span class="tooth-number">${tooth.number}</span>
                        <span class="tooth-name">${tooth.name}</span>
                        <button class="remove-tooth" onclick="prostheticsManager.removeSelectedTooth(${tooth.number})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('')}
            </div>
            <div class="selected-teeth-summary">
                <strong>إجمالي الأسنان المختارة: ${this.selectedTeeth.length}</strong>
            </div>
        `;
    }

    removeSelectedTooth(toothNumber) {
        // إزالة من القائمة
        this.selectedTeeth = this.selectedTeeth.filter(t => t.number !== toothNumber);

        // إزالة التحديد من الرسم
        const toothElement = document.querySelector(`[data-tooth-number="${toothNumber}"]`);
        if (toothElement) {
            toothElement.classList.remove('selected');
        }

        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    selectAllTeeth() {
        const teeth = document.querySelectorAll('.tooth');
        this.selectedTeeth = [];

        teeth.forEach(tooth => {
            tooth.classList.add('selected');
            this.selectedTeeth.push({
                number: parseInt(tooth.dataset.toothNumber),
                name: tooth.dataset.toothName,
                type: tooth.dataset.toothType
            });
        });

        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    clearSelectedTeeth() {
        const teeth = document.querySelectorAll('.tooth');
        teeth.forEach(tooth => {
            tooth.classList.remove('selected');
        });

        this.selectedTeeth = [];
        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    selectUpperTeeth() {
        const upperTeeth = document.querySelectorAll('.upper-teeth .tooth');

        upperTeeth.forEach(tooth => {
            const toothNumber = parseInt(tooth.dataset.toothNumber);
            if (!this.selectedTeeth.find(t => t.number === toothNumber)) {
                tooth.classList.add('selected');
                this.selectedTeeth.push({
                    number: toothNumber,
                    name: tooth.dataset.toothName,
                    type: tooth.dataset.toothType
                });
            }
        });

        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    selectLowerTeeth() {
        const lowerTeeth = document.querySelectorAll('.lower-teeth .tooth');

        lowerTeeth.forEach(tooth => {
            const toothNumber = parseInt(tooth.dataset.toothNumber);
            if (!this.selectedTeeth.find(t => t.number === toothNumber)) {
                tooth.classList.add('selected');
                this.selectedTeeth.push({
                    number: toothNumber,
                    name: tooth.dataset.toothName,
                    type: tooth.dataset.toothType
                });
            }
        });

        this.updateSelectedTeethDisplay();
        this.calculatePrice();
    }

    // ========================================
    // نظام حساب الأسعار التلقائي - Automatic Price Calculation System
    // ========================================

    calculatePrice() {
        const typeSelect = document.getElementById('prosthetic-type');
        const doctorSelect = document.getElementById('doctor-select');

        if (!typeSelect || !typeSelect.value) {
            this.clearPriceDisplay();
            return;
        }

        const selectedOption = typeSelect.selectedOptions[0];
        const unitPrice = parseFloat(selectedOption.dataset.price) || 0;
        const isPerTooth = selectedOption.dataset.isPerTooth === 'true';
        const specialCalculation = selectedOption.dataset.specialCalculation === 'true';

        let quantity = 1;
        let totalPrice = 0;

        if (isPerTooth) {
            quantity = this.selectedTeeth.length;
            if (specialCalculation) {
                // حساب خاص للبرشل الجزئي
                totalPrice = this.calculatePartialBridgePrice(unitPrice, quantity);
            } else {
                // حساب عادي للتركيبات الفردية
                totalPrice = unitPrice * quantity;
            }
        } else {
            // تركيبات لا تحسب بالسن (مثل الأطقم الكاملة)
            totalPrice = unitPrice;
        }

        // حساب خصم الطبيب
        let discountPercentage = 0;
        let discountAmount = 0;

        if (doctorSelect && doctorSelect.value) {
            const doctor = this.doctors.find(d => d.id == doctorSelect.value);
            if (doctor && doctor.discount_percentage) {
                discountPercentage = doctor.discount_percentage;
                discountAmount = (totalPrice * discountPercentage) / 100;
            }
        }

        const finalPrice = totalPrice - discountAmount;

        // تحديث العرض
        this.updatePriceDisplay({
            quantity,
            unitPrice,
            totalPrice,
            discountPercentage,
            discountAmount,
            finalPrice
        });
    }

    calculatePartialBridgePrice(unitPrice, teethCount, material = 'metal', complexity = 'simple') {
        // استخدام حاسبة البرشل الجزئي المتقدمة
        if (teethCount === 0) return 0;

        try {
            // تحديد موقع الأسنان بناءً على الأسنان المختارة
            const location = this.determineTeethLocation();

            // خيارات الحساب
            const options = {
                teethCount: teethCount,
                basePrice: unitPrice,
                material: material,
                complexity: complexity,
                location: location,
                pricingType: 'standard',
                additionalWork: [],
                doctorDiscount: 0
            };

            // حساب السعر باستخدام الحاسبة المتقدمة
            const result = partialBridgeCalculator.calculatePartialBridgePrice(options);

            // حفظ تفاصيل الحساب للعرض لاحقاً
            this.lastCalculationDetails = result;

            return result.breakdown.totalBeforeDiscount;

        } catch (error) {
            console.warn('خطأ في حاسبة البرشل المتقدمة، استخدام الحساب البسيط:', error);

            // العودة للحساب البسيط في حالة الخطأ
            return this.calculateSimplePartialBridgePrice(unitPrice, teethCount);
        }
    }

    calculateSimplePartialBridgePrice(unitPrice, teethCount) {
        // نظام حساب بسيط للبرشل الجزئي (احتياطي)
        let totalPrice = 0;

        // أول 3 أسنان بالسعر الكامل
        const firstTier = Math.min(teethCount, 3);
        totalPrice += firstTier * unitPrice;

        // الأسنان من 4-6 بخصم 10%
        if (teethCount > 3) {
            const secondTier = Math.min(teethCount - 3, 3);
            totalPrice += secondTier * unitPrice * 0.9;
        }

        // الأسنان من 7-10 بخصم 20%
        if (teethCount > 6) {
            const thirdTier = Math.min(teethCount - 6, 4);
            totalPrice += thirdTier * unitPrice * 0.8;
        }

        // أي أسنان إضافية بخصم 30%
        if (teethCount > 10) {
            const remainingTeeth = teethCount - 10;
            totalPrice += remainingTeeth * unitPrice * 0.7;
        }

        return totalPrice;
    }

    determineTeethLocation() {
        // تحديد موقع الأسنان بناءً على الأسنان المختارة
        if (this.selectedTeeth.length === 0) return 'posterior';

        const anteriorTeeth = this.selectedTeeth.filter(tooth =>
            (tooth.number >= 11 && tooth.number <= 13) ||
            (tooth.number >= 21 && tooth.number <= 23) ||
            (tooth.number >= 31 && tooth.number <= 33) ||
            (tooth.number >= 41 && tooth.number <= 43)
        );

        const posteriorTeeth = this.selectedTeeth.filter(tooth =>
            (tooth.number >= 14 && tooth.number <= 18) ||
            (tooth.number >= 24 && tooth.number <= 28) ||
            (tooth.number >= 34 && tooth.number <= 38) ||
            (tooth.number >= 44 && tooth.number <= 48)
        );

        if (anteriorTeeth.length > 0 && posteriorTeeth.length > 0) {
            return 'mixed';
        } else if (anteriorTeeth.length > 0) {
            return 'anterior';
        } else {
            return 'posterior';
        }
    }

    updatePriceDisplay(priceData) {
        const elements = {
            teethCount: document.getElementById('teeth-count'),
            unitPrice: document.getElementById('unit-price'),
            totalPrice: document.getElementById('total-price'),
            doctorDiscount: document.getElementById('doctor-discount'),
            discountAmount: document.getElementById('discount-amount'),
            finalPrice: document.getElementById('final-price')
        };

        if (elements.teethCount) elements.teethCount.textContent = priceData.quantity;
        if (elements.unitPrice) elements.unitPrice.textContent = formatCurrency(priceData.unitPrice);
        if (elements.totalPrice) elements.totalPrice.textContent = formatCurrency(priceData.totalPrice);
        if (elements.doctorDiscount) elements.doctorDiscount.textContent = `${priceData.discountPercentage}%`;
        if (elements.discountAmount) elements.discountAmount.textContent = formatCurrency(priceData.discountAmount);
        if (elements.finalPrice) elements.finalPrice.textContent = formatCurrency(priceData.finalPrice);
    }

    clearPriceDisplay() {
        const elements = {
            teethCount: document.getElementById('teeth-count'),
            unitPrice: document.getElementById('unit-price'),
            totalPrice: document.getElementById('total-price'),
            doctorDiscount: document.getElementById('doctor-discount'),
            discountAmount: document.getElementById('discount-amount'),
            finalPrice: document.getElementById('final-price')
        };

        Object.values(elements).forEach(element => {
            if (element) {
                if (element.id === 'teeth-count') {
                    element.textContent = '0';
                } else if (element.id === 'doctor-discount') {
                    element.textContent = '0%';
                } else {
                    element.textContent = '0 ريال';
                }
            }
        });
    }

    // ========================================
    // حفظ التركيبة - Save Prosthetic
    // ========================================

    async saveProsthetic() {
        try {
            // التحقق من صحة البيانات
            const validationResult = this.validateProstheticData();
            if (!validationResult.isValid) {
                showError(validationResult.message);
                return;
            }

            // جمع البيانات
            const prostheticData = this.collectProstheticData();

            // حفظ في قاعدة البيانات
            const result = await db.insert('prosthetics', prostheticData);

            if (result) {
                // حفظ تفاصيل الأسنان
                await this.saveProstheticTeeth(result.id);

                showSuccess('تم حفظ التركيبة بنجاح');
                this.closeNewProstheticModal();
                await this.refreshData();
            } else {
                showError('فشل في حفظ التركيبة');
            }

        } catch (error) {
            console.error('خطأ في حفظ التركيبة:', error);
            showError('حدث خطأ أثناء حفظ التركيبة');
        }
    }

    validateProstheticData() {
        const patientName = document.getElementById('patient-name').value.trim();
        const doctorId = document.getElementById('doctor-select').value;
        const prostheticType = document.getElementById('prosthetic-type').value;

        if (!patientName) {
            return { isValid: false, message: 'يرجى إدخال اسم المريض' };
        }

        if (!doctorId) {
            return { isValid: false, message: 'يرجى اختيار الطبيب' };
        }

        if (!prostheticType) {
            return { isValid: false, message: 'يرجى اختيار نوع التركيبة' };
        }

        const typeSelect = document.getElementById('prosthetic-type');
        const selectedOption = typeSelect.selectedOptions[0];
        const isPerTooth = selectedOption.dataset.isPerTooth === 'true';

        if (isPerTooth && this.selectedTeeth.length === 0) {
            return { isValid: false, message: 'يرجى اختيار الأسنان المطلوبة' };
        }

        return { isValid: true };
    }

    collectProstheticData() {
        const typeSelect = document.getElementById('prosthetic-type');
        const selectedOption = typeSelect.selectedOptions[0];
        const prostheticTypeData = this.prostheticTypes.find(t => t.id == typeSelect.value);

        // حساب الأسعار
        const unitPrice = parseFloat(selectedOption.dataset.price) || 0;
        const isPerTooth = selectedOption.dataset.isPerTooth === 'true';
        const specialCalculation = selectedOption.dataset.specialCalculation === 'true';

        let quantity = isPerTooth ? this.selectedTeeth.length : 1;
        let totalPrice = 0;

        if (isPerTooth) {
            if (specialCalculation) {
                totalPrice = this.calculatePartialBridgePrice(unitPrice, quantity);
            } else {
                totalPrice = unitPrice * quantity;
            }
        } else {
            totalPrice = unitPrice;
        }

        // حساب الخصم
        const doctorId = document.getElementById('doctor-select').value;
        const doctor = this.doctors.find(d => d.id == doctorId);
        const discountPercentage = doctor ? (doctor.discount_percentage || 0) : 0;
        const discountAmount = (totalPrice * discountPercentage) / 100;
        const finalPrice = totalPrice - discountAmount;

        return {
            patient_name: document.getElementById('patient-name').value.trim(),
            patient_phone: document.getElementById('patient-phone').value.trim(),
            doctor_id: parseInt(doctorId),
            prosthetic_category: document.getElementById('prosthetic-category').value,
            prosthetic_type: prostheticTypeData.type_name,
            prosthetic_subtype: prostheticTypeData.subtype,
            material: document.getElementById('prosthetic-material').value,
            color_shade: document.getElementById('color-shade').value,
            selected_teeth: JSON.stringify(this.selectedTeeth.map(t => t.number)),
            teeth_positions: JSON.stringify(this.selectedTeeth),
            quantity: quantity,
            unit_price: unitPrice,
            total_price: totalPrice,
            discount_percentage: discountPercentage,
            discount_amount: discountAmount,
            final_price: finalPrice,
            priority: document.getElementById('priority').value,
            delivery_date: document.getElementById('delivery-date').value,
            notes: document.getElementById('notes').value.trim(),
            special_instructions: document.getElementById('special-instructions').value.trim(),
            created_by: getCurrentUser()?.name || 'النظام',
            status: 'pending'
        };
    }

    async saveProstheticTeeth(prostheticId) {
        for (const tooth of this.selectedTeeth) {
            const toothData = {
                prosthetic_id: prostheticId,
                tooth_number: tooth.number,
                tooth_position: this.getToothPosition(tooth.number),
                tooth_type: tooth.type,
                tooth_name: tooth.name
            };

            await db.insert('prosthetic_teeth', toothData);
        }
    }

    getToothPosition(toothNumber) {
        if (toothNumber >= 11 && toothNumber <= 18) return 'upper_right';
        if (toothNumber >= 21 && toothNumber <= 28) return 'upper_left';
        if (toothNumber >= 31 && toothNumber <= 38) return 'lower_left';
        if (toothNumber >= 41 && toothNumber <= 48) return 'lower_right';
        return 'unknown';
    }

    // ========================================
    // إدارة النوافذ المنبثقة - Modal Management
    // ========================================

    showNewProstheticModal() {
        const modal = document.getElementById('new-prosthetic-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.resetNewProstheticForm();
        }
    }

    closeNewProstheticModal() {
        const modal = document.getElementById('new-prosthetic-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetNewProstheticForm();
        }
    }

    resetNewProstheticForm() {
        const form = document.getElementById('new-prosthetic-form');
        if (form) {
            form.reset();
        }

        this.selectedTeeth = [];
        this.clearSelectedTeeth();
        this.clearPriceDisplay();
        this.clearProstheticDetails();
    }

    // ========================================
    // وظائف مساعدة - Helper Functions
    // ========================================

    getStatusClass(status) {
        const statusClasses = {
            'pending': 'warning',
            'in_progress': 'info',
            'completed': 'success',
            'delivered': 'success',
            'cancelled': 'error'
        };
        return statusClasses[status] || 'secondary';
    }

    getStatusText(status) {
        const statusTexts = {
            'pending': 'قيد التنفيذ',
            'in_progress': 'جاري العمل',
            'completed': 'مكتمل',
            'delivered': 'تم التسليم',
            'cancelled': 'ملغي'
        };
        return statusTexts[status] || status;
    }

    getTeethDisplay(selectedTeethJson) {
        try {
            const teeth = JSON.parse(selectedTeethJson || '[]');
            if (teeth.length === 0) return '-';
            if (teeth.length <= 3) return teeth.join(', ');
            return `${teeth.slice(0, 3).join(', ')} +${teeth.length - 3}`;
        } catch {
            return '-';
        }
    }

    // ========================================
    // تصفية وبحث التركيبات - Filter and Search Prosthetics
    // ========================================

    filterProsthetics() {
        const searchTerm = document.getElementById('prosthetics-search')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('status-filter')?.value || '';
        const categoryFilter = document.getElementById('category-filter')?.value || '';
        const doctorFilter = document.getElementById('doctor-filter')?.value || '';

        let filteredProsthetics = [...this.prosthetics];

        // تطبيق البحث النصي
        if (searchTerm) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic =>
                prosthetic.patient_name.toLowerCase().includes(searchTerm) ||
                prosthetic.prosthetic_type.toLowerCase().includes(searchTerm) ||
                prosthetic.id.toString().includes(searchTerm)
            );
        }

        // تطبيق تصفية الحالة
        if (statusFilter) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic =>
                prosthetic.status === statusFilter
            );
        }

        // تطبيق تصفية الفئة
        if (categoryFilter) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic =>
                prosthetic.prosthetic_category === categoryFilter
            );
        }

        // تطبيق تصفية الطبيب
        if (doctorFilter) {
            filteredProsthetics = filteredProsthetics.filter(prosthetic =>
                prosthetic.doctor_id == doctorFilter
            );
        }

        // تحديث العرض
        this.prosthetics = filteredProsthetics;
        this.renderProstheticsList();
    }

    // ========================================
    // إجراءات التركيبات - Prosthetic Actions
    // ========================================

    async refreshData() {
        await this.loadInitialData();
        this.renderProstheticsList();
        this.renderProstheticTypes();
    }

    viewProsthetic(id) {
        // TODO: تنفيذ عرض تفاصيل التركيبة
        console.log('عرض التركيبة:', id);
    }

    editProsthetic(id) {
        // TODO: تنفيذ تعديل التركيبة
        console.log('تعديل التركيبة:', id);
    }

    printProsthetic(id) {
        // TODO: تنفيذ طباعة التركيبة
        console.log('طباعة التركيبة:', id);
    }

    async deleteProsthetic(id) {
        if (confirm('هل أنت متأكد من حذف هذه التركيبة؟')) {
            try {
                await db.delete('prosthetics', id);
                await db.delete('prosthetic_teeth', { prosthetic_id: id });
                showSuccess('تم حذف التركيبة بنجاح');
                await this.refreshData();
            } catch (error) {
                console.error('خطأ في حذف التركيبة:', error);
                showError('فشل في حذف التركيبة');
            }
        }
    }

    exportProsthetics() {
        // TODO: تنفيذ تصدير التركيبات
        console.log('تصدير التركيبات');
    }
}

// ========================================
// فئة حساب أسعار التركيبات - Prosthetic Price Calculator
// ========================================

class ProstheticPriceCalculator {
    constructor() {
        this.discountTiers = {
            partial_bridge: [
                { min: 1, max: 3, discount: 0 },
                { min: 4, max: 6, discount: 0.1 },
                { min: 7, max: 10, discount: 0.2 },
                { min: 11, max: Infinity, discount: 0.3 }
            ]
        };
    }

    calculatePrice(prostheticType, teethCount, unitPrice, doctorDiscount = 0) {
        let totalPrice = 0;

        if (prostheticType.is_per_tooth) {
            if (prostheticType.special_calculation) {
                totalPrice = this.calculateSpecialPrice(prostheticType, teethCount, unitPrice);
            } else {
                totalPrice = unitPrice * teethCount;
            }
        } else {
            totalPrice = unitPrice;
        }

        // تطبيق خصم الطبيب
        const discountAmount = (totalPrice * doctorDiscount) / 100;
        const finalPrice = totalPrice - discountAmount;

        return {
            unitPrice,
            quantity: prostheticType.is_per_tooth ? teethCount : 1,
            totalPrice,
            discountPercentage: doctorDiscount,
            discountAmount,
            finalPrice
        };
    }

    calculateSpecialPrice(prostheticType, teethCount, unitPrice) {
        if (prostheticType.category === 'dentures' && prostheticType.type_name.includes('برشل')) {
            return this.calculatePartialBridgePrice(teethCount, unitPrice);
        }

        // حسابات خاصة أخرى يمكن إضافتها هنا
        return unitPrice * teethCount;
    }

    calculatePartialBridgePrice(teethCount, unitPrice) {
        let totalPrice = 0;
        const tiers = this.discountTiers.partial_bridge;

        for (const tier of tiers) {
            const teethInTier = Math.min(Math.max(teethCount - tier.min + 1, 0), tier.max - tier.min + 1);
            if (teethInTier > 0) {
                const tierPrice = teethInTier * unitPrice * (1 - tier.discount);
                totalPrice += tierPrice;
                teethCount -= teethInTier;
            }
            if (teethCount <= 0) break;
        }

        return totalPrice;
    }
}

// ========================================
// تهيئة وحدة التركيبات - Initialize Prosthetics Module
// ========================================

// إنشاء مثيل من مدير التركيبات
const prostheticsManager = new ProstheticsManager();

// دالة تهيئة الوحدة
async function initializeProstheticsModule() {
    try {
        const success = await prostheticsManager.init();
        if (success) {
            console.log('✅ تم تهيئة وحدة التركيبات بنجاح');
        } else {
            console.error('❌ فشل في تهيئة وحدة التركيبات');
        }
        return success;
    } catch (error) {
        console.error('❌ خطأ في تهيئة وحدة التركيبات:', error);
        return false;
    }
}

// تصدير الوحدة للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { prostheticsManager, ProstheticsManager, ProstheticPriceCalculator };
}

console.log('✅ تم تحميل وحدة إدارة التركيبات بنجاح');
