// نظام إدارة معمل الأسنان المتقدم v2.0
// Main Process - Electron Application Entry Point
// تطبيق سطح المكتب احترافي لإدارة معامل الأسنان

const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');

// تكوين التطبيق
const isDev = process.env.NODE_ENV === 'development';
const isWindows = process.platform === 'win32';
const isMac = process.platform === 'darwin';

// متغيرات عامة
let mainWindow;
let splashWindow;

// إعدادات النافذة الرئيسية
const WINDOW_CONFIG = {
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    show: false,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true,
        webSecurity: false
    }
};

// إنشاء نافذة البداية
function createSplashWindow() {
    splashWindow = new BrowserWindow({
        width: 400,
        height: 300,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });

    splashWindow.loadFile('splash.html');
    
    splashWindow.on('closed', () => {
        splashWindow = null;
    });

    // إخفاء نافذة البداية بعد 3 ثواني
    setTimeout(() => {
        if (splashWindow) {
            splashWindow.close();
        }
        if (mainWindow) {
            mainWindow.show();
            mainWindow.focus();
        }
    }, 3000);
}

// إنشاء النافذة الرئيسية
function createMainWindow() {
    // إنشاء النافذة الرئيسية
    mainWindow = new BrowserWindow(WINDOW_CONFIG);

    // تحميل الملف الرئيسي
    mainWindow.loadFile('index.html');

    // إعداد القائمة
    createApplicationMenu();

    // معالجة الأحداث
    mainWindow.webContents.on('did-finish-load', () => {
        console.log('✅ تم تحميل التطبيق بنجاح');
        
        // إرسال معلومات النظام
        mainWindow.webContents.send('system-info', {
            platform: process.platform,
            version: app.getVersion(),
            electronVersion: process.versions.electron,
            nodeVersion: process.versions.node
        });
    });

    // معالجة الأخطاء
    mainWindow.webContents.on('crashed', () => {
        console.error('❌ تعطل التطبيق');
        dialog.showErrorBox('خطأ في التطبيق', 'حدث خطأ غير متوقع في التطبيق');
    });

    // معالجة الروابط الخارجية
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // إعداد أدوات التطوير
    if (isDev) {
        mainWindow.webContents.openDevTools();
    }

    // معالجة إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // معالجة تصغير النافذة
    mainWindow.on('minimize', (event) => {
        if (isWindows) {
            event.preventDefault();
            mainWindow.hide();
        }
    });

    return mainWindow;
}

// إنشاء قائمة التطبيق
function createApplicationMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'جديد',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'new');
                    }
                },
                {
                    label: 'فتح',
                    accelerator: 'CmdOrCtrl+O',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'open');
                    }
                },
                {
                    label: 'حفظ',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.send('menu-action', 'save');
                    }
                },
                { type: 'separator' },
                {
                    label: 'تصدير',
                    submenu: [
                        {
                            label: 'تصدير إلى Excel',
                            click: () => {
                                mainWindow.webContents.send('menu-action', 'export-excel');
                            }
                        },
                        {
                            label: 'تصدير إلى PDF',
                            click: () => {
                                mainWindow.webContents.send('menu-action', 'export-pdf');
                            }
                        }
                    ]
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: isMac ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                {
                    label: 'تراجع',
                    accelerator: 'CmdOrCtrl+Z',
                    role: 'undo'
                },
                {
                    label: 'إعادة',
                    accelerator: 'Shift+CmdOrCtrl+Z',
                    role: 'redo'
                },
                { type: 'separator' },
                {
                    label: 'قص',
                    accelerator: 'CmdOrCtrl+X',
                    role: 'cut'
                },
                {
                    label: 'نسخ',
                    accelerator: 'CmdOrCtrl+C',
                    role: 'copy'
                },
                {
                    label: 'لصق',
                    accelerator: 'CmdOrCtrl+V',
                    role: 'paste'
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'تبديل أدوات التطوير',
                    accelerator: isMac ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
                    click: () => {
                        mainWindow.webContents.toggleDevTools();
                    }
                },
                { type: 'separator' },
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom + 1);
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom - 1);
                    }
                },
                {
                    label: 'الحجم الطبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => {
                        mainWindow.webContents.setZoomLevel(0);
                    }
                },
                { type: 'separator' },
                {
                    label: 'ملء الشاشة',
                    accelerator: isMac ? 'Ctrl+Cmd+F' : 'F11',
                    click: () => {
                        const isFullScreen = mainWindow.isFullScreen();
                        mainWindow.setFullScreen(!isFullScreen);
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول التطبيق',
                            message: 'نظام إدارة معمل الأسنان المتقدم v2.0',
                            detail: 'تطبيق احترافي لإدارة معامل الأسنان\nالإصدار: 2.0.0\nتاريخ التطوير: 2025',
                            buttons: ['موافق']
                        });
                    }
                },
                {
                    label: 'دليل المستخدم',
                    click: () => {
                        shell.openExternal('https://dentallab.com/help');
                    }
                },
                {
                    label: 'الدعم الفني',
                    click: () => {
                        shell.openExternal('https://dentallab.com/support');
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// معالجة أحداث التطبيق
app.whenReady().then(() => {
    console.log('🚀 بدء تشغيل نظام إدارة معمل الأسنان المتقدم v2.0');
    
    // إنشاء النوافذ
    createSplashWindow();
    createMainWindow();

    // معالجة تفعيل التطبيق على macOS
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

// معالجة إغلاق جميع النوافذ
app.on('window-all-closed', () => {
    if (!isMac) {
        app.quit();
    }
});

// معالجة إغلاق التطبيق
app.on('before-quit', (event) => {
    console.log('🔄 جاري إغلاق التطبيق...');
    
    // حفظ البيانات قبل الإغلاق
    if (mainWindow) {
        mainWindow.webContents.send('app-closing');
    }
});

// معالجة IPC للتواصل مع العملية الرئيسية
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('show-save-dialog', async () => {
    const result = await dialog.showSaveDialog(mainWindow, {
        filters: [
            { name: 'Excel Files', extensions: ['xlsx'] },
            { name: 'PDF Files', extensions: ['pdf'] },
            { name: 'All Files', extensions: ['*'] }
        ]
    });
    return result;
});

ipcMain.handle('show-open-dialog', async () => {
    const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile'],
        filters: [
            { name: 'Database Files', extensions: ['db', 'sqlite'] },
            { name: 'All Files', extensions: ['*'] }
        ]
    });
    return result;
});

// معالجة الأخطاء العامة
process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error);
    dialog.showErrorBox('خطأ في التطبيق', `حدث خطأ غير متوقع:\n${error.message}`);
});

console.log('✅ تم تحميل الملف الرئيسي بنجاح');
