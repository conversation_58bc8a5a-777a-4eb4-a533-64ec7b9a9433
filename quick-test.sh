#!/bin/bash

# اختبار سريع - نظام إدارة معمل الأسنان المتقدم v2.0
# Quick System Test - Advanced Dental Lab Management System v2.0

# تعيين الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل الملونة
print_message() {
    echo -e "${2}${1}${NC}"
}

clear

print_message "========================================" $BLUE
print_message "   اختبار سريع للنظام" $BLUE
print_message "   Quick System Test" $BLUE
print_message "========================================" $BLUE
echo

print_message "🧪 تشغيل اختبار النظام..." $YELLOW
echo

node test-system.js

echo
print_message "========================================" $BLUE
echo

if [ $? -eq 0 ]; then
    print_message "✅ جميع الاختبارات نجحت!" $GREEN
    print_message "💡 يمكنك الآن تشغيل التطبيق باستخدام ./start.sh" $GREEN
else
    print_message "❌ بعض الاختبارات فشلت" $RED
    print_message "📖 راجع ملف INSTALLATION.md للحصول على المساعدة" $YELLOW
fi

echo
read -p "اضغط Enter للمتابعة..."
