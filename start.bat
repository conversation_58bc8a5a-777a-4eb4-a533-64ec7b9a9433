@echo off
chcp 65001 >nul
title نظام إدارة معمل الأسنان المتقدم v2.0

echo.
echo ========================================
echo    نظام إدارة معمل الأسنان المتقدم v2.0
echo    Advanced Dental Lab Management System
echo ========================================
echo.

echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo    يمكنك تحميله من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
echo.

echo 🔍 التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)

echo ✅ npm متوفر
echo.

echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo 📥 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ التبعيات متوفرة
)

echo.
echo 🚀 بدء تشغيل التطبيق...
echo.

npm start

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح
pause
