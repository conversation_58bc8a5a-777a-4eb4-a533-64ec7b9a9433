#!/bin/bash

# نظام إدارة معمل الأسنان المتقدم v2.0
# Advanced Dental Lab Management System v2.0

# تعيين الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل الملونة
print_message() {
    echo -e "${2}${1}${NC}"
}

# دالة التحقق من الأوامر
check_command() {
    if command -v $1 &> /dev/null; then
        print_message "✅ $1 متوفر" $GREEN
        return 0
    else
        print_message "❌ $1 غير مثبت" $RED
        return 1
    fi
}

clear

print_message "========================================" $BLUE
print_message "   نظام إدارة معمل الأسنان المتقدم v2.0" $BLUE
print_message "   Advanced Dental Lab Management System" $BLUE
print_message "========================================" $BLUE
echo

print_message "🔍 التحقق من Node.js..." $YELLOW
if check_command "node"; then
    NODE_VERSION=$(node --version)
    print_message "   الإصدار: $NODE_VERSION" $GREEN
else
    print_message "يرجى تثبيت Node.js من: https://nodejs.org" $RED
    exit 1
fi

echo

print_message "🔍 التحقق من npm..." $YELLOW
if check_command "npm"; then
    NPM_VERSION=$(npm --version)
    print_message "   الإصدار: $NPM_VERSION" $GREEN
else
    print_message "npm غير متوفر" $RED
    exit 1
fi

echo

print_message "📦 التحقق من التبعيات..." $YELLOW
if [ ! -d "node_modules" ]; then
    print_message "📥 تثبيت التبعيات..." $YELLOW
    npm install
    if [ $? -eq 0 ]; then
        print_message "✅ تم تثبيت التبعيات بنجاح" $GREEN
    else
        print_message "❌ فشل في تثبيت التبعيات" $RED
        exit 1
    fi
else
    print_message "✅ التبعيات متوفرة" $GREEN
fi

echo

print_message "🚀 بدء تشغيل التطبيق..." $YELLOW
echo

npm start

if [ $? -eq 0 ]; then
    echo
    print_message "✅ تم إغلاق التطبيق بنجاح" $GREEN
else
    echo
    print_message "❌ فشل في تشغيل التطبيق" $RED
    exit 1
fi
