/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   الرسوم المتحركة - Animations
   ======================================== */

/* ========================================
   الرسوم المتحركة الأساسية - Base Animations
   ======================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

/* ========================================
   فئات الرسوم المتحركة - Animation Classes
   ======================================== */

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-fade-out {
  animation: fadeOut 0.5s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.4s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-scale-out {
  animation: scaleOut 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 1s;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shake {
  animation: shake 0.5s;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-wiggle {
  animation: wiggle 1s;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* ========================================
   رسوم متحركة مخصصة للتطبيق
   ======================================== */

@keyframes loadingProgress {
  0% {
    width: 0%;
  }
  25% {
    width: 25%;
  }
  50% {
    width: 50%;
  }
  75% {
    width: 75%;
  }
  100% {
    width: 100%;
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 5px var(--primary-color);
  }
  50% {
    box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes morphing {
  0% {
    border-radius: var(--radius-lg);
  }
  25% {
    border-radius: var(--radius-xl);
  }
  50% {
    border-radius: var(--radius-2xl);
  }
  75% {
    border-radius: var(--radius-xl);
  }
  100% {
    border-radius: var(--radius-lg);
  }
}

/* ========================================
   فئات الرسوم المتحركة المخصصة
   ======================================== */

.animate-loading-progress {
  animation: loadingProgress 3s ease-in-out;
}

.animate-glow-pulse {
  animation: glowPulse 2s ease-in-out infinite;
}

.animate-slide-in-from-right {
  animation: slideInFromRight 0.5s ease-out;
}

.animate-slide-in-from-left {
  animation: slideInFromLeft 0.5s ease-out;
}

.animate-typewriter {
  animation: typewriter 2s steps(40, end);
}

.animate-blink {
  animation: blink 1s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-morphing {
  animation: morphing 4s ease-in-out infinite;
}

/* ========================================
   رسوم متحركة للحالات - State Animations
   ======================================== */

.animate-success {
  animation: scaleIn 0.3s ease-out, pulse 0.6s ease-out 0.3s;
}

.animate-error {
  animation: shake 0.5s ease-out;
}

.animate-warning {
  animation: wiggle 0.8s ease-out;
}

.animate-info {
  animation: fadeInUp 0.5s ease-out;
}

/* ========================================
   رسوم متحركة للتفاعل - Interaction Animations
   ======================================== */

.hover-lift {
  transition: transform var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform var(--transition-fast);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-glow {
  transition: box-shadow var(--transition-fast);
}

.hover-glow:hover {
  box-shadow: 0 0 20px var(--primary-color);
}

.click-ripple {
  position: relative;
  overflow: hidden;
}

.click-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.click-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* ========================================
   رسوم متحركة للتحميل - Loading Animations
   ======================================== */

.loading-dots {
  display: inline-flex;
  gap: var(--spacing-1);
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-wave {
  display: inline-flex;
  gap: 2px;
}

.loading-wave span {
  width: 4px;
  height: 20px;
  background: var(--primary-color);
  animation: loadingWave 1.2s ease-in-out infinite;
}

.loading-wave span:nth-child(1) { animation-delay: 0s; }
.loading-wave span:nth-child(2) { animation-delay: 0.1s; }
.loading-wave span:nth-child(3) { animation-delay: 0.2s; }
.loading-wave span:nth-child(4) { animation-delay: 0.3s; }
.loading-wave span:nth-child(5) { animation-delay: 0.4s; }

@keyframes loadingWave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* ========================================
   رسوم متحركة للانتقالات - Transition Animations
   ======================================== */

.page-transition-enter {
  opacity: 0;
  transform: translateX(30px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

/* ========================================
   تحسينات الأداء - Performance Optimizations
   ======================================== */

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-pulse,
  .animate-spin,
  .animate-bounce,
  .animate-heartbeat {
    animation: none !important;
  }
}

/* ========================================
   رسوم متحركة للطباعة
   ======================================== */

@media print {
  *,
  *::before,
  *::after {
    animation: none !important;
    transition: none !important;
  }
}
