/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   التصميم الأساسي - Base Styles
   ======================================== */

/* ========================================
   إعادة تعيين الأساسيات
   ======================================== */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-arabic);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--background);
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* دعم اللغة الإنجليزية */
[data-lang="en"] body {
  font-family: var(--font-family-english);
  direction: ltr;
  text-align: left;
}

/* ========================================
   العناصر الأساسية
   ======================================== */

h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-4);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-variant);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

ul, ol {
  margin-bottom: var(--spacing-4);
  padding-right: var(--spacing-6);
}

[data-lang="en"] ul,
[data-lang="en"] ol {
  padding-right: 0;
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-1);
  color: var(--text-secondary);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-6);
}

th, td {
  padding: var(--spacing-3);
  text-align: right;
  border-bottom: 1px solid var(--divider);
}

[data-lang="en"] th,
[data-lang="en"] td {
  text-align: left;
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--surface-variant);
}

/* ========================================
   الحاوي الرئيسي
   ======================================== */

.app-container {
  display: grid;
  grid-template-areas: 
    "header header"
    "sidebar main"
    "sidebar status";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr var(--status-bar-height);
  height: 100vh;
  overflow: hidden;
}

.app-container.sidebar-collapsed {
  grid-template-columns: var(--sidebar-collapsed-width) 1fr;
}

/* ========================================
   شريط العنوان
   ======================================== */

.app-header {
  grid-area: header;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-6);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  color: white;
  box-shadow: var(--shadow-md);
  z-index: var(--z-sticky);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-weight: var(--font-weight-bold);
}

.app-logo i {
  font-size: var(--font-size-2xl);
  color: var(--warning-300);
}

.app-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.app-version {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 var(--spacing-8);
}

.search-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-4) var(--spacing-2) var(--spacing-10);
  border: none;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: var(--font-size-sm);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

[data-lang="en"] .search-input {
  padding: var(--spacing-2) var(--spacing-10) var(--spacing-2) var(--spacing-4);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.search-icon {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
}

[data-lang="en"] .search-icon {
  right: auto;
  left: var(--spacing-3);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.control-btn:active {
  transform: translateY(0);
}

.language-toggle {
  width: auto;
  padding: 0 var(--spacing-3);
  gap: var(--spacing-2);
}

.lang-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--error-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  text-align: center;
  line-height: 1;
}

[data-lang="en"] .notification-badge {
  right: auto;
  left: -2px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.2);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

[data-lang="en"] .user-details {
  align-items: flex-end;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1.2;
}

.user-role {
  font-size: var(--font-size-xs);
  opacity: 0.8;
  line-height: 1.2;
}

.user-menu-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
}

.user-menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* ========================================
   الفئات المساعدة
   ======================================== */

.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-disabled { color: var(--text-disabled); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-500); }
.bg-success { background-color: var(--success-500); }
.bg-warning { background-color: var(--warning-500); }
.bg-error { background-color: var(--error-500); }
.bg-info { background-color: var(--info-500); }

.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
