/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   المكونات - Components
   ======================================== */

/* ========================================
   الأزرار - Buttons
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* أنواع الأزرار */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--surface-variant);
  border-color: var(--primary-color);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: none;
}

.btn-ghost:hover {
  background: var(--surface-variant);
  color: var(--text-primary);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  color: white;
}

.btn-error {
  background: linear-gradient(135deg, var(--error-500), var(--error-600));
  color: white;
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

.btn-block {
  width: 100%;
}

.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: var(--radius-full);
}

/* ========================================
   البطاقات - Cards
   ======================================== */

.card {
  background: var(--surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--divider);
  background: var(--surface-variant);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.card-title i {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
}

.card-content {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--divider);
  background: var(--surface-variant);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ========================================
   النماذج - Forms
   ======================================== */

.form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--surface);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  width: 100%;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-input::placeholder {
  color: var(--text-disabled);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  right: var(--spacing-3);
  color: var(--text-disabled);
  pointer-events: none;
}

[data-lang="en"] .input-icon {
  right: auto;
  left: var(--spacing-3);
}

.input-group .form-input {
  padding-right: var(--spacing-10);
}

[data-lang="en"] .input-group .form-input {
  padding-right: var(--spacing-4);
  padding-left: var(--spacing-10);
}

.password-toggle {
  position: absolute;
  left: var(--spacing-3);
  background: none;
  border: none;
  color: var(--text-disabled);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-base);
  transition: color var(--transition-fast);
}

[data-lang="en"] .password-toggle {
  left: auto;
  right: var(--spacing-3);
}

.password-toggle:hover {
  color: var(--text-secondary);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

/* ========================================
   الجداول - Tables
   ======================================== */

.table-container {
  background: var(--surface);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.data-table th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  color: white;
  padding: var(--spacing-4);
  text-align: right;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
}

[data-lang="en"] .data-table th {
  text-align: left;
}

.data-table td {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--divider);
  color: var(--text-primary);
  vertical-align: middle;
}

.data-table tr:hover {
  background: var(--surface-variant);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* ========================================
   الشارات - Badges
   ======================================== */

.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-800);
}

.badge-success {
  background: var(--success-100);
  color: var(--success-800);
}

.badge-warning {
  background: var(--warning-100);
  color: var(--warning-800);
}

.badge-error {
  background: var(--error-100);
  color: var(--error-800);
}

.badge-info {
  background: var(--info-100);
  color: var(--info-800);
}

/* ========================================
   النوافذ المنبثقة - Modals
   ======================================== */

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--surface);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: transform var(--transition-normal);
}

.modal.show .modal-content {
  transform: scale(1) translateY(0);
}

/* ========================================
   الإشعارات - Notifications
   ======================================== */

.notification-container {
  position: fixed;
  top: var(--spacing-6);
  right: var(--spacing-6);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  max-width: 400px;
}

[data-lang="en"] .notification-container {
  right: auto;
  left: var(--spacing-6);
}

.notification {
  background: var(--surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  padding: var(--spacing-4);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

[data-lang="en"] .notification {
  transform: translateX(-100%);
}

.notification.show {
  transform: translateX(0);
}

.notification-icon {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  color: white;
  flex-shrink: 0;
}

.notification-success .notification-icon {
  background: var(--success-500);
}

.notification-warning .notification-icon {
  background: var(--warning-500);
}

.notification-error .notification-icon {
  background: var(--error-500);
}

.notification-info .notification-icon {
  background: var(--info-500);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.notification-message {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-disabled);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-base);
  transition: color var(--transition-fast);
  flex-shrink: 0;
}

.notification-close:hover {
  color: var(--text-secondary);
}

/* ========================================
   شريط التحميل - Loading
   ======================================== */

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
  padding: var(--spacing-6);
  color: var(--text-secondary);
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========================================
   الحالة الفارغة - Empty State
   ======================================== */

.empty-state {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: var(--spacing-6);
  opacity: 0.5;
  color: var(--text-disabled);
}

.empty-state h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-3);
}

.empty-state p {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}
