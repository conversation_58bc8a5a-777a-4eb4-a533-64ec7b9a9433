/* ========================================
   واجهة تسجيل الدخول العصرية
   Modern Login Interface
   ======================================== */

/* ========================================
   النافذة الرئيسية
   ======================================== */

.modern-login-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-login-modal.show {
    opacity: 1;
    visibility: visible;
}

/* ========================================
   الخلفية المتحركة
   ======================================== */

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        #667eea 0%, 
        #764ba2 25%, 
        #f093fb 50%, 
        #f5576c 75%, 
        #4facfe 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.login-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px, 80px 80px;
    animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.login-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

/* ========================================
   الحاوية الرئيسية
   ======================================== */

.modern-login-container {
    display: flex;
    width: 90%;
    max-width: 1200px;
    height: 80vh;
    max-height: 700px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    overflow: hidden;
    transform: scale(0.9) translateY(50px);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-login-modal.show .modern-login-container {
    transform: scale(1) translateY(0);
}

/* ========================================
   لوحة المعلومات (الجانب الأيسر)
   ======================================== */

.login-info-panel {
    flex: 1;
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.login-info-panel::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: backgroundMove 30s linear infinite;
}

@keyframes backgroundMove {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-50px, -50px) rotate(360deg); }
}

/* ========================================
   شعار العلامة التجارية
   ======================================== */

.login-brand {
    text-align: center;
    margin-bottom: 60px;
    z-index: 2;
    position: relative;
}

.brand-logo {
    position: relative;
    margin-bottom: 30px;
}

.logo-icon {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    margin: 0 auto;
    position: relative;
    z-index: 3;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring-1 {
    width: 120px;
    height: 120px;
    animation: ringPulse 3s ease-in-out infinite;
}

.ring-2 {
    width: 140px;
    height: 140px;
    animation: ringPulse 3s ease-in-out infinite 0.5s;
}

.ring-3 {
    width: 160px;
    height: 160px;
    animation: ringPulse 3s ease-in-out infinite 1s;
}

@keyframes ringPulse {
    0%, 100% { 
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.1;
    }
}

.brand-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.brand-subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 300;
}

/* ========================================
   ميزات النظام
   ======================================== */

.login-features {
    width: 100%;
    z-index: 2;
    position: relative;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(-10px);
    background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-left: 20px;
    flex-shrink: 0;
}

.feature-text h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.feature-text p {
    font-size: 14px;
    opacity: 0.8;
    margin: 0;
}

/* ========================================
   لوحة النموذج (الجانب الأيمن)
   ======================================== */

.login-form-panel {
    flex: 1;
    padding: 60px 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.95);
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-header h2 {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.login-header p {
    font-size: 16px;
    color: #7f8c8d;
    margin: 0;
}

/* ========================================
   النموذج العصري
   ======================================== */

.modern-login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 30px;
}

.input-container {
    position: relative;
}

.modern-input {
    width: 100%;
    padding: 20px 50px 20px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 16px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    outline: none;
}

.modern-input:focus {
    border-color: #1976d2;
    background: white;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.modern-input:focus + .modern-label,
.modern-input:not(:placeholder-shown) + .modern-label {
    transform: translateY(-35px) scale(0.85);
    color: #1976d2;
    background: white;
    padding: 0 8px;
}

.modern-label {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 16px;
    color: #7f8c8d;
    pointer-events: none;
    transition: all 0.3s ease;
    transform-origin: right center;
}

.input-icon {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
    font-size: 18px;
    transition: color 0.3s ease;
}

.modern-input:focus ~ .input-icon {
    color: #1976d2;
}

.password-toggle {
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #1976d2;
    background: rgba(25, 118, 210, 0.1);
}

.input-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #1976d2, #42a5f5);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.modern-input:focus ~ .input-underline {
    transform: scaleX(1);
}

/* ========================================
   خيارات النموذج
   ======================================== */

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.modern-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.modern-checkbox input[type="checkbox"] {
    display: none;
}

.checkbox-mark {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e8ed;
    border-radius: 4px;
    margin-left: 10px;
    position: relative;
    transition: all 0.3s ease;
    background: white;
}

.checkbox-mark::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 6px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg) scale(0);
    transition: transform 0.2s ease;
}

.modern-checkbox input[type="checkbox"]:checked + .checkbox-mark {
    background: #1976d2;
    border-color: #1976d2;
}

.modern-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
    transform: rotate(45deg) scale(1);
}

.checkbox-text {
    font-size: 14px;
    color: #2c3e50;
}

.forgot-password {
    font-size: 14px;
    color: #1976d2;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #1565c0;
    text-decoration: underline;
}

/* ========================================
   زر تسجيل الدخول العصري
   ======================================== */

.modern-login-btn {
    width: 100%;
    height: 56px;
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

.modern-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
}

.modern-login-btn:active {
    transform: translateY(0);
}

.modern-login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.modern-login-btn:hover::before {
    left: 100%;
}

.btn-text {
    display: inline-block;
    transition: opacity 0.3s ease;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.loader-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-success {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 20px;
}

.modern-login-btn.loading .btn-text {
    opacity: 0;
}

.modern-login-btn.loading .btn-loader {
    opacity: 1;
}

.modern-login-btn.success .btn-text,
.modern-login-btn.success .btn-loader {
    opacity: 0;
}

.modern-login-btn.success .btn-success {
    opacity: 1;
}

/* ========================================
   تذييل النموذج
   ======================================== */

.login-footer {
    text-align: center;
}

.security-info {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #7f8c8d;
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
}

.security-info i {
    color: #1976d2;
}

/* ========================================
   رسائل الخطأ في الحقول
   ======================================== */

.input-error {
    position: absolute;
    bottom: -20px;
    right: 0;
    font-size: 12px;
    color: #f44336;
    background: rgba(244, 67, 54, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(244, 67, 54, 0.2);
    animation: errorSlideIn 0.3s ease;
}

@keyframes errorSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.input-container.error .modern-input {
    border-color: #f44336;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.input-container.error .input-icon {
    color: #f44336;
}

/* ========================================
   تحسينات إضافية للتفاعل
   ======================================== */

.input-container.focused .input-icon {
    animation: iconPulse 0.3s ease;
}

@keyframes iconPulse {
    0% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.2); }
    100% { transform: translateY(-50%) scale(1.1); }
}

.modern-input:focus {
    animation: inputGlow 0.3s ease;
}

@keyframes inputGlow {
    0% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.3); }
    50% { box-shadow: 0 0 0 8px rgba(25, 118, 210, 0.1); }
    100% { box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1); }
}

/* ========================================
   الجسيمات المتحركة
   ======================================== */

.floating-particle {
    transition: opacity 0.3s ease;
}

.login-background:hover .floating-particle {
    opacity: 0.8 !important;
}

/* ========================================
   تحسينات الأداء
   ======================================== */

.modern-login-modal * {
    will-change: transform, opacity;
}

.modern-login-container {
    contain: layout style paint;
}

/* ========================================
   حالات التحميل المتقدمة
   ======================================== */

.modern-login-btn.loading {
    background: linear-gradient(135deg, #1565c0, #1976d2);
    cursor: not-allowed;
}

.modern-login-btn.success {
    background: linear-gradient(135deg, #4caf50, #66bb6a);
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* ========================================
   التصميم المتجاوب
   ======================================== */

@media (max-width: 768px) {
    .modern-login-container {
        flex-direction: column;
        width: 95%;
        height: 90vh;
    }

    .login-info-panel {
        flex: none;
        height: 200px;
        padding: 30px 20px;
    }

    .login-brand {
        margin-bottom: 20px;
    }

    .brand-title {
        font-size: 20px;
    }

    .login-features {
        display: none;
    }

    .login-form-panel {
        padding: 30px 20px;
    }

    .login-header h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .modern-login-container {
        width: 100%;
        height: 100vh;
        border-radius: 0;
    }

    .login-form-panel {
        padding: 20px 15px;
    }

    .modern-input {
        padding: 16px 45px 16px 16px;
    }

    .modern-label {
        top: 16px;
        right: 16px;
    }
}
