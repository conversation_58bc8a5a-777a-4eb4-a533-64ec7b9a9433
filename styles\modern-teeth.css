/* ========================================
   تصميم الأسنان العصري - Modern Teeth Design
   نظام إدارة معمل الأسنان المتقدم v2.0
   ======================================== */

/* ========================================
   الحاوي الرئيسي للأسنان
   ======================================== */

.teeth-selector {
    background: #f8fafc;
    border-radius: 15px;
    padding: 40px 20px;
    border: 2px solid #e2e8f0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.teeth-diagram {
    text-align: center;
    position: relative;
}

/* ========================================
   تصميم الفكين
   ======================================== */

.jaw {
    margin-bottom: 60px;
    position: relative;
    background: white;
    border-radius: 15px;
    padding: 30px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.jaw-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #16a34a;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.jaw-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #16a34a;
    border-radius: 2px;
}

/* تسميات الجوانب */
.jaw-sides-labels {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 0 20px;
}

.side-label {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    border: 2px solid #a5d6a7;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.2);
    text-align: center;
    min-width: 120px;
}

.side-label.right-side {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
    border-color: #a5d6a7;
}

.side-label.left-side {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #2e7d32;
    border-color: #a5d6a7;
}

/* ========================================
   صف الأسنان
   ======================================== */

.teeth-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-bottom: 20px;
    padding: 20px 10px;
    position: relative;
    background: transparent;
}

/* الخط الفاصل الأزرق كما في التصميم */
.teeth-row::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 3px;
    height: 120px;
    background: #2563eb;
    border-radius: 2px;
    z-index: 10;
    box-shadow: 0 0 6px rgba(37, 99, 235, 0.3);
}

@keyframes separatorPulse {
    0%, 100% {
        opacity: 0.6;
        box-shadow: 0 0 10px rgba(25, 118, 210, 0.3);
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 20px rgba(25, 118, 210, 0.6);
    }
}

/* ========================================
   تصميم السن الجديد
   ======================================== */

.tooth {
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    position: relative;
    transform-style: preserve-3d;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.tooth-visual {
    width: 80px;
    height: 80px;
    border: 2px solid #d1d5db;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* شكل السن الموحد - حجم طبيعي */
.tooth-shape {
    font-size: 2.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: all 0.3s ease;
    line-height: 1;
    width: 100%;
    height: 100%;
}

.tooth:hover .tooth-shape {
    opacity: 1;
    transform: scale(1.05);
}

/* تأثير اللمعان */
.tooth-visual::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 30%;
    height: 30%;
    background: radial-gradient(ellipse, rgba(255,255,255,0.8) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0.6;
}

/* تأثير الانعكاس */
.tooth-visual::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.tooth:hover .tooth-visual::after {
    transform: translateX(100%);
}

.tooth-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: #374151;
    z-index: 2;
    position: absolute;
    top: 90px;
    left: 50%;
    transform: translateX(-50%);
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    text-align: center;
}

.tooth-label {
    font-size: 0.7rem;
    color: #6b7280;
    margin-top: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* ألوان مختلفة للأرقام حسب التصميم المطلوب */
.tooth:nth-child(1) .tooth-number { color: #dc2626; } /* أحمر - رقم 8 */
.tooth:nth-child(2) .tooth-number { color: #ea580c; } /* برتقالي - رقم 7 */
.tooth:nth-child(3) .tooth-number { color: #ea580c; } /* برتقالي - رقم 6 */
.tooth:nth-child(4) .tooth-number { color: #16a34a; } /* أخضر - رقم 5 */
.tooth:nth-child(5) .tooth-number { color: #0891b2; } /* سماوي - رقم 4 */
.tooth:nth-child(6) .tooth-number { color: #16a34a; } /* أخضر - رقم 3 */
.tooth:nth-child(7) .tooth-number { color: #2563eb; } /* أزرق - رقم 2 */
.tooth:nth-child(8) .tooth-number { color: #dc2626; } /* أحمر - رقم 1 */

/* ========================================
   حالات التفاعل
   ======================================== */

.tooth:hover {
    transform: translateY(-5px) scale(1.05);
}

.tooth:hover .tooth-visual {
    border-color: #3b82f6;
    background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
    box-shadow: 
        0 8px 25px rgba(59, 130, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.tooth:hover .tooth-number {
    color: #1d4ed8;
}

.tooth:hover .tooth-label {
    color: #3b82f6;
    transform: scale(1.1);
}

/* ========================================
   حالة الاختيار
   ======================================== */

.tooth.selected {
    transform: translateY(-8px) scale(1.1);
    animation: selectedPulse 2s ease-in-out infinite;
}

.tooth.selected .tooth-visual {
    border-color: #10b981;
    background: linear-gradient(145deg, #10b981 0%, #059669 100%);
    box-shadow: 
        0 12px 35px rgba(16, 185, 129, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.tooth.selected .tooth-number {
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.tooth.selected .tooth-label {
    color: #10b981;
    font-weight: 700;
    transform: scale(1.15);
}

@keyframes selectedPulse {
    0%, 100% { 
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
    }
    50% { 
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
}

/* ========================================
   شكل السن الموحد (مثل اللوجو)
   ======================================== */

.tooth-visual {
    border-radius: 50% 50% 15px 15px;
    background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
    border-color: #1976d2;
    position: relative;
}

.tooth-visual::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 35px;
    height: 35px;
    background-image: url('../assets/tooth-logo.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.8;
    z-index: 1;
    transition: all 0.3s ease;
}

.tooth.selected .tooth-visual {
    background: linear-gradient(145deg, #10b981 0%, #059669 100%);
    border-color: #059669;
}

.tooth:hover .tooth-visual::before {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
    filter: brightness(1.2) saturate(1.3);
}

.tooth.selected .tooth-visual::before {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
    filter: brightness(0) invert(1);
    animation: logoGlow 2s ease-in-out infinite;
}

@keyframes logoGlow {
    0%, 100% {
        filter: brightness(0) invert(1) drop-shadow(0 0 5px rgba(255,255,255,0.8));
    }
    50% {
        filter: brightness(0) invert(1) drop-shadow(0 0 15px rgba(255,255,255,1));
    }
}

/* ========================================
   تأثيرات خاصة للأضراس
   ======================================== */

.tooth-visual.molar::before {
    width: 25%;
    height: 25%;
    top: 15%;
    left: 15%;
}

.tooth-visual.molar .tooth-number {
    font-size: 1rem;
}

/* ========================================
   تحسينات إضافية
   ======================================== */

.tooth:active {
    transform: translateY(-3px) scale(1.02);
}

.tooth-visual {
    border-style: solid;
    border-image: linear-gradient(45deg, #cbd5e1, #94a3b8) 1;
}

.tooth.selected .tooth-visual {
    border-image: linear-gradient(45deg, #10b981, #059669) 1;
}

/* تأثير الضوء المتحرك */
@keyframes lightSweep {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(200%) skewX(-15deg); }
}

.tooth:hover .tooth-visual::after {
    animation: lightSweep 0.8s ease-out;
}

/* ========================================
   تحسينات الأداء
   ======================================== */

.tooth {
    will-change: transform;
    contain: layout style paint;
}

.tooth-visual {
    will-change: background, border-color, box-shadow;
    contain: layout style paint;
}

.tooth-visual::before {
    will-change: transform, opacity, filter;
}

/* ========================================
   تحسينات خاصة للوجو
   ======================================== */

.tooth-visual {
    position: relative;
    overflow: visible; /* للسماح بظهور الأشعة الذهبية */
}

/* تأثير خاص عند التمرير على اللوجو */
.tooth:hover .tooth-visual::before {
    animation: logoHover 0.6s ease-in-out;
}

@keyframes logoHover {
    0% { transform: translate(-50%, -50%) scale(1) rotate(0deg); }
    50% { transform: translate(-50%, -50%) scale(1.15) rotate(5deg); }
    100% { transform: translate(-50%, -50%) scale(1.1) rotate(0deg); }
}

/* تأثير النبضة للوجو المختار */
.tooth.selected .tooth-visual::before {
    animation: logoSelected 2s ease-in-out infinite;
}

@keyframes logoSelected {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1.05);
        filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255,255,255,0.8));
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        filter: brightness(0) invert(1) drop-shadow(0 0 15px rgba(255,255,255,1));
    }
}

/* ========================================
   التصميم المتجاوب
   ======================================== */

@media (max-width: 768px) {
    .teeth-row {
        gap: 8px;
        padding: 15px;
    }
    
    .tooth-visual {
        width: 45px;
        height: 45px;
    }
    
    .tooth-visual.molar {
        width: 50px;
        height: 50px;
    }
    
    .tooth-number {
        font-size: 0.8rem;
    }
    
    .tooth-label {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .teeth-selector {
        padding: 20px;
    }
    
    .teeth-row {
        gap: 6px;
        padding: 10px;
    }
    
    .tooth-visual {
        width: 40px;
        height: 40px;
    }
    
    .tooth-visual.molar {
        width: 45px;
        height: 45px;
    }
    
    .jaw-title {
        font-size: 1.2rem;
        padding: 12px 20px;
    }
}

/* ========================================
   الأنيميشن والتأثيرات المتقدمة
   ======================================== */

@keyframes gentleVibration {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-1px); }
    75% { transform: translateX(1px); }
}

@keyframes clickBounce {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

@keyframes selectionGlow {
    0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
    100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

@keyframes toothShine {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(200%) skewX(-15deg); }
}

@keyframes floatingEffect {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

@keyframes colorShift {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

/* تطبيق التأثيرات */
.tooth.selected {
    animation: selectionGlow 2s infinite;
}

.tooth:hover {
    animation: floatingEffect 1s ease-in-out infinite;
}

.jaw-title {
    animation: colorShift 10s linear infinite;
}

/* ========================================
   تأثيرات الجسيمات
   ======================================== */

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #10b981;
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 2s ease-out forwards;
}

@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(0) translateY(-50px);
    }
}

/* ========================================
   تأثيرات الإضاءة المتقدمة
   ======================================== */

.tooth-visual {
    position: relative;
    overflow: hidden;
}

.tooth-visual::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 30%;
    height: 30%;
    background: radial-gradient(ellipse, rgba(255,255,255,0.8) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.tooth:hover .tooth-visual::before {
    opacity: 1;
    transform: scale(1.2);
}

/* ========================================
   تحسينات الأداء
   ======================================== */

.tooth {
    contain: layout style paint;
    will-change: transform;
}

.tooth-visual {
    contain: layout style paint;
    will-change: background, border-color, box-shadow;
}

/* ========================================
   تأثيرات خاصة للأسنان المختلفة
   ======================================== */

.tooth-visual.incisor:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
}

.tooth-visual.canine:hover {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.6);
}

.tooth-visual.premolar:hover {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.6);
}

.tooth-visual.molar:hover {
    box-shadow: 0 0 25px rgba(14, 165, 233, 0.6);
}
