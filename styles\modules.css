/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   الوحدات - Modules
   ======================================== */

/* ========================================
   الشريط الجانبي - Sidebar
   ======================================== */

.sidebar {
  grid-area: sidebar;
  background: var(--surface);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all var(--transition-normal);
  z-index: var(--z-sticky);
}

[data-lang="en"] .sidebar {
  border-left: none;
  border-right: 1px solid var(--border-color);
}

.sidebar-header {
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--divider);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--surface-variant);
}

.sidebar-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background: var(--surface);
  color: var(--text-primary);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-4) 0;
}

.nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0 var(--spacing-3) var(--spacing-1) var(--spacing-3);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  background: var(--surface-variant);
  color: var(--text-primary);
  transform: translateX(-2px);
}

[data-lang="en"] .nav-link:hover {
  transform: translateX(2px);
}

.nav-link.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  color: white;
  box-shadow: var(--shadow-sm);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  right: -3px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: var(--primary-color);
  border-radius: var(--radius-base);
}

[data-lang="en"] .nav-link.active::before {
  right: auto;
  left: -3px;
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-badge {
  background: var(--error-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  text-align: center;
  line-height: 1;
}

.sidebar-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--divider);
  background: var(--surface-variant);
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.status-item i {
  width: 12px;
  text-align: center;
}

/* الشريط الجانبي المطوي */
.app-container.sidebar-collapsed .sidebar {
  width: var(--sidebar-collapsed-width);
}

.app-container.sidebar-collapsed .sidebar-title,
.app-container.sidebar-collapsed .nav-text,
.app-container.sidebar-collapsed .system-status {
  display: none;
}

.app-container.sidebar-collapsed .nav-link {
  justify-content: center;
  padding: var(--spacing-3);
}

.app-container.sidebar-collapsed .nav-link::before {
  display: none;
}

/* ========================================
   المحتوى الرئيسي - Main Content
   ======================================== */

.main-content {
  grid-area: main;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--background);
}

.breadcrumb {
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--surface);
  border-bottom: 1px solid var(--divider);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.breadcrumb-item {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb-item:hover {
  color: var(--primary-color);
}

.breadcrumb-item.active {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 var(--spacing-2);
  color: var(--text-disabled);
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-6);
}

/* ========================================
   شريط الحالة - Status Bar
   ======================================== */

.status-bar {
  grid-area: status;
  background: var(--surface);
  border-top: 1px solid var(--divider);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-6);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.status-left,
.status-center,
.status-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.status-online {
  color: var(--success-500);
}

.status-offline {
  color: var(--error-500);
}

/* ========================================
   شاشة التحميل - Loading Screen
   ======================================== */

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: white;
}

.loading-content {
  text-align: center;
  max-width: 400px;
  padding: var(--spacing-8);
}

.loading-logo {
  font-size: 5rem;
  margin-bottom: var(--spacing-6);
  animation: pulse 2s infinite;
}

.loading-text h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-2);
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.loading-text p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin-bottom: var(--spacing-8);
}

.loading-spinner {
  margin-bottom: var(--spacing-6);
}

.loading-spinner .spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255,255,255,0.3);
  border-top: 4px solid white;
  margin: 0 auto;
}

.loading-progress {
  margin-top: var(--spacing-6);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255,255,255,0.3);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-3);
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
  width: 0%;
}

.progress-text {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* ========================================
   نافذة تسجيل الدخول - Login Modal
   ======================================== */

.login-form {
  padding: var(--spacing-8);
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.login-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4) auto;
  font-size: var(--font-size-3xl);
  color: white;
  box-shadow: var(--shadow-lg);
}

.login-header h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.login-header p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 0;
}

.login-footer {
  margin-top: var(--spacing-6);
  text-align: center;
}

.login-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: var(--surface-variant);
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
}

/* ========================================
   الإحصائيات - Statistics
   ======================================== */

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.stat-card {
  background: var(--surface);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-variant));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-2xl);
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-1) 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-content p {
  color: var(--text-secondary);
  margin: 0;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

/* ========================================
   الرسوم البيانية - Charts
   ======================================== */

.charts-section {
  margin: var(--spacing-8) 0;
}

.chart-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.chart-card {
  min-height: 400px;
  background: var(--surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.chart-card .card-content {
  padding: var(--spacing-6);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-card canvas {
  max-height: 100%;
  max-width: 100%;
}

/* ========================================
   الأزرار العائمة - Floating Action Buttons
   ======================================== */

.fab {
  position: fixed;
  bottom: var(--spacing-8);
  right: var(--spacing-8);
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
  color: white;
  border: none;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  transition: all var(--transition-fast);
  z-index: var(--z-fixed);
}

[data-lang="en"] .fab {
  right: auto;
  left: var(--spacing-8);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.fab:active {
  transform: scale(0.95);
}
