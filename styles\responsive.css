/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   التصميم المتجاوب - Responsive Design
   ======================================== */

/* ========================================
   الشاشات الكبيرة جداً - Extra Large (2xl)
   1536px وأكبر
   ======================================== */

@media (min-width: 1536px) {
  .container {
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .chart-row {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .content-area {
    padding: var(--spacing-8);
  }
  
  .card-content {
    padding: var(--spacing-8);
  }
}

/* ========================================
   الشاشات الكبيرة - Large (xl)
   1280px - 1535px
   ======================================== */

@media (max-width: 1535px) and (min-width: 1280px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .chart-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .sidebar {
    width: 260px;
  }
  
  .app-container {
    grid-template-columns: 260px 1fr;
  }
}

/* ========================================
   الشاشات المتوسطة الكبيرة - Large (lg)
   1024px - 1279px
   ======================================== */

@media (max-width: 1279px) and (min-width: 1024px) {
  .app-header {
    padding: 0 var(--spacing-4);
  }
  
  .header-center {
    max-width: 300px;
    margin: 0 var(--spacing-4);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    width: 240px;
  }
  
  .app-container {
    grid-template-columns: 240px 1fr;
  }
  
  .content-area {
    padding: var(--spacing-4);
  }
  
  .card-content {
    padding: var(--spacing-4);
  }
  
  .data-table {
    font-size: var(--font-size-xs);
  }
  
  .data-table th,
  .data-table td {
    padding: var(--spacing-2) var(--spacing-3);
  }
}

/* ========================================
   الشاشات المتوسطة - Medium (md)
   768px - 1023px
   ======================================== */

@media (max-width: 1023px) and (min-width: 768px) {
  .app-container {
    grid-template-areas: 
      "header header"
      "main main"
      "status status";
    grid-template-columns: 1fr;
    grid-template-rows: var(--header-height) 1fr var(--status-bar-height);
  }
  
  .sidebar {
    position: fixed;
    top: var(--header-height);
    right: -280px;
    height: calc(100vh - var(--header-height) - var(--status-bar-height));
    width: 280px;
    z-index: var(--z-modal);
    transition: right var(--transition-normal);
    box-shadow: var(--shadow-lg);
  }
  
  [data-lang="en"] .sidebar {
    right: auto;
    left: -280px;
    transition: left var(--transition-normal);
  }
  
  .sidebar.open {
    right: 0;
  }
  
  [data-lang="en"] .sidebar.open {
    left: 0;
  }
  
  .sidebar-overlay {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    bottom: var(--status-bar-height);
    background: var(--overlay);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }
  
  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }
  
  .header-left {
    gap: var(--spacing-2);
  }
  
  .app-title {
    display: none;
  }
  
  .header-center {
    display: none;
  }
  
  .header-controls {
    gap: var(--spacing-1);
  }
  
  .control-btn {
    width: 36px;
    height: 36px;
  }
  
  .user-details {
    display: none;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
  }
  
  .chart-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .chart-card {
    min-height: 300px;
  }
  
  .chart-card .card-content {
    height: 200px;
  }
  
  .content-area {
    padding: var(--spacing-4);
  }
  
  .card-header {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
  
  .header-actions {
    width: 100%;
    display: flex;
    gap: var(--spacing-2);
    flex-wrap: wrap;
  }
  
  .header-actions .btn {
    flex: 1;
    min-width: 120px;
  }
  
  .data-table-container {
    overflow-x: auto;
  }
  
  .data-table {
    min-width: 600px;
  }
  
  .modal-content {
    width: 95%;
    margin: var(--spacing-4);
  }
  
  .fab {
    bottom: var(--spacing-6);
    right: var(--spacing-6);
  }
  
  [data-lang="en"] .fab {
    right: auto;
    left: var(--spacing-6);
  }
}

/* ========================================
   الشاشات الصغيرة - Small (sm)
   640px - 767px
   ======================================== */

@media (max-width: 767px) and (min-width: 640px) {
  .app-header {
    padding: 0 var(--spacing-3);
  }
  
  .app-logo {
    gap: var(--spacing-2);
  }
  
  .app-logo i {
    font-size: var(--font-size-xl);
  }
  
  .app-version {
    display: none;
  }
  
  .header-controls {
    gap: var(--spacing-1);
  }
  
  .control-btn {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-sm);
  }
  
  .language-toggle .lang-text {
    display: none;
  }
  
  .user-info {
    padding: var(--spacing-1);
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-base);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }
  
  .stat-card {
    padding: var(--spacing-4);
    gap: var(--spacing-3);
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: var(--font-size-xl);
  }
  
  .stat-content h3 {
    font-size: var(--font-size-xl);
  }
  
  .chart-card {
    min-height: 250px;
  }
  
  .chart-card .card-content {
    height: 180px;
    padding: var(--spacing-3);
  }
  
  .content-area {
    padding: var(--spacing-3);
  }
  
  .card-content {
    padding: var(--spacing-3);
  }
  
  .card-header {
    padding: var(--spacing-3);
  }
  
  .card-title {
    font-size: var(--font-size-base);
  }
  
  .header-actions .btn {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
  }
  
  .data-table {
    font-size: var(--font-size-xs);
  }
  
  .data-table th,
  .data-table td {
    padding: var(--spacing-2);
  }
  
  .breadcrumb {
    padding: var(--spacing-3);
    font-size: var(--font-size-xs);
  }
  
  .status-bar {
    padding: 0 var(--spacing-3);
    font-size: var(--font-size-xs);
  }
  
  .status-center {
    display: none;
  }
  
  .modal-content {
    width: 95%;
    margin: var(--spacing-2);
  }
  
  .login-form {
    padding: var(--spacing-6);
  }
  
  .notification-container {
    top: var(--spacing-4);
    right: var(--spacing-4);
    left: var(--spacing-4);
    max-width: none;
  }
  
  [data-lang="en"] .notification-container {
    left: var(--spacing-4);
    right: var(--spacing-4);
  }
}

/* ========================================
   الشاشات الصغيرة جداً - Extra Small
   أقل من 640px
   ======================================== */

@media (max-width: 639px) {
  .app-header {
    padding: 0 var(--spacing-2);
    height: 56px;
  }
  
  .app-container {
    grid-template-rows: 56px 1fr 28px;
  }
  
  .sidebar {
    top: 56px;
    height: calc(100vh - 56px - 28px);
  }
  
  .sidebar-overlay {
    top: 56px;
    bottom: 28px;
  }
  
  .app-logo {
    gap: var(--spacing-1);
  }
  
  .app-logo i {
    font-size: var(--font-size-lg);
  }
  
  .app-title {
    font-size: var(--font-size-sm);
  }
  
  .control-btn {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-xs);
  }
  
  .user-info {
    gap: var(--spacing-1);
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-sm);
  }
  
  .user-menu-btn {
    display: none;
  }
  
  .stats-grid {
    gap: var(--spacing-2);
  }
  
  .stat-card {
    padding: var(--spacing-3);
    gap: var(--spacing-2);
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
    margin: 0 auto;
  }
  
  .stat-content h3 {
    font-size: var(--font-size-lg);
  }
  
  .stat-content p {
    font-size: var(--font-size-xs);
  }
  
  .chart-card {
    min-height: 200px;
  }
  
  .chart-card .card-content {
    height: 150px;
    padding: var(--spacing-2);
  }
  
  .content-area {
    padding: var(--spacing-2);
  }
  
  .card-content {
    padding: var(--spacing-2);
  }
  
  .card-header {
    padding: var(--spacing-2);
  }
  
  .card-title {
    font-size: var(--font-size-sm);
    gap: var(--spacing-2);
  }
  
  .card-title i {
    font-size: var(--font-size-lg);
  }
  
  .header-actions {
    flex-direction: column;
  }
  
  .header-actions .btn {
    width: 100%;
    justify-content: center;
  }
  
  .data-table-container {
    margin: 0 calc(-1 * var(--spacing-2));
  }
  
  .data-table {
    min-width: 500px;
    font-size: var(--font-size-xs);
  }
  
  .data-table th,
  .data-table td {
    padding: var(--spacing-1) var(--spacing-2);
  }
  
  .breadcrumb {
    padding: var(--spacing-2);
    font-size: var(--font-size-xs);
  }
  
  .status-bar {
    padding: 0 var(--spacing-2);
    font-size: 10px;
    height: 28px;
  }
  
  .status-right {
    display: none;
  }
  
  .modal-content {
    width: 100%;
    height: 100%;
    border-radius: 0;
    margin: 0;
  }
  
  .login-form {
    padding: var(--spacing-4);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .notification-container {
    top: var(--spacing-2);
    right: var(--spacing-2);
    left: var(--spacing-2);
  }
  
  .notification {
    padding: var(--spacing-3);
  }
  
  .fab {
    width: 48px;
    height: 48px;
    bottom: var(--spacing-4);
    right: var(--spacing-4);
    font-size: var(--font-size-lg);
  }
  
  [data-lang="en"] .fab {
    right: auto;
    left: var(--spacing-4);
  }
}

/* ========================================
   تحسينات خاصة للأجهزة اللوحية
   ======================================== */

@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .sidebar {
    width: 200px;
  }
  
  .app-container {
    grid-template-columns: 200px 1fr;
  }
  
  .nav-text {
    font-size: var(--font-size-xs);
  }
  
  .sidebar-title {
    font-size: var(--font-size-xs);
  }
}

@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-row {
    grid-template-columns: 1fr;
  }
}

/* ========================================
   تحسينات للطباعة
   ======================================== */

@media print {
  .app-header,
  .sidebar,
  .status-bar,
  .fab,
  .notification-container,
  .modal,
  .btn,
  .header-actions {
    display: none !important;
  }
  
  .app-container {
    grid-template-areas: "main";
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    height: auto;
  }
  
  .main-content {
    overflow: visible;
  }
  
  .content-area {
    padding: 0;
  }
  
  .card {
    break-inside: avoid;
    margin-bottom: 1rem;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .data-table {
    font-size: 10px;
  }
  
  .data-table th,
  .data-table td {
    padding: 0.25rem;
    border: 1px solid #000;
  }
}
