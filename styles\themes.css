/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   الثيمات والأوضاع - Themes
   ======================================== */

/* ========================================
   الوضع النهاري - Light Theme
   ======================================== */

.theme-light,
[data-theme="light"] {
  /* الألوان الأساسية */
  --primary-color: var(--primary-600);
  --primary-variant: var(--primary-700);
  --secondary-color: var(--secondary-500);
  
  /* ألوان الخلفية */
  --background: #f8fafc;
  --surface: #ffffff;
  --surface-variant: #f1f5f9;
  --surface-hover: #e2e8f0;
  
  /* ألوان النص */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-disabled: var(--gray-400);
  --text-inverse: #ffffff;
  
  /* ألوان الحدود */
  --border-color: var(--gray-200);
  --divider: var(--gray-100);
  --outline: var(--gray-300);
  
  /* ألوان الحالة */
  --success: var(--success-600);
  --warning: var(--warning-600);
  --error: var(--error-600);
  --info: var(--info-600);
  
  /* ألوان خاصة */
  --overlay: rgba(0, 0, 0, 0.5);
  --backdrop: rgba(0, 0, 0, 0.3);
  --glass: rgba(255, 255, 255, 0.8);
  
  /* الظلال */
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-1: 0 1px 3px var(--shadow-color);
  --shadow-2: 0 4px 6px var(--shadow-color);
  --shadow-3: 0 10px 15px var(--shadow-color);
  --shadow-4: 0 20px 25px var(--shadow-color);
}

/* ========================================
   الوضع الليلي - Dark Theme
   ======================================== */

.theme-dark,
[data-theme="dark"] {
  /* الألوان الأساسية */
  --primary-color: var(--primary-400);
  --primary-variant: var(--primary-300);
  --secondary-color: var(--secondary-400);
  
  /* ألوان الخلفية */
  --background: #0f172a;
  --surface: #1e293b;
  --surface-variant: #334155;
  --surface-hover: #475569;
  
  /* ألوان النص */
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-disabled: var(--gray-500);
  --text-inverse: var(--gray-900);
  
  /* ألوان الحدود */
  --border-color: var(--gray-600);
  --divider: var(--gray-700);
  --outline: var(--gray-500);
  
  /* ألوان الحالة */
  --success: var(--success-400);
  --warning: var(--warning-400);
  --error: var(--error-400);
  --info: var(--info-400);
  
  /* ألوان خاصة */
  --overlay: rgba(0, 0, 0, 0.7);
  --backdrop: rgba(0, 0, 0, 0.5);
  --glass: rgba(30, 41, 59, 0.8);
  
  /* الظلال */
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-1: 0 1px 3px var(--shadow-color);
  --shadow-2: 0 4px 6px var(--shadow-color);
  --shadow-3: 0 10px 15px var(--shadow-color);
  --shadow-4: 0 20px 25px var(--shadow-color);
}

/* ========================================
   انتقالات الثيم - Theme Transitions
   ======================================== */

* {
  transition: 
    background-color var(--transition-normal),
    border-color var(--transition-normal),
    color var(--transition-normal),
    box-shadow var(--transition-normal);
}

/* ========================================
   تخصيصات الوضع الليلي
   ======================================== */

.theme-dark .app-header {
  background: linear-gradient(135deg, var(--surface), var(--surface-variant));
  border-bottom: 1px solid var(--border-color);
}

.theme-dark .search-input {
  background: var(--surface-variant);
  border: 1px solid var(--border-color);
}

.theme-dark .search-input::placeholder {
  color: var(--text-disabled);
}

.theme-dark .control-btn {
  background: var(--surface-variant);
  color: var(--text-primary);
}

.theme-dark .control-btn:hover {
  background: var(--surface-hover);
}

.theme-dark .user-info {
  background: var(--surface-variant);
}

.theme-dark .user-info:hover {
  background: var(--surface-hover);
}

.theme-dark .sidebar {
  background: var(--surface);
  border-color: var(--border-color);
}

.theme-dark .nav-link:hover {
  background: var(--surface-variant);
}

.theme-dark .card {
  background: var(--surface);
  border-color: var(--border-color);
}

.theme-dark .card-header {
  background: var(--surface-variant);
  border-color: var(--divider);
}

.theme-dark .form-input,
.theme-dark .form-select,
.theme-dark .form-textarea {
  background: var(--surface);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.theme-dark .form-input:focus,
.theme-dark .form-select:focus,
.theme-dark .form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.theme-dark .data-table th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
}

.theme-dark .data-table tr:hover {
  background: var(--surface-variant);
}

.theme-dark .modal {
  background: var(--overlay);
}

.theme-dark .modal-content {
  background: var(--surface);
  border: 1px solid var(--border-color);
}

.theme-dark .notification {
  background: var(--surface);
  border-color: var(--border-color);
}

.theme-dark .loading-screen {
  background: linear-gradient(135deg, var(--background), var(--surface));
}

/* ========================================
   ثيم خاص بمعمل الأسنان - Dental Theme
   ======================================== */

.theme-dental {
  --primary-color: var(--dental-blue);
  --primary-variant: #1565c0;
  --secondary-color: var(--dental-teal);
  --accent-color: var(--dental-green);
  --warning-color: var(--dental-orange);
}

.theme-dental .app-header {
  background: linear-gradient(135deg, var(--dental-blue), var(--dental-teal));
}

.theme-dental .nav-link.active {
  background: linear-gradient(135deg, var(--dental-blue), var(--dental-teal));
}

.theme-dental .btn-primary {
  background: linear-gradient(135deg, var(--dental-blue), var(--dental-teal));
}

.theme-dental .stat-icon {
  background: linear-gradient(135deg, var(--dental-blue), var(--dental-teal));
}

.theme-dental .fab {
  background: linear-gradient(135deg, var(--dental-blue), var(--dental-teal));
}

/* ========================================
   ثيم عالي التباين - High Contrast Theme
   ======================================== */

.theme-high-contrast {
  --primary-color: #0066cc;
  --primary-variant: #004499;
  --background: #ffffff;
  --surface: #ffffff;
  --surface-variant: #f5f5f5;
  --text-primary: #000000;
  --text-secondary: #333333;
  --border-color: #000000;
  --divider: #666666;
  --success: #008000;
  --warning: #ff8800;
  --error: #cc0000;
  --info: #0066cc;
}

.theme-high-contrast * {
  border-width: 2px !important;
}

.theme-high-contrast .btn {
  border: 2px solid currentColor !important;
  font-weight: var(--font-weight-bold) !important;
}

.theme-high-contrast .card {
  border: 2px solid var(--border-color) !important;
}

.theme-high-contrast .form-input,
.theme-high-contrast .form-select,
.theme-high-contrast .form-textarea {
  border: 2px solid var(--border-color) !important;
}

.theme-high-contrast .form-input:focus,
.theme-high-contrast .form-select:focus,
.theme-high-contrast .form-textarea:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.3) !important;
}

/* ========================================
   ثيم للطباعة - Print Theme
   ======================================== */

@media print {
  .theme-print,
  * {
    --primary-color: #000000;
    --primary-variant: #333333;
    --background: #ffffff;
    --surface: #ffffff;
    --surface-variant: #ffffff;
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-color: #000000;
    --divider: #cccccc;
    
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .app-header,
  .sidebar,
  .status-bar,
  .fab,
  .btn,
  .control-btn {
    display: none !important;
  }
  
  .app-container {
    grid-template-areas: "main";
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }
  
  .main-content {
    grid-area: main;
  }
  
  .card {
    border: 1px solid #000000 !important;
    break-inside: avoid;
    margin-bottom: 1rem;
  }
  
  .data-table {
    border-collapse: collapse;
  }
  
  .data-table th,
  .data-table td {
    border: 1px solid #000000 !important;
    padding: 0.5rem !important;
  }
  
  .data-table th {
    background: #f0f0f0 !important;
    font-weight: bold !important;
  }
}

/* ========================================
   تخصيصات إضافية للثيمات
   ======================================== */

/* تأثيرات الزجاج المصقول */
.glass-effect {
  background: var(--glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تدرجات مخصصة */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-variant));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-600));
}

.gradient-success {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
}

.gradient-warning {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.gradient-error {
  background: linear-gradient(135deg, var(--error-500), var(--error-600));
}

.gradient-info {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

/* تأثيرات النيون */
.neon-glow {
  box-shadow: 
    0 0 5px var(--primary-color),
    0 0 10px var(--primary-color),
    0 0 15px var(--primary-color),
    0 0 20px var(--primary-color);
}

/* تأثيرات الحركة */
.theme-animated * {
  transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-animated .card:hover {
  transform: translateY(-8px) scale(1.02);
}

.theme-animated .btn:hover {
  transform: translateY(-2px) scale(1.05);
}

.theme-animated .nav-link:hover {
  transform: translateX(-4px) scale(1.02);
}

[data-lang="en"] .theme-animated .nav-link:hover {
  transform: translateX(4px) scale(1.02);
}
