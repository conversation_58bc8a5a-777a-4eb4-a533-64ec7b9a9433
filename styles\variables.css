/* ========================================
   نظام إدارة معمل الأسنان المتقدم v2.0
   ملف المتغيرات العامة - Variables
   ======================================== */

:root {
  /* ========================================
     الألوان الأساسية - Primary Colors
     ======================================== */
  
  /* الألوان الرئيسية */
  --primary-50: #e3f2fd;
  --primary-100: #bbdefb;
  --primary-200: #90caf9;
  --primary-300: #64b5f6;
  --primary-400: #42a5f5;
  --primary-500: #2196f3;
  --primary-600: #1e88e5;
  --primary-700: #1976d2;
  --primary-800: #1565c0;
  --primary-900: #0d47a1;
  
  /* الألوان الثانوية */
  --secondary-50: #f3e5f5;
  --secondary-100: #e1bee7;
  --secondary-200: #ce93d8;
  --secondary-300: #ba68c8;
  --secondary-400: #ab47bc;
  --secondary-500: #9c27b0;
  --secondary-600: #8e24aa;
  --secondary-700: #7b1fa2;
  --secondary-800: #6a1b9a;
  --secondary-900: #4a148c;
  
  /* ألوان الحالة */
  --success-50: #e8f5e8;
  --success-100: #c8e6c9;
  --success-200: #a5d6a7;
  --success-300: #81c784;
  --success-400: #66bb6a;
  --success-500: #4caf50;
  --success-600: #43a047;
  --success-700: #388e3c;
  --success-800: #2e7d32;
  --success-900: #1b5e20;
  
  --warning-50: #fff8e1;
  --warning-100: #ffecb3;
  --warning-200: #ffe082;
  --warning-300: #ffd54f;
  --warning-400: #ffca28;
  --warning-500: #ffc107;
  --warning-600: #ffb300;
  --warning-700: #ffa000;
  --warning-800: #ff8f00;
  --warning-900: #ff6f00;
  
  --error-50: #ffebee;
  --error-100: #ffcdd2;
  --error-200: #ef9a9a;
  --error-300: #e57373;
  --error-400: #ef5350;
  --error-500: #f44336;
  --error-600: #e53935;
  --error-700: #d32f2f;
  --error-800: #c62828;
  --error-900: #b71c1c;
  
  --info-50: #e1f5fe;
  --info-100: #b3e5fc;
  --info-200: #81d4fa;
  --info-300: #4fc3f7;
  --info-400: #29b6f6;
  --info-500: #03a9f4;
  --info-600: #039be5;
  --info-700: #0288d1;
  --info-800: #0277bd;
  --info-900: #01579b;
  
  /* ========================================
     الألوان المحايدة - Neutral Colors
     ======================================== */
  
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  
  /* ========================================
     الخطوط - Typography
     ======================================== */
  
  /* عائلات الخطوط */
  --font-family-arabic: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-english: 'Roboto', 'Arial', 'Helvetica', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;
  
  /* أحجام الخطوط */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  
  /* أوزان الخطوط */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* ارتفاع الأسطر */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* ========================================
     المسافات - Spacing
     ======================================== */
  
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;  /* 2px */
  --spacing-1: 0.25rem;     /* 4px */
  --spacing-1-5: 0.375rem;  /* 6px */
  --spacing-2: 0.5rem;      /* 8px */
  --spacing-2-5: 0.625rem;  /* 10px */
  --spacing-3: 0.75rem;     /* 12px */
  --spacing-3-5: 0.875rem;  /* 14px */
  --spacing-4: 1rem;        /* 16px */
  --spacing-5: 1.25rem;     /* 20px */
  --spacing-6: 1.5rem;      /* 24px */
  --spacing-7: 1.75rem;     /* 28px */
  --spacing-8: 2rem;        /* 32px */
  --spacing-9: 2.25rem;     /* 36px */
  --spacing-10: 2.5rem;     /* 40px */
  --spacing-11: 2.75rem;    /* 44px */
  --spacing-12: 3rem;       /* 48px */
  --spacing-14: 3.5rem;     /* 56px */
  --spacing-16: 4rem;       /* 64px */
  --spacing-20: 5rem;       /* 80px */
  --spacing-24: 6rem;       /* 96px */
  --spacing-28: 7rem;       /* 112px */
  --spacing-32: 8rem;       /* 128px */
  
  /* ========================================
     الحدود والزوايا - Borders & Radius
     ======================================== */
  
  /* سماكة الحدود */
  --border-width-0: 0;
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;
  
  /* نصف القطر */
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;
  
  /* ========================================
     الظلال - Shadows
     ======================================== */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* ========================================
     الانتقالات - Transitions
     ======================================== */
  
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* ========================================
     نقاط الكسر - Breakpoints
     ======================================== */
  
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* ========================================
     Z-Index
     ======================================== */
  
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ========================================
   الوضع الليلي - Dark Mode Variables
   ======================================== */

[data-theme="dark"] {
  /* إعادة تعريف الألوان للوضع الليلي */
  --primary-color: var(--primary-400);
  --primary-variant: var(--primary-300);
  
  --surface: var(--gray-800);
  --surface-variant: var(--gray-700);
  --background: var(--gray-900);
  
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-disabled: var(--gray-500);
  
  --border-color: var(--gray-600);
  --divider: var(--gray-700);
}

/* ========================================
   الوضع النهاري - Light Mode Variables
   ======================================== */

[data-theme="light"] {
  /* الألوان للوضع النهاري */
  --primary-color: var(--primary-600);
  --primary-variant: var(--primary-700);
  
  --surface: #ffffff;
  --surface-variant: var(--gray-50);
  --background: var(--gray-100);
  
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-disabled: var(--gray-400);
  
  --border-color: var(--gray-300);
  --divider: var(--gray-200);
}

/* ========================================
   متغيرات خاصة بالتطبيق
   ======================================== */

:root {
  /* ألوان خاصة بمعمل الأسنان */
  --dental-blue: #1976d2;
  --dental-teal: #00acc1;
  --dental-green: #43a047;
  --dental-orange: #fb8c00;
  
  /* أبعاد الشريط الجانبي */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
  
  /* ارتفاع الشريط العلوي */
  --header-height: 64px;
  
  /* ارتفاع شريط الحالة */
  --status-bar-height: 32px;
}
