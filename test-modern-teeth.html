<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم الأسنان العصري</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- ملفات التصميم -->
    <link rel="stylesheet" href="styles/variables.css">
    <link rel="stylesheet" href="styles/modern-teeth.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .test-header p {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .control-btn.secondary {
            background: #f1f5f9;
            color: #374151;
            border: 2px solid #e2e8f0;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px 25px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            border: 2px solid #0ea5e9;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #0c4a6e;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #0369a1;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🦷 تصميم الأسنان الموحد مع اللوجو</h1>
            <p>اختبار واجهة اختيار الأسنان الجديدة مع اللوجو المخصص والفواصل البصرية</p>
        </div>
        
        <div class="controls">
            <button class="control-btn primary" onclick="selectAllTeeth()">
                <i class="fas fa-check-double"></i>
                تحديد الكل
            </button>
            <button class="control-btn secondary" onclick="clearAllTeeth()">
                <i class="fas fa-times"></i>
                إلغاء التحديد
            </button>
            <button class="control-btn secondary" onclick="selectUpperJaw()">
                <i class="fas fa-arrow-up"></i>
                الفك العلوي
            </button>
            <button class="control-btn secondary" onclick="selectLowerJaw()">
                <i class="fas fa-arrow-down"></i>
                الفك السفلي
            </button>
            <button class="control-btn secondary" onclick="randomSelection()">
                <i class="fas fa-random"></i>
                اختيار عشوائي
            </button>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="selected-count">0</div>
                <div class="stat-label">أسنان مختارة</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="total-teeth">32</div>
                <div class="stat-label">إجمالي الأسنان</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="selection-percentage">0%</div>
                <div class="stat-label">نسبة الاختيار</div>
            </div>
        </div>
        
        <!-- واجهة اختيار الأسنان -->
        <div class="teeth-selector">
            <div class="teeth-diagram">
                <!-- الفك العلوي -->
                <div class="jaw upper-jaw">
                    <h4 class="jaw-title">الفك العلوي (UPPER JAW)</h4>

                    <!-- تسميات الجوانب للفك العلوي -->
                    <div class="jaw-sides-labels">
                        <span class="side-label right-side">الجانب الأيمن</span>
                        <span class="side-label left-side">الجانب الأيسر</span>
                    </div>

                    <div class="teeth-row upper-teeth" id="upper-teeth">
                        <!-- سيتم إنشاؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- الفك السفلي -->
                <div class="jaw lower-jaw">
                    <h4 class="jaw-title">الفك السفلي (LOWER JAW)</h4>

                    <!-- تسميات الجوانب للفك السفلي -->
                    <div class="jaw-sides-labels">
                        <span class="side-label right-side">الجانب الأيمن</span>
                        <span class="side-label left-side">الجانب الأيسر</span>
                    </div>

                    <div class="teeth-row lower-teeth" id="lower-teeth">
                        <!-- سيتم إنشاؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الأسنان مع الترقيم الصحيح والأشكال
        const teethData = {
            // الفك العلوي - الجانب الأيمن (8-1)
            upper_right: [
                { number: 8, type: 'molar', name: 'ضرس العقل' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 1, type: 'incisor', name: 'القاطع المركزي' }
            ],
            // الفك العلوي - الجانب الأيسر (1-8)
            upper_left: [
                { number: 1, type: 'incisor', name: 'القاطع المركزي' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 8, type: 'molar', name: 'ضرس العقل' }
            ],
            // الفك السفلي - الجانب الأيمن (8-1)
            lower_right: [
                { number: 8, type: 'molar', name: 'ضرس العقل' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 1, type: 'incisor', name: 'القاطع المركزي' }
            ],
            // الفك السفلي - الجانب الأيسر (1-8)
            lower_left: [
                { number: 1, type: 'incisor', name: 'القاطع المركزي' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 8, type: 'molar', name: 'ضرس العقل' }
            ]
        };
        
        let selectedTeeth = [];
        
        // إنشاء الأسنان
        function createTeeth() {
            const upperContainer = document.getElementById('upper-teeth');
            const lowerContainer = document.getElementById('lower-teeth');

            // الأسنان العلوية - الجانب الأيمن (8-1)
            teethData.upper_right.forEach(toothData => {
                const tooth = createToothElement(toothData, 'upper_right');
                upperContainer.appendChild(tooth);
            });

            // الأسنان العلوية - الجانب الأيسر (1-8)
            teethData.upper_left.forEach(toothData => {
                const tooth = createToothElement(toothData, 'upper_left');
                upperContainer.appendChild(tooth);
            });

            // الأسنان السفلية - الجانب الأيمن (8-1)
            teethData.lower_right.forEach(toothData => {
                const tooth = createToothElement(toothData, 'lower_right');
                lowerContainer.appendChild(tooth);
            });

            // الأسنان السفلية - الجانب الأيسر (1-8)
            teethData.lower_left.forEach(toothData => {
                const tooth = createToothElement(toothData, 'lower_left');
                lowerContainer.appendChild(tooth);
            });
        }
        
        function createToothElement(toothData, section) {
            const tooth = document.createElement('div');
            const uniqueId = `${section}_${toothData.number}`;

            tooth.className = 'tooth';
            tooth.dataset.tooth = uniqueId;
            tooth.dataset.number = toothData.number;
            tooth.dataset.section = section;
            tooth.title = toothData.name;

            // إضافة شكل السن حسب النوع
            const toothIcon = getToothIcon(toothData.type);

            tooth.innerHTML = `
                <div class="tooth-visual ${toothData.type}">
                    <div class="tooth-shape">${toothIcon}</div>
                    <span class="tooth-number">${toothData.number}</span>
                </div>
                <div class="tooth-label">${toothData.number}</div>
            `;

            tooth.addEventListener('click', () => toggleTooth(uniqueId));

            return tooth;
        }

        function getToothIcon(type) {
            // استخدام اللوجو الطبي الموحد لجميع الأسنان
            return `<svg width="24" height="24" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <!-- الأشعة الذهبية -->
              <g stroke="#F59E0B" stroke-width="1.5" stroke-linecap="round" opacity="0.8">
                <line x1="50" y1="8" x2="50" y2="15" />
                <line x1="58" y1="9" x2="57" y2="16" />
                <line x1="66" y1="12" x2="64" y2="18" />
                <line x1="73" y1="17" x2="70" y2="22" />
                <line x1="79" y1="24" x2="75" y2="28" />
                <line x1="84" y1="32" x2="79" y2="35" />
                <line x1="42" y1="9" x2="43" y2="16" />
                <line x1="34" y1="12" x2="36" y2="18" />
                <line x1="27" y1="17" x2="30" y2="22" />
                <line x1="21" y1="24" x2="25" y2="28" />
                <line x1="16" y1="32" x2="21" y2="35" />
              </g>

              <!-- الصليب الطبي -->
              <g fill="#2563EB">
                <rect x="75" y="25" width="6" height="15" rx="1" />
                <rect x="72" y="28" width="12" height="6" rx="1" />
              </g>

              <!-- السن الرئيسي -->
              <path d="M28 38 C28 28, 35 23, 50 23 C65 23, 72 28, 72 38 L72 55 C72 63, 68 70, 63 74 C58 78, 54 79, 50 77 C46 79, 42 78, 37 74 C32 70, 28 63, 28 55 Z"
                    fill="white"
                    stroke="#2563EB"
                    stroke-width="2"/>

              <!-- التفاصيل الداخلية -->
              <path d="M33 42 C33 35, 38 31, 50 31 C62 31, 67 35, 67 42 L67 50 C67 55, 63 60, 58 62 C55 64, 52 64, 50 62 C48 64, 45 64, 42 62 C37 60, 33 55, 33 50 Z"
                    fill="#F0F9FF"
                    opacity="0.7"/>

              <!-- الجذور -->
              <ellipse cx="43" cy="70" rx="3" ry="8" fill="#2563EB" opacity="0.7"/>
              <ellipse cx="57" cy="70" rx="3" ry="8" fill="#2563EB" opacity="0.7"/>

              <!-- اللمعة -->
              <ellipse cx="45" cy="38" rx="4" ry="6" fill="white" opacity="0.5"/>
            </svg>`;
        }
        
        function toggleTooth(toothId) {
            const tooth = document.querySelector(`[data-tooth="${toothId}"]`);
            const index = selectedTeeth.indexOf(toothId);

            if (index > -1) {
                selectedTeeth.splice(index, 1);
                tooth.classList.remove('selected');
            } else {
                selectedTeeth.push(toothId);
                tooth.classList.add('selected');
            }

            updateStats();
        }

        function selectAllTeeth() {
            selectedTeeth = [];
            document.querySelectorAll('.tooth').forEach(tooth => {
                selectedTeeth.push(tooth.dataset.tooth);
            });
            updateTeethVisual();
            updateStats();
        }

        function clearAllTeeth() {
            selectedTeeth = [];
            updateTeethVisual();
            updateStats();
        }

        function selectUpperJaw() {
            selectedTeeth = [];
            document.querySelectorAll('.upper-teeth .tooth').forEach(tooth => {
                selectedTeeth.push(tooth.dataset.tooth);
            });
            updateTeethVisual();
            updateStats();
        }

        function selectLowerJaw() {
            selectedTeeth = [];
            document.querySelectorAll('.lower-teeth .tooth').forEach(tooth => {
                selectedTeeth.push(tooth.dataset.tooth);
            });
            updateTeethVisual();
            updateStats();
        }
        
        function randomSelection() {
            selectedTeeth = [];
            const allTeeth = Array.from(document.querySelectorAll('.tooth'));
            const count = Math.floor(Math.random() * 15) + 5; // 5-20 أسنان

            while (selectedTeeth.length < count) {
                const randomTooth = allTeeth[Math.floor(Math.random() * allTeeth.length)];
                const toothId = randomTooth.dataset.tooth;
                if (!selectedTeeth.includes(toothId)) {
                    selectedTeeth.push(toothId);
                }
            }

            updateTeethVisual();
            updateStats();
        }

        function updateTeethVisual() {
            document.querySelectorAll('.tooth').forEach(tooth => {
                const toothId = tooth.dataset.tooth;
                if (selectedTeeth.includes(toothId)) {
                    tooth.classList.add('selected');
                } else {
                    tooth.classList.remove('selected');
                }
            });
        }

        function updateStats() {
            const count = selectedTeeth.length;
            const totalTeeth = document.querySelectorAll('.tooth').length;
            const percentage = Math.round((count / totalTeeth) * 100);

            document.getElementById('selected-count').textContent = count;
            document.getElementById('total-teeth').textContent = totalTeeth;
            document.getElementById('selection-percentage').textContent = percentage + '%';
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            createTeeth();
            updateStats();
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            switch(e.key.toLowerCase()) {
                case 'a':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        selectAllTeeth();
                    }
                    break;
                case 'escape':
                    clearAllTeeth();
                    break;
                case 'u':
                    selectUpperJaw();
                    break;
                case 'l':
                    selectLowerJaw();
                    break;
                case 'r':
                    randomSelection();
                    break;
            }
        });
    </script>
</body>
</html>
