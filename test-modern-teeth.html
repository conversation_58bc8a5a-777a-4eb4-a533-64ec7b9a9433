<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم الأسنان العصري</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- ملفات التصميم -->
    <link rel="stylesheet" href="styles/variables.css">
    <link rel="stylesheet" href="styles/modern-teeth.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .test-header p {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .control-btn.secondary {
            background: #f1f5f9;
            color: #374151;
            border: 2px solid #e2e8f0;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px 25px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            border: 2px solid #0ea5e9;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #0c4a6e;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #0369a1;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🦷 تصميم الأسنان الموحد مع اللوجو</h1>
            <p>اختبار واجهة اختيار الأسنان الجديدة مع اللوجو المخصص والفواصل البصرية</p>
        </div>
        
        <div class="controls">
            <button class="control-btn primary" onclick="selectAllTeeth()">
                <i class="fas fa-check-double"></i>
                تحديد الكل
            </button>
            <button class="control-btn secondary" onclick="clearAllTeeth()">
                <i class="fas fa-times"></i>
                إلغاء التحديد
            </button>
            <button class="control-btn secondary" onclick="selectUpperJaw()">
                <i class="fas fa-arrow-up"></i>
                الفك العلوي
            </button>
            <button class="control-btn secondary" onclick="selectLowerJaw()">
                <i class="fas fa-arrow-down"></i>
                الفك السفلي
            </button>
            <button class="control-btn secondary" onclick="randomSelection()">
                <i class="fas fa-random"></i>
                اختيار عشوائي
            </button>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="selected-count">0</div>
                <div class="stat-label">أسنان مختارة</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">32</div>
                <div class="stat-label">إجمالي الأسنان</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="selection-percentage">0%</div>
                <div class="stat-label">نسبة الاختيار</div>
            </div>
        </div>
        
        <!-- واجهة اختيار الأسنان -->
        <div class="teeth-selector">
            <div class="teeth-diagram">
                <!-- الفك العلوي -->
                <div class="jaw upper-jaw">
                    <h4 class="jaw-title">الفك العلوي (1-16)</h4>
                    <div class="teeth-row upper-teeth" id="upper-teeth">
                        <!-- سيتم إنشاؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- الفك السفلي -->
                <div class="jaw lower-jaw">
                    <h4 class="jaw-title">الفك السفلي (17-32)</h4>
                    <div class="teeth-row lower-teeth" id="lower-teeth">
                        <!-- سيتم إنشاؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الأسنان - جميعها بنفس الشكل
        const teethData = {
            1: { type: 'tooth', name: 'السن رقم 1 - الأيمن العلوي' },
            2: { type: 'tooth', name: 'السن رقم 2 - الأيمن العلوي' },
            3: { type: 'tooth', name: 'السن رقم 3 - الأيمن العلوي' },
            4: { type: 'tooth', name: 'السن رقم 4 - الأيمن العلوي' },
            5: { type: 'tooth', name: 'السن رقم 5 - الأيمن العلوي' },
            6: { type: 'tooth', name: 'السن رقم 6 - الأيمن العلوي' },
            7: { type: 'tooth', name: 'السن رقم 7 - الأيمن العلوي' },
            8: { type: 'tooth', name: 'السن رقم 8 - الأيمن العلوي' },
            9: { type: 'tooth', name: 'السن رقم 9 - الأيسر العلوي' },
            10: { type: 'tooth', name: 'السن رقم 10 - الأيسر العلوي' },
            11: { type: 'tooth', name: 'السن رقم 11 - الأيسر العلوي' },
            12: { type: 'tooth', name: 'السن رقم 12 - الأيسر العلوي' },
            13: { type: 'tooth', name: 'السن رقم 13 - الأيسر العلوي' },
            14: { type: 'tooth', name: 'السن رقم 14 - الأيسر العلوي' },
            15: { type: 'tooth', name: 'السن رقم 15 - الأيسر العلوي' },
            16: { type: 'tooth', name: 'السن رقم 16 - الأيسر العلوي' }
        };

        // إضافة الأسنان السفلية
        for (let i = 17; i <= 32; i++) {
            const position = i <= 24 ? 'الأيمن' : 'الأيسر';
            teethData[i] = {
                type: 'tooth',
                name: `السن رقم ${i} - ${position} السفلي`
            };
        }
        
        let selectedTeeth = [];
        
        // إنشاء الأسنان
        function createTeeth() {
            const upperContainer = document.getElementById('upper-teeth');
            const lowerContainer = document.getElementById('lower-teeth');
            
            // الأسنان العلوية (1-16)
            for (let i = 1; i <= 16; i++) {
                const tooth = createToothElement(i);
                upperContainer.appendChild(tooth);
            }
            
            // الأسنان السفلية (17-32)
            for (let i = 17; i <= 32; i++) {
                const tooth = createToothElement(i);
                lowerContainer.appendChild(tooth);
            }
        }
        
        function createToothElement(toothNumber) {
            const toothData = teethData[toothNumber];
            const tooth = document.createElement('div');
            tooth.className = 'tooth';
            tooth.dataset.tooth = toothNumber;
            tooth.title = toothData.name;
            
            tooth.innerHTML = `
                <div class="tooth-visual ${toothData.type}">
                    <span class="tooth-number">${toothNumber}</span>
                </div>
                <div class="tooth-label">${toothNumber}</div>
            `;
            
            tooth.addEventListener('click', () => toggleTooth(toothNumber));
            
            return tooth;
        }
        
        function toggleTooth(toothNumber) {
            const tooth = document.querySelector(`[data-tooth="${toothNumber}"]`);
            const index = selectedTeeth.indexOf(toothNumber);
            
            if (index > -1) {
                selectedTeeth.splice(index, 1);
                tooth.classList.remove('selected');
            } else {
                selectedTeeth.push(toothNumber);
                tooth.classList.add('selected');
            }
            
            updateStats();
        }
        
        function selectAllTeeth() {
            selectedTeeth = Array.from({length: 32}, (_, i) => i + 1);
            updateTeethVisual();
            updateStats();
        }
        
        function clearAllTeeth() {
            selectedTeeth = [];
            updateTeethVisual();
            updateStats();
        }
        
        function selectUpperJaw() {
            selectedTeeth = Array.from({length: 16}, (_, i) => i + 1);
            updateTeethVisual();
            updateStats();
        }
        
        function selectLowerJaw() {
            selectedTeeth = Array.from({length: 16}, (_, i) => i + 17);
            updateTeethVisual();
            updateStats();
        }
        
        function randomSelection() {
            selectedTeeth = [];
            const count = Math.floor(Math.random() * 15) + 5; // 5-20 أسنان
            
            while (selectedTeeth.length < count) {
                const tooth = Math.floor(Math.random() * 32) + 1;
                if (!selectedTeeth.includes(tooth)) {
                    selectedTeeth.push(tooth);
                }
            }
            
            updateTeethVisual();
            updateStats();
        }
        
        function updateTeethVisual() {
            document.querySelectorAll('.tooth').forEach(tooth => {
                const toothNumber = parseInt(tooth.dataset.tooth);
                if (selectedTeeth.includes(toothNumber)) {
                    tooth.classList.add('selected');
                } else {
                    tooth.classList.remove('selected');
                }
            });
        }
        
        function updateStats() {
            const count = selectedTeeth.length;
            const percentage = Math.round((count / 32) * 100);
            
            document.getElementById('selected-count').textContent = count;
            document.getElementById('selection-percentage').textContent = percentage + '%';
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            createTeeth();
            updateStats();
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            switch(e.key.toLowerCase()) {
                case 'a':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        selectAllTeeth();
                    }
                    break;
                case 'escape':
                    clearAllTeeth();
                    break;
                case 'u':
                    selectUpperJaw();
                    break;
                case 'l':
                    selectLowerJaw();
                    break;
                case 'r':
                    randomSelection();
                    break;
            }
        });
    </script>
</body>
</html>
