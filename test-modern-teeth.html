<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم الأسنان العصري</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- ملفات التصميم -->
    <link rel="stylesheet" href="styles/variables.css">
    <link rel="stylesheet" href="styles/modern-teeth.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .test-header p {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .control-btn.secondary {
            background: #f1f5f9;
            color: #374151;
            border: 2px solid #e2e8f0;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px 25px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            border: 2px solid #0ea5e9;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #0c4a6e;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #0369a1;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🦷 تصميم الأسنان الموحد مع اللوجو</h1>
            <p>اختبار واجهة اختيار الأسنان الجديدة مع اللوجو المخصص والفواصل البصرية</p>
        </div>
        
        <div class="controls">
            <button class="control-btn primary" onclick="selectAllTeeth()">
                <i class="fas fa-check-double"></i>
                تحديد الكل
            </button>
            <button class="control-btn secondary" onclick="clearAllTeeth()">
                <i class="fas fa-times"></i>
                إلغاء التحديد
            </button>
            <button class="control-btn secondary" onclick="selectUpperJaw()">
                <i class="fas fa-arrow-up"></i>
                الفك العلوي
            </button>
            <button class="control-btn secondary" onclick="selectLowerJaw()">
                <i class="fas fa-arrow-down"></i>
                الفك السفلي
            </button>
            <button class="control-btn secondary" onclick="randomSelection()">
                <i class="fas fa-random"></i>
                اختيار عشوائي
            </button>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="selected-count">0</div>
                <div class="stat-label">أسنان مختارة</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="total-teeth">32</div>
                <div class="stat-label">إجمالي الأسنان</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="selection-percentage">0%</div>
                <div class="stat-label">نسبة الاختيار</div>
            </div>
        </div>
        
        <!-- واجهة اختيار الأسنان -->
        <div class="teeth-selector">
            <div class="teeth-diagram">
                <!-- الفك العلوي -->
                <div class="jaw upper-jaw">
                    <h4 class="jaw-title">الفك العلوي (UPPER JAW)</h4>

                    <!-- تسميات الجوانب للفك العلوي -->
                    <div class="jaw-sides-labels">
                        <span class="side-label right-side">الجانب الأيمن</span>
                        <span class="side-label left-side">الجانب الأيسر</span>
                    </div>

                    <div class="teeth-row upper-teeth" id="upper-teeth">
                        <!-- سيتم إنشاؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- الفك السفلي -->
                <div class="jaw lower-jaw">
                    <h4 class="jaw-title">الفك السفلي (LOWER JAW)</h4>

                    <!-- تسميات الجوانب للفك السفلي -->
                    <div class="jaw-sides-labels">
                        <span class="side-label right-side">الجانب الأيمن</span>
                        <span class="side-label left-side">الجانب الأيسر</span>
                    </div>

                    <div class="teeth-row lower-teeth" id="lower-teeth">
                        <!-- سيتم إنشاؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الأسنان مع الترقيم الصحيح والأشكال
        const teethData = {
            // الفك العلوي - الجانب الأيمن (8-1)
            upper_right: [
                { number: 8, type: 'molar', name: 'ضرس العقل' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 1, type: 'incisor', name: 'القاطع المركزي' }
            ],
            // الفك العلوي - الجانب الأيسر (1-8)
            upper_left: [
                { number: 1, type: 'incisor', name: 'القاطع المركزي' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 8, type: 'molar', name: 'ضرس العقل' }
            ],
            // الفك السفلي - الجانب الأيمن (8-1)
            lower_right: [
                { number: 8, type: 'molar', name: 'ضرس العقل' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 1, type: 'incisor', name: 'القاطع المركزي' }
            ],
            // الفك السفلي - الجانب الأيسر (1-8)
            lower_left: [
                { number: 1, type: 'incisor', name: 'القاطع المركزي' },
                { number: 2, type: 'incisor', name: 'القاطع الجانبي' },
                { number: 3, type: 'canine', name: 'الناب' },
                { number: 4, type: 'premolar', name: 'الضاحك الأول' },
                { number: 5, type: 'premolar', name: 'الضاحك الثاني' },
                { number: 6, type: 'molar', name: 'الضرس الأول' },
                { number: 7, type: 'molar', name: 'الضرس الثاني' },
                { number: 8, type: 'molar', name: 'ضرس العقل' }
            ]
        };
        
        let selectedTeeth = [];
        
        // إنشاء الأسنان
        function createTeeth() {
            const upperContainer = document.getElementById('upper-teeth');
            const lowerContainer = document.getElementById('lower-teeth');

            // الأسنان العلوية - الجانب الأيمن (8-1)
            teethData.upper_right.forEach(toothData => {
                const tooth = createToothElement(toothData, 'upper_right');
                upperContainer.appendChild(tooth);
            });

            // الأسنان العلوية - الجانب الأيسر (1-8)
            teethData.upper_left.forEach(toothData => {
                const tooth = createToothElement(toothData, 'upper_left');
                upperContainer.appendChild(tooth);
            });

            // الأسنان السفلية - الجانب الأيمن (8-1)
            teethData.lower_right.forEach(toothData => {
                const tooth = createToothElement(toothData, 'lower_right');
                lowerContainer.appendChild(tooth);
            });

            // الأسنان السفلية - الجانب الأيسر (1-8)
            teethData.lower_left.forEach(toothData => {
                const tooth = createToothElement(toothData, 'lower_left');
                lowerContainer.appendChild(tooth);
            });
        }
        
        function createToothElement(toothData, section) {
            const tooth = document.createElement('div');
            const uniqueId = `${section}_${toothData.number}`;

            tooth.className = 'tooth';
            tooth.dataset.tooth = uniqueId;
            tooth.dataset.number = toothData.number;
            tooth.dataset.section = section;
            tooth.title = toothData.name;

            // إضافة شكل السن حسب النوع
            const toothIcon = getToothIcon(toothData.type);

            tooth.innerHTML = `
                <div class="tooth-visual ${toothData.type}">
                    <div class="tooth-shape">${toothIcon}</div>
                    <span class="tooth-number">${toothData.number}</span>
                </div>
                <div class="tooth-label">${toothData.number}</div>
            `;

            tooth.addEventListener('click', () => toggleTooth(uniqueId));

            return tooth;
        }

        function getToothIcon(type) {
            const icons = {
                'incisor': `<svg width="20" height="24" viewBox="0 0 20 24" fill="none">
                    <path d="M10 2C7 2 5 4 5 7V17C5 20 7 22 10 22C13 22 15 20 15 17V7C15 4 13 2 10 2Z"
                          fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1.5"/>
                </svg>`,
                'canine': `<svg width="20" height="26" viewBox="0 0 20 26" fill="none">
                    <path d="M10 2C7 2 4 4 4 7V18C4 21 7 24 10 24C13 24 16 21 16 18V7C16 4 13 2 10 2Z"
                          fill="#fef3c7" stroke="#f59e0b" stroke-width="1.5"/>
                </svg>`,
                'premolar': `<svg width="22" height="24" viewBox="0 0 22 24" fill="none">
                    <path d="M11 2C8 2 6 4 6 7V17C6 20 8 22 11 22C14 22 16 20 16 17V7C16 4 14 2 11 2Z"
                          fill="#f0fdf4" stroke="#22c55e" stroke-width="1.5"/>
                </svg>`,
                'molar': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C8 2 5 4 5 7V17C5 20 8 22 12 22C16 22 19 20 19 17V7C19 4 16 2 12 2Z"
                          fill="#fdf2f8" stroke="#ec4899" stroke-width="1.5"/>
                </svg>`
            };
            return icons[type] || icons['incisor'];
        }
        
        function toggleTooth(toothId) {
            const tooth = document.querySelector(`[data-tooth="${toothId}"]`);
            const index = selectedTeeth.indexOf(toothId);

            if (index > -1) {
                selectedTeeth.splice(index, 1);
                tooth.classList.remove('selected');
            } else {
                selectedTeeth.push(toothId);
                tooth.classList.add('selected');
            }

            updateStats();
        }

        function selectAllTeeth() {
            selectedTeeth = [];
            document.querySelectorAll('.tooth').forEach(tooth => {
                selectedTeeth.push(tooth.dataset.tooth);
            });
            updateTeethVisual();
            updateStats();
        }

        function clearAllTeeth() {
            selectedTeeth = [];
            updateTeethVisual();
            updateStats();
        }

        function selectUpperJaw() {
            selectedTeeth = [];
            document.querySelectorAll('.upper-teeth .tooth').forEach(tooth => {
                selectedTeeth.push(tooth.dataset.tooth);
            });
            updateTeethVisual();
            updateStats();
        }

        function selectLowerJaw() {
            selectedTeeth = [];
            document.querySelectorAll('.lower-teeth .tooth').forEach(tooth => {
                selectedTeeth.push(tooth.dataset.tooth);
            });
            updateTeethVisual();
            updateStats();
        }
        
        function randomSelection() {
            selectedTeeth = [];
            const allTeeth = Array.from(document.querySelectorAll('.tooth'));
            const count = Math.floor(Math.random() * 15) + 5; // 5-20 أسنان

            while (selectedTeeth.length < count) {
                const randomTooth = allTeeth[Math.floor(Math.random() * allTeeth.length)];
                const toothId = randomTooth.dataset.tooth;
                if (!selectedTeeth.includes(toothId)) {
                    selectedTeeth.push(toothId);
                }
            }

            updateTeethVisual();
            updateStats();
        }

        function updateTeethVisual() {
            document.querySelectorAll('.tooth').forEach(tooth => {
                const toothId = tooth.dataset.tooth;
                if (selectedTeeth.includes(toothId)) {
                    tooth.classList.add('selected');
                } else {
                    tooth.classList.remove('selected');
                }
            });
        }

        function updateStats() {
            const count = selectedTeeth.length;
            const totalTeeth = document.querySelectorAll('.tooth').length;
            const percentage = Math.round((count / totalTeeth) * 100);

            document.getElementById('selected-count').textContent = count;
            document.getElementById('total-teeth').textContent = totalTeeth;
            document.getElementById('selection-percentage').textContent = percentage + '%';
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            createTeeth();
            updateStats();
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            switch(e.key.toLowerCase()) {
                case 'a':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        selectAllTeeth();
                    }
                    break;
                case 'escape':
                    clearAllTeeth();
                    break;
                case 'u':
                    selectUpperJaw();
                    break;
                case 'l':
                    selectLowerJaw();
                    break;
                case 'r':
                    randomSelection();
                    break;
            }
        });
    </script>
</body>
</html>
