// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// اختبارات نظام التركيبات المتقدم - Advanced Prosthetics Tests
// ========================================

console.log('🧪 بدء اختبارات نظام التركيبات المتقدم...');

// ========================================
// إعداد البيئة للاختبار - Test Environment Setup
// ========================================

// محاكاة قاعدة البيانات
const mockAdvancedDatabase = {
    prosthetics: [
        {
            id: 1,
            case_number: '202412-001',
            patient_name: 'أحمد محمد السعيد',
            patient_phone: '**********',
            doctor_id: 1,
            prosthetic_category: 'porcelain',
            prosthetic_type: 'بورسلين فيتا',
            material: 'بورسلين فيتا',
            selected_teeth: JSON.stringify([11, 12, 13]),
            teeth_count: 3,
            unit_price: 350,
            total_price: 1050,
            discount_percentage: 10,
            discount_amount: 105,
            final_price: 945,
            expected_delivery: '2024-12-20',
            status: 'pending',
            priority: 'normal',
            createdAt: '2024-12-13T10:00:00Z'
        },
        {
            id: 2,
            case_number: '202412-002',
            patient_name: 'فاطمة عبدالله الزهراني',
            patient_phone: '**********',
            doctor_id: 2,
            prosthetic_category: 'dentures',
            prosthetic_type: 'برشل جزئى',
            material: 'برشل جزئى',
            selected_teeth: JSON.stringify([14, 15, 16, 17]),
            teeth_count: 4,
            unit_price: 0, // سيتم حسابه بالطريقة الخاصة
            total_price: 420, // 150 + (3 × 90)
            discount_percentage: 0,
            discount_amount: 0,
            final_price: 420,
            special_calculation: JSON.stringify({
                type: 'partial_denture',
                first_tooth_price: 150,
                additional_tooth_price: 90,
                calculation: '150 + (3 × 90) = 420'
            }),
            expected_delivery: '2024-12-25',
            status: 'in_progress',
            priority: 'high',
            createdAt: '2024-12-13T14:30:00Z'
        }
    ],
    doctors: [
        {
            id: 1,
            name: 'د. أحمد محمد السعيد',
            specialty: 'طب الأسنان العام',
            phone: '**********',
            isActive: true
        },
        {
            id: 2,
            name: 'د. فاطمة عبدالله الزهراني',
            specialty: 'تقويم الأسنان',
            phone: '**********',
            isActive: true
        }
    ],
    prosthetic_types: [
        {
            id: 1,
            category: 'porcelain',
            type_name: 'فيتا',
            material: 'Vita Porcelain',
            unit_price: 350,
            is_per_tooth: true,
            special_calculation: false
        },
        {
            id: 2,
            category: 'dentures',
            type_name: 'برشل جزئي',
            material: 'Metal Framework',
            unit_price: 150,
            is_per_tooth: true,
            special_calculation: true
        }
    ],
    system_settings: [
        {
            id: 1,
            setting_key: 'next_case_number',
            setting_value: '3',
            setting_type: 'number'
        },
        {
            id: 2,
            setting_key: 'case_number_format',
            setting_value: 'YYYYMM-###',
            setting_type: 'string'
        },
        {
            id: 3,
            setting_key: 'partial_denture_first_tooth_price',
            setting_value: '150',
            setting_type: 'number'
        },
        {
            id: 4,
            setting_key: 'partial_denture_additional_tooth_price',
            setting_value: '90',
            setting_type: 'number'
        }
    ]
};

// محاكاة قاعدة البيانات
const mockAdvancedDB = {
    async findAll(table, options = {}) {
        let data = mockAdvancedDatabase[table] || [];

        // تطبيق التصفية
        if (options.where) {
            data = data.filter(item => {
                for (const [key, value] of Object.entries(options.where)) {
                    if (typeof value === 'object' && value.like) {
                        const searchTerm = value.like.replace(/%/g, '');
                        if (!item[key] || !item[key].toString().includes(searchTerm)) {
                            return false;
                        }
                    } else if (item[key] !== value) {
                        return false;
                    }
                }
                return true;
            });
        }

        // تطبيق الترتيب
        if (options.orderBy) {
            const [field, direction] = options.orderBy.split(' ');
            data.sort((a, b) => {
                if (direction === 'DESC') {
                    return new Date(b[field]) - new Date(a[field]);
                } else {
                    return new Date(a[field]) - new Date(b[field]);
                }
            });
        }

        // تطبيق الحد الأقصى
        if (options.limit) {
            data = data.slice(0, options.limit);
        }

        return data;
    },

    async insert(table, data) {
        if (!mockAdvancedDatabase[table]) mockAdvancedDatabase[table] = [];
        const newId = Math.max(...mockAdvancedDatabase[table].map(item => item.id), 0) + 1;
        const newItem = { ...data, id: newId, createdAt: new Date().toISOString() };
        mockAdvancedDatabase[table].push(newItem);
        return newItem;
    },

    async update(table, id, data) {
        if (!mockAdvancedDatabase[table]) return null;
        const index = mockAdvancedDatabase[table].findIndex(item => item.id === id);
        if (index !== -1) {
            mockAdvancedDatabase[table][index] = {
                ...mockAdvancedDatabase[table][index],
                ...data,
                updatedAt: new Date().toISOString()
            };
            return mockAdvancedDatabase[table][index];
        }
        return null;
    },

    async delete(table, id) {
        if (!mockAdvancedDatabase[table]) return false;
        const index = mockAdvancedDatabase[table].findIndex(item => item.id === id);
        if (index !== -1) {
            mockAdvancedDatabase[table].splice(index, 1);
            return true;
        }
        return false;
    }
};

// ========================================
// اختبارات نظام التركيبات المتقدم - Advanced Prosthetics Tests
// ========================================

class AdvancedProstheticsTest {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
        this.priceCalculator = new PriceCalculator();
    }

    // ========================================
    // تشغيل جميع الاختبارات - Run All Tests
    // ========================================

    async runAllTests() {
        console.log('\n🧪 بدء اختبارات نظام التركيبات المتقدم...\n');

        // اختبارات أساسية
        await this.testSystemInitialization();
        await this.testCaseNumberGeneration();
        await this.testTeethSelection();
        await this.testPriceCalculation();

        // اختبارات الحساب الخاص
        await this.testPartialDentureCalculation();
        await this.testDiscountCalculation();

        // اختبارات البيانات
        await this.testDataValidation();
        await this.testDataSaving();
        await this.testDataFiltering();

        // اختبارات الواجهة
        await this.testUserInterface();

        // عرض النتائج
        this.displayResults();
    }

    // ========================================
    // اختبار تهيئة النظام - Test System Initialization
    // ========================================

    async testSystemInitialization() {
        try {
            console.log('🔍 اختبار تهيئة النظام...');

            // محاكاة مدير التركيبات المتقدم
            const manager = {
                prosthetics: [],
                doctors: [],
                prostheticTypes: [],
                selectedTeeth: [],
                teethMap: {},
                priceCalculator: new PriceCalculator()
            };

            this.assert(
                typeof manager === 'object',
                'إنشاء مدير التركيبات المتقدم',
                'يجب أن يتم إنشاء مدير التركيبات المتقدم بنجاح'
            );

            this.assert(
                Array.isArray(manager.selectedTeeth),
                'تهيئة قائمة الأسنان المختارة',
                'يجب أن تكون قائمة الأسنان المختارة مصفوفة'
            );

            this.assert(
                typeof manager.priceCalculator === 'object',
                'تهيئة حاسبة الأسعار',
                'يجب أن يتم تهيئة حاسبة الأسعار بنجاح'
            );

        } catch (error) {
            this.assert(false, 'تهيئة النظام', error.message);
        }
    }

    // ========================================
    // اختبار توليد رقم الحالة - Test Case Number Generation
    // ========================================

    async testCaseNumberGeneration() {
        try {
            console.log('🔍 اختبار توليد رقم الحالة...');

            // محاكاة توليد رقم الحالة
            const generateCaseNumber = async () => {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const nextNumber = 3; // من الإعدادات

                return `${year}${month}-${String(nextNumber).padStart(3, '0')}`;
            };

            const caseNumber = await generateCaseNumber();
            const expectedPattern = /^\d{6}-\d{3}$/;

            this.assert(
                expectedPattern.test(caseNumber),
                'تنسيق رقم الحالة',
                'يجب أن يكون رقم الحالة بالتنسيق الصحيح YYYYMM-###'
            );

            this.assert(
                caseNumber.length === 10,
                'طول رقم الحالة',
                'يجب أن يكون طول رقم الحالة 10 أحرف'
            );

        } catch (error) {
            this.assert(false, 'توليد رقم الحالة', error.message);
        }
    }

    // ========================================
    // اختبار اختيار الأسنان - Test Teeth Selection
    // ========================================

    async testTeethSelection() {
        try {
            console.log('🔍 اختبار اختيار الأسنان...');

            let selectedTeeth = [];

            // محاكاة اختيار الأسنان
            const toggleTooth = (toothNumber) => {
                const index = selectedTeeth.indexOf(toothNumber);
                if (index > -1) {
                    selectedTeeth.splice(index, 1);
                } else {
                    selectedTeeth.push(toothNumber);
                }
            };

            // اختبار اختيار أسنان فردية
            toggleTooth(11);
            toggleTooth(12);
            toggleTooth(13);

            this.assert(
                selectedTeeth.length === 3,
                'اختيار أسنان فردية',
                'يجب أن يتم اختيار الأسنان الفردية بشكل صحيح'
            );

            this.assert(
                selectedTeeth.includes(11) && selectedTeeth.includes(12) && selectedTeeth.includes(13),
                'محتوى الأسنان المختارة',
                'يجب أن تحتوي القائمة على الأسنان المختارة'
            );

            // اختبار إلغاء اختيار سن
            toggleTooth(12);

            this.assert(
                selectedTeeth.length === 2 && !selectedTeeth.includes(12),
                'إلغاء اختيار السن',
                'يجب أن يتم إلغاء اختيار السن بشكل صحيح'
            );

            // اختبار اختيار الفك العلوي
            selectedTeeth = Array.from({length: 16}, (_, i) => i + 1);

            this.assert(
                selectedTeeth.length === 16,
                'اختيار الفك العلوي',
                'يجب أن يتم اختيار جميع أسنان الفك العلوي'
            );

            // اختبار اختيار الفك السفلي
            selectedTeeth = Array.from({length: 16}, (_, i) => i + 17);

            this.assert(
                selectedTeeth.length === 16 && selectedTeeth[0] === 17 && selectedTeeth[15] === 32,
                'اختيار الفك السفلي',
                'يجب أن يتم اختيار جميع أسنان الفك السفلي'
            );

        } catch (error) {
            this.assert(false, 'اختيار الأسنان', error.message);
        }
    }

    // ========================================
    // اختبار حساب الأسعار - Test Price Calculation
    // ========================================

    async testPriceCalculation() {
        try {
            console.log('🔍 اختبار حساب الأسعار...');

            // اختبار الحساب العادي
            const normalCalculation = this.priceCalculator.calculate(
                'بورسلين فيتا', // المادة
                3, // عدد الأسنان
                350, // السعر لكل سن
                10 // نسبة الخصم
            );

            this.assert(
                normalCalculation.basePrice === 1050,
                'حساب السعر الأساسي',
                'يجب أن يكون السعر الأساسي = عدد الأسنان × السعر لكل سن'
            );

            this.assert(
                normalCalculation.discount.amount === 105,
                'حساب مبلغ الخصم',
                'يجب أن يكون مبلغ الخصم = السعر الأساسي × نسبة الخصم / 100'
            );

            this.assert(
                normalCalculation.finalPrice === 945,
                'حساب السعر النهائي',
                'يجب أن يكون السعر النهائي = السعر الأساسي - مبلغ الخصم'
            );

        } catch (error) {
            this.assert(false, 'حساب الأسعار', error.message);
        }
    }

    // ========================================
    // اختبار حساب البرشل الجزئي - Test Partial Denture Calculation
    // ========================================

    async testPartialDentureCalculation() {
        try {
            console.log('🔍 اختبار حساب البرشل الجزئي...');

            // اختبار الحساب الخاص للبرشل الجزئي
            const partialDentureCalculation = this.priceCalculator.calculate(
                'برشل جزئى', // المادة
                4, // عدد الأسنان
                0, // السعر لكل سن (لا يُستخدم في الحساب الخاص)
                0 // نسبة الخصم
            );

            // السعر المتوقع: 150 (أول سن) + (3 × 90) (الأسنان الإضافية) = 420
            const expectedPrice = 150 + (3 * 90);

            this.assert(
                partialDentureCalculation.basePrice === expectedPrice,
                'حساب البرشل الجزئي',
                'يجب أن يكون حساب البرشل الجزئي: أول سن 150 + باقي الأسنان × 90'
            );

            this.assert(
                partialDentureCalculation.specialCalculation !== null,
                'وجود حساب خاص',
                'يجب أن يحتوي البرشل الجزئي على حساب خاص'
            );

            this.assert(
                partialDentureCalculation.specialCalculation.type === 'partial_denture',
                'نوع الحساب الخاص',
                'يجب أن يكون نوع الحساب الخاص هو partial_denture'
            );

        } catch (error) {
            this.assert(false, 'حساب البرشل الجزئي', error.message);
        }
    }

    // ========================================
    // اختبار حساب الخصم - Test Discount Calculation
    // ========================================

    async testDiscountCalculation() {
        try {
            console.log('🔍 اختبار حساب الخصم...');

            // اختبار حساب الخصم بالنسبة المئوية
            const percentageDiscount = this.priceCalculator.calculateDiscount(1000, 15);

            this.assert(
                percentageDiscount.amount === 150,
                'حساب الخصم بالنسبة المئوية',
                'يجب أن يكون مبلغ الخصم = السعر الأساسي × النسبة المئوية / 100'
            );

            this.assert(
                percentageDiscount.percentage === 15,
                'النسبة المئوية للخصم',
                'يجب أن تكون النسبة المئوية صحيحة'
            );

            // اختبار حساب الخصم بالمبلغ المحدد
            const amountDiscount = this.priceCalculator.calculateDiscount(1000, 0, 200);

            this.assert(
                amountDiscount.amount === 200,
                'حساب الخصم بالمبلغ المحدد',
                'يجب أن يكون مبلغ الخصم هو المبلغ المحدد'
            );

            this.assert(
                amountDiscount.percentage === 20,
                'النسبة المئوية المحسوبة من المبلغ',
                'يجب أن تكون النسبة المئوية محسوبة بشكل صحيح من المبلغ'
            );

        } catch (error) {
            this.assert(false, 'حساب الخصم', error.message);
        }
    }

    // ========================================
    // اختبار التحقق من صحة البيانات - Test Data Validation
    // ========================================

    async testDataValidation() {
        try {
            console.log('🔍 اختبار التحقق من صحة البيانات...');

            // محاكاة دالة التحقق من صحة البيانات
            const validateProstheticData = (data) => {
                if (!data.caseNumber) return { isValid: false, message: 'رقم الحالة مطلوب' };
                if (!data.doctorId) return { isValid: false, message: 'يرجى اختيار الطبيب' };
                if (!data.patientName) return { isValid: false, message: 'يرجى إدخال اسم المريض' };
                if (!data.selectedMaterial) return { isValid: false, message: 'يرجى اختيار نوع التركيبة' };
                if (!data.selectedTeeth || data.selectedTeeth.length === 0) return { isValid: false, message: 'يرجى اختيار الأسنان المطلوبة' };
                if (!data.expectedDelivery) return { isValid: false, message: 'يرجى تحديد تاريخ التسليم المتوقع' };
                if (!data.unitPrice || data.unitPrice <= 0) return { isValid: false, message: 'يرجى إدخال سعر صحيح' };

                return { isValid: true };
            };

            // اختبار بيانات صحيحة
            const validData = {
                caseNumber: '202412-003',
                doctorId: 1,
                patientName: 'محمد أحمد',
                selectedMaterial: 'بورسلين فيتا',
                selectedTeeth: [11, 12, 13],
                expectedDelivery: '2024-12-20',
                unitPrice: 350
            };

            const validResult = validateProstheticData(validData);
            this.assert(
                validResult.isValid === true,
                'التحقق من البيانات الصحيحة',
                'يجب أن تمر البيانات الصحيحة من التحقق'
            );

            // اختبار بيانات ناقصة
            const invalidData = {
                caseNumber: '',
                doctorId: null,
                patientName: '',
                selectedMaterial: null,
                selectedTeeth: [],
                expectedDelivery: '',
                unitPrice: 0
            };

            const invalidResult = validateProstheticData(invalidData);
            this.assert(
                invalidResult.isValid === false,
                'التحقق من البيانات الناقصة',
                'يجب أن تفشل البيانات الناقصة في التحقق'
            );

            this.assert(
                typeof invalidResult.message === 'string',
                'رسالة خطأ التحقق',
                'يجب أن تحتوي نتيجة التحقق على رسالة خطأ'
            );

        } catch (error) {
            this.assert(false, 'التحقق من صحة البيانات', error.message);
        }
    }

    // ========================================
    // اختبار حفظ البيانات - Test Data Saving
    // ========================================

    async testDataSaving() {
        try {
            console.log('🔍 اختبار حفظ البيانات...');

            // بيانات تركيبة جديدة
            const newProsthetic = {
                case_number: '202412-003',
                patient_name: 'سارة أحمد محمد',
                patient_phone: '**********',
                doctor_id: 1,
                prosthetic_category: 'zirconia',
                prosthetic_type: 'Zircon Full Anatomy',
                material: 'Zircon Full Anatomy',
                selected_teeth: JSON.stringify([21, 22, 23]),
                teeth_count: 3,
                unit_price: 450,
                total_price: 1350,
                discount_percentage: 5,
                discount_amount: 67.5,
                final_price: 1282.5,
                expected_delivery: '2024-12-22',
                status: 'pending',
                priority: 'normal'
            };

            // حفظ التركيبة
            const savedProsthetic = await mockAdvancedDB.insert('prosthetics', newProsthetic);

            this.assert(
                savedProsthetic !== null,
                'حفظ التركيبة',
                'يجب أن يتم حفظ التركيبة بنجاح'
            );

            this.assert(
                savedProsthetic.id > 0,
                'تعيين معرف للتركيبة',
                'يجب أن تحصل التركيبة على معرف فريد'
            );

            this.assert(
                savedProsthetic.case_number === newProsthetic.case_number,
                'حفظ رقم الحالة',
                'يجب أن يتم حفظ رقم الحالة بشكل صحيح'
            );

            // التحقق من وجود التركيبة في قاعدة البيانات
            const allProsthetics = await mockAdvancedDB.findAll('prosthetics');
            const foundProsthetic = allProsthetics.find(p => p.id === savedProsthetic.id);

            this.assert(
                foundProsthetic !== undefined,
                'وجود التركيبة في قاعدة البيانات',
                'يجب أن تكون التركيبة موجودة في قاعدة البيانات بعد الحفظ'
            );

        } catch (error) {
            this.assert(false, 'حفظ البيانات', error.message);
        }
    }

    // ========================================
    // اختبار تصفية البيانات - Test Data Filtering
    // ========================================

    async testDataFiltering() {
        try {
            console.log('🔍 اختبار تصفية البيانات...');

            // تصفية حسب الحالة
            const pendingProsthetics = await mockAdvancedDB.findAll('prosthetics', {
                where: { status: 'pending' }
            });

            this.assert(
                Array.isArray(pendingProsthetics),
                'تصفية حسب الحالة',
                'يجب أن تعيد التصفية مصفوفة'
            );

            this.assert(
                pendingProsthetics.every(p => p.status === 'pending'),
                'صحة التصفية حسب الحالة',
                'يجب أن تحتوي النتائج على التركيبات المعلقة فقط'
            );

            // تصفية حسب الطبيب
            const doctorProsthetics = await mockAdvancedDB.findAll('prosthetics', {
                where: { doctor_id: 1 }
            });

            this.assert(
                doctorProsthetics.every(p => p.doctor_id === 1),
                'تصفية حسب الطبيب',
                'يجب أن تحتوي النتائج على تركيبات الطبيب المحدد فقط'
            );

            // البحث النصي
            const searchResults = await mockAdvancedDB.findAll('prosthetics', {
                where: { case_number: { like: '%202412%' } }
            });

            this.assert(
                searchResults.every(p => p.case_number.includes('202412')),
                'البحث النصي',
                'يجب أن تحتوي النتائج على التركيبات التي تطابق البحث'
            );

        } catch (error) {
            this.assert(false, 'تصفية البيانات', error.message);
        }
    }

    // ========================================
    // اختبار واجهة المستخدم - Test User Interface
    // ========================================

    async testUserInterface() {
        try {
            console.log('🔍 اختبار واجهة المستخدم...');

            // محاكاة عناصر الواجهة
            const mockElements = {
                'prosthetics-search': { value: '', addEventListener: () => {} },
                'status-filter': { value: '', addEventListener: () => {} },
                'doctor-filter': { value: '', addEventListener: () => {} },
                'pending-count': { textContent: '0' },
                'progress-count': { textContent: '0' },
                'completed-count': { textContent: '0' },
                'delivered-count': { textContent: '0' }
            };

            // محاكاة document.getElementById
            const getElementById = (id) => mockElements[id] || null;

            this.assert(
                getElementById('prosthetics-search') !== null,
                'وجود حقل البحث',
                'يجب أن يكون حقل البحث موجوداً في الواجهة'
            );

            this.assert(
                getElementById('status-filter') !== null,
                'وجود تصفية الحالة',
                'يجب أن تكون تصفية الحالة موجودة في الواجهة'
            );

            this.assert(
                getElementById('doctor-filter') !== null,
                'وجود تصفية الطبيب',
                'يجب أن تكون تصفية الطبيب موجودة في الواجهة'
            );

            // اختبار تحديث الإحصائيات
            const updateStatistics = (prosthetics) => {
                const pendingCount = prosthetics.filter(p => p.status === 'pending').length;
                const progressCount = prosthetics.filter(p => p.status === 'in_progress').length;
                const completedCount = prosthetics.filter(p => p.status === 'completed').length;
                const deliveredCount = prosthetics.filter(p => p.status === 'delivered').length;

                const pendingElement = getElementById('pending-count');
                const progressElement = getElementById('progress-count');
                const completedElement = getElementById('completed-count');
                const deliveredElement = getElementById('delivered-count');

                if (pendingElement) pendingElement.textContent = pendingCount;
                if (progressElement) progressElement.textContent = progressCount;
                if (completedElement) completedElement.textContent = completedCount;
                if (deliveredElement) deliveredElement.textContent = deliveredCount;

                return { pendingCount, progressCount, completedCount, deliveredCount };
            };

            const testProsthetics = [
                { status: 'pending' },
                { status: 'pending' },
                { status: 'in_progress' },
                { status: 'completed' },
                { status: 'delivered' }
            ];

            const stats = updateStatistics(testProsthetics);

            this.assert(
                stats.pendingCount === 2,
                'حساب التركيبات المعلقة',
                'يجب أن يتم حساب التركيبات المعلقة بشكل صحيح'
            );

            this.assert(
                stats.progressCount === 1,
                'حساب التركيبات قيد التنفيذ',
                'يجب أن يتم حساب التركيبات قيد التنفيذ بشكل صحيح'
            );

        } catch (error) {
            this.assert(false, 'واجهة المستخدم', error.message);
        }
    }

    // ========================================
    // دالة التحقق - Assert Function
    // ========================================

    assert(condition, testName, message) {
        const result = {
            name: testName,
            passed: condition,
            message: message,
            timestamp: new Date().toISOString()
        };

        this.testResults.push(result);

        if (condition) {
            this.passedTests++;
            console.log(`✅ ${testName}: نجح`);
        } else {
            this.failedTests++;
            console.log(`❌ ${testName}: فشل - ${message}`);
        }
    }

    // ========================================
    // عرض النتائج - Display Results
    // ========================================

    displayResults() {
        console.log('\n📊 نتائج اختبارات نظام التركيبات المتقدم:');
        console.log('='.repeat(60));
        console.log(`إجمالي الاختبارات: ${this.testResults.length}`);
        console.log(`الاختبارات الناجحة: ${this.passedTests} ✅`);
        console.log(`الاختبارات الفاشلة: ${this.failedTests} ❌`);
        console.log(`معدل النجاح: ${((this.passedTests / this.testResults.length) * 100).toFixed(1)}%`);

        if (this.failedTests > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            this.testResults
                .filter(result => !result.passed)
                .forEach(result => {
                    console.log(`- ${result.name}: ${result.message}`);
                });
        }

        console.log('\n🎉 انتهت اختبارات نظام التركيبات المتقدم!');

        return {
            total: this.testResults.length,
            passed: this.passedTests,
            failed: this.failedTests,
            successRate: (this.passedTests / this.testResults.length) * 100
        };
    }
}

// ========================================
// تشغيل الاختبارات - Run Tests
// ========================================

async function runAdvancedProstheticsTests() {
    const tester = new AdvancedProstheticsTest();
    return await tester.runAllTests();
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (typeof require !== 'undefined' && require.main === module) {
    runAdvancedProstheticsTests().then(results => {
        console.log('\n📈 ملخص النتائج النهائية:');
        console.log(`نجح ${results.passed} من ${results.total} اختبار (${results.successRate.toFixed(1)}%)`);

        // إنهاء العملية بحالة نجاح أو فشل
        process.exit(results.failed === 0 ? 0 : 1);
    }).catch(error => {
        console.error('❌ خطأ في تشغيل الاختبارات:', error);
        process.exit(1);
    });
}

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runAdvancedProstheticsTests, AdvancedProstheticsTest };
}

console.log('✅ تم تحميل اختبارات نظام التركيبات المتقدم بنجاح');