// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// اختبار وحدة التركيبات - Prosthetics Module Test
// ========================================

console.log('🧪 بدء اختبار وحدة التركيبات...\n');

// ========================================
// اختبار حاسبة البرشل الجزئي - Test Partial Bridge Calculator
// ========================================

function testPartialBridgeCalculator() {
    console.log('📊 اختبار حاسبة البرشل الجزئي...');
    
    try {
        // إنشاء مثيل من الحاسبة
        const calculator = new PartialBridgeCalculator();
        
        // اختبار 1: حساب بسيط
        console.log('\n🔹 اختبار 1: حساب بسيط (3 أسنان)');
        const test1 = calculator.calculatePartialBridgePrice({
            teethCount: 3,
            basePrice: 150,
            material: 'metal',
            complexity: 'simple',
            location: 'posterior'
        });
        
        console.log(`✅ السعر النهائي: ${test1.summary.finalPrice} ريال`);
        console.log(`✅ السعر لكل سن: ${test1.summary.pricePerTooth.toFixed(2)} ريال`);
        
        // اختبار 2: حساب معقد
        console.log('\n🔹 اختبار 2: حساب معقد (8 أسنان، زيركون)');
        const test2 = calculator.calculatePartialBridgePrice({
            teethCount: 8,
            basePrice: 150,
            material: 'zirconia',
            complexity: 'complex',
            location: 'anterior',
            doctorDiscount: 15
        });
        
        console.log(`✅ السعر قبل الخصم: ${test2.breakdown.totalBeforeDiscount} ريال`);
        console.log(`✅ مبلغ الخصم: ${test2.breakdown.discountAmount} ريال`);
        console.log(`✅ السعر النهائي: ${test2.summary.finalPrice} ريال`);
        
        // اختبار 3: مقارنة الخيارات
        console.log('\n🔹 اختبار 3: مقارنة خيارات التسعير');
        const suggestions = calculator.suggestBestOption(6, 150);
        console.log(`✅ الخيار الموصى به: ${suggestions.recommended.description}`);
        console.log(`✅ التوفير: ${suggestions.recommended.savings.toFixed(2)} ريال`);
        
        // اختبار 4: تحليل الربحية
        console.log('\n🔹 اختبار 4: تحليل الربحية');
        const profitability = calculator.analyzeProfitability({
            teethCount: 5,
            basePrice: 150,
            material: 'vitallium',
            complexity: 'moderate'
        }, 400); // تكلفة 400 ريال
        
        console.log(`✅ الإيرادات: ${profitability.revenue} ريال`);
        console.log(`✅ الربح: ${profitability.profit} ريال`);
        console.log(`✅ هامش الربح: ${profitability.profitMargin.toFixed(1)}%`);
        console.log(`✅ التوصية: ${profitability.recommendation.message}`);
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار حاسبة البرشل الجزئي:', error);
        return false;
    }
}

// ========================================
// اختبار أنواع التركيبات - Test Prosthetic Types
// ========================================

function testProstheticTypes() {
    console.log('\n🦷 اختبار أنواع التركيبات...');
    
    try {
        // قائمة أنواع التركيبات المتوقعة
        const expectedTypes = [
            'فيتا', 'جى سرام', 'فيس',           // بورسلين
            'Full Anatomy', 'Copy + Porcelain', 'Onlay', // زيركون
            'معدن', 'Vitallium',                // معدن
            'طقم كامل', 'طقم جزئي', 'برشل جزئي', // أطقم
            'جهاز تقويم متحرك', 'حافظ مكان', 'واقي أسنان', // تقويم
            'إصلاح كسر', 'تعديل وضبط', 'إعادة تبطين', 'تلميع وتنظيف' // إضافية
        ];
        
        console.log(`✅ عدد أنواع التركيبات المتوقعة: ${expectedTypes.length}`);
        
        // اختبار الفئات
        const categories = ['porcelain', 'zirconia', 'metal', 'dentures', 'orthodontics', 'additional'];
        console.log(`✅ عدد الفئات: ${categories.length}`);
        
        categories.forEach(category => {
            console.log(`  - ${category}: متوفر`);
        });
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار أنواع التركيبات:', error);
        return false;
    }
}

// ========================================
// اختبار نظام اختيار الأسنان - Test Teeth Selection System
// ========================================

function testTeethSelectionSystem() {
    console.log('\n🦷 اختبار نظام اختيار الأسنان...');
    
    try {
        // محاكاة اختيار أسنان مختلفة
        const testSelections = [
            { numbers: [11, 12, 13], description: 'أسنان أمامية علوية' },
            { numbers: [36, 37], description: 'أضراس سفلية' },
            { numbers: [14, 15, 16, 24, 25, 26], description: 'أسنان مختلطة' },
            { numbers: [41, 42, 31, 32], description: 'أسنان أمامية سفلية' }
        ];
        
        testSelections.forEach((selection, index) => {
            console.log(`\n🔹 اختبار ${index + 1}: ${selection.description}`);
            console.log(`  الأسنان: ${selection.numbers.join(', ')}`);
            console.log(`  العدد: ${selection.numbers.length}`);
            
            // تحديد الموقع
            const location = determineLocation(selection.numbers);
            console.log(`  الموقع: ${location}`);
        });
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار نظام اختيار الأسنان:', error);
        return false;
    }
}

// دالة مساعدة لتحديد موقع الأسنان
function determineLocation(toothNumbers) {
    const anterior = toothNumbers.filter(num => 
        (num >= 11 && num <= 13) || (num >= 21 && num <= 23) ||
        (num >= 31 && num <= 33) || (num >= 41 && num <= 43)
    );
    
    const posterior = toothNumbers.filter(num => 
        (num >= 14 && num <= 18) || (num >= 24 && num <= 28) ||
        (num >= 34 && num <= 38) || (num >= 44 && num <= 48)
    );
    
    if (anterior.length > 0 && posterior.length > 0) return 'مختلط';
    if (anterior.length > 0) return 'أمامي';
    return 'خلفي';
}

// ========================================
// اختبار حساب الأسعار - Test Price Calculation
// ========================================

function testPriceCalculation() {
    console.log('\n💰 اختبار حساب الأسعار...');
    
    try {
        // اختبارات مختلفة للأسعار
        const priceTests = [
            {
                name: 'تاج بورسلين واحد',
                type: 'per_tooth',
                unitPrice: 350,
                quantity: 1,
                discount: 0
            },
            {
                name: 'جسر زيركون 3 وحدات',
                type: 'per_tooth',
                unitPrice: 450,
                quantity: 3,
                discount: 10
            },
            {
                name: 'طقم كامل',
                type: 'fixed',
                unitPrice: 800,
                quantity: 1,
                discount: 15
            },
            {
                name: 'برشل جزئي 6 أسنان',
                type: 'partial_bridge',
                unitPrice: 150,
                quantity: 6,
                discount: 12
            }
        ];
        
        priceTests.forEach((test, index) => {
            console.log(`\n🔹 اختبار ${index + 1}: ${test.name}`);
            
            let totalPrice = 0;
            
            if (test.type === 'per_tooth') {
                totalPrice = test.unitPrice * test.quantity;
            } else if (test.type === 'fixed') {
                totalPrice = test.unitPrice;
            } else if (test.type === 'partial_bridge') {
                // استخدام حساب البرشل الجزئي
                totalPrice = calculateSimplePartialBridge(test.unitPrice, test.quantity);
            }
            
            const discountAmount = (totalPrice * test.discount) / 100;
            const finalPrice = totalPrice - discountAmount;
            
            console.log(`  السعر الأساسي: ${totalPrice} ريال`);
            console.log(`  الخصم (${test.discount}%): ${discountAmount} ريال`);
            console.log(`  السعر النهائي: ${finalPrice} ريال`);
        });
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار حساب الأسعار:', error);
        return false;
    }
}

// دالة مساعدة لحساب البرشل الجزئي البسيط
function calculateSimplePartialBridge(unitPrice, teethCount) {
    let totalPrice = 0;
    
    const firstTier = Math.min(teethCount, 3);
    totalPrice += firstTier * unitPrice;
    
    if (teethCount > 3) {
        const secondTier = Math.min(teethCount - 3, 3);
        totalPrice += secondTier * unitPrice * 0.9;
    }
    
    if (teethCount > 6) {
        const thirdTier = Math.min(teethCount - 6, 4);
        totalPrice += thirdTier * unitPrice * 0.8;
    }
    
    if (teethCount > 10) {
        const remainingTeeth = teethCount - 10;
        totalPrice += remainingTeeth * unitPrice * 0.7;
    }
    
    return totalPrice;
}

// ========================================
// تشغيل جميع الاختبارات - Run All Tests
// ========================================

async function runProstheticsTests() {
    console.log('🚀 نظام إدارة معمل الأسنان المتقدم v2.0 - اختبار وحدة التركيبات\n');
    
    const tests = [
        { name: 'حاسبة البرشل الجزئي', func: testPartialBridgeCalculator },
        { name: 'أنواع التركيبات', func: testProstheticTypes },
        { name: 'نظام اختيار الأسنان', func: testTeethSelectionSystem },
        { name: 'حساب الأسعار', func: testPriceCalculation }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            const result = await test.func();
            if (result) {
                passedTests++;
                console.log(`✅ نجح اختبار: ${test.name}\n`);
            } else {
                console.log(`❌ فشل اختبار: ${test.name}\n`);
            }
        } catch (error) {
            console.log(`❌ خطأ في اختبار ${test.name}:`, error, '\n');
        }
    }
    
    console.log('========================================');
    console.log('📊 نتائج اختبار وحدة التركيبات:');
    console.log(`✅ نجح: ${passedTests}/${totalTests}`);
    console.log(`❌ فشل: ${totalTests - passedTests}/${totalTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('========================================\n');
    
    if (passedTests === totalTests) {
        console.log('🎉 جميع اختبارات وحدة التركيبات نجحت!');
        console.log('💡 وحدة التركيبات جاهزة للاستخدام.');
    } else {
        console.log('⚠️ بعض اختبارات وحدة التركيبات فشلت.');
        console.log('🔧 يرجى مراجعة الأخطاء أعلاه.');
    }
    
    return passedTests === totalTests;
}

// تشغيل الاختبارات
if (require.main === module) {
    runProstheticsTests().catch(error => {
        console.error('❌ خطأ في تشغيل اختبارات وحدة التركيبات:', error);
        process.exit(1);
    });
}

module.exports = {
    runProstheticsTests,
    testPartialBridgeCalculator,
    testProstheticTypes,
    testTeethSelectionSystem,
    testPriceCalculation
};
