// ========================================
// نظام إدارة معمل الأسنان المتقدم v2.0
// اختبار النظام - System Test
// ========================================

console.log('🧪 بدء اختبار النظام...\n');

// ========================================
// اختبار المتطلبات الأساسية - Test Prerequisites
// ========================================

function testPrerequisites() {
    console.log('📋 اختبار المتطلبات الأساسية...');
    
    // اختبار Node.js
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion >= 16) {
        console.log(`✅ Node.js: ${nodeVersion} (مدعوم)`);
    } else {
        console.log(`❌ Node.js: ${nodeVersion} (غير مدعوم - يتطلب v16 أو أحدث)`);
        return false;
    }
    
    // اختبار النظام
    const platform = process.platform;
    const arch = process.arch;
    console.log(`✅ النظام: ${platform} ${arch}`);
    
    // اختبار الذاكرة
    const totalMemory = process.memoryUsage();
    console.log(`✅ الذاكرة المستخدمة: ${Math.round(totalMemory.heapUsed / 1024 / 1024)} MB`);
    
    return true;
}

// ========================================
// اختبار الملفات الأساسية - Test Core Files
// ========================================

function testCoreFiles() {
    console.log('\n📁 اختبار الملفات الأساسية...');
    
    const fs = require('fs');
    const path = require('path');
    
    const requiredFiles = [
        'index.html',
        'main.js',
        'package.json',
        'js/core/globals.js',
        'js/core/database.js',
        'js/core/auth.js',
        'js/core/language.js',
        'js/core/theme.js',
        'js/core/notifications.js',
        'js/core/utils.js',
        'js/app.js',
        'js/modules/dashboard.js',
        'css/main.css',
        'css/layout.css',
        'css/dashboard.css'
    ];
    
    let allFilesExist = true;
    
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file}`);
        } else {
            console.log(`❌ ${file} (مفقود)`);
            allFilesExist = false;
        }
    });
    
    return allFilesExist;
}

// ========================================
// اختبار التبعيات - Test Dependencies
// ========================================

function testDependencies() {
    console.log('\n📦 اختبار التبعيات...');
    
    const fs = require('fs');
    
    // قراءة package.json
    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const dependencies = packageJson.dependencies || {};
        
        console.log(`✅ عدد التبعيات: ${Object.keys(dependencies).length}`);
        
        // اختبار وجود node_modules
        if (fs.existsSync('node_modules')) {
            console.log('✅ مجلد node_modules موجود');
            
            // اختبار بعض التبعيات المهمة
            const importantDeps = ['electron', 'chart.js', 'sqlite3'];
            
            importantDeps.forEach(dep => {
                if (fs.existsSync(`node_modules/${dep}`)) {
                    console.log(`✅ ${dep} مثبت`);
                } else {
                    console.log(`⚠️ ${dep} غير مثبت`);
                }
            });
            
            return true;
        } else {
            console.log('❌ مجلد node_modules غير موجود - يرجى تشغيل npm install');
            return false;
        }
        
    } catch (error) {
        console.log('❌ خطأ في قراءة package.json:', error.message);
        return false;
    }
}

// ========================================
// اختبار إعدادات التطبيق - Test App Configuration
// ========================================

function testAppConfiguration() {
    console.log('\n⚙️ اختبار إعدادات التطبيق...');
    
    try {
        // محاكاة تحميل الإعدادات
        const mockGlobals = {
            APP_INFO: {
                name: 'نظام إدارة معمل الأسنان المتقدم',
                version: '2.0.0'
            },
            SYSTEM_CONFIG: {
                database: { name: 'dental_lab_v2.db' },
                ui: { language: 'ar', theme: 'light' }
            }
        };
        
        console.log(`✅ اسم التطبيق: ${mockGlobals.APP_INFO.name}`);
        console.log(`✅ إصدار التطبيق: ${mockGlobals.APP_INFO.version}`);
        console.log(`✅ اللغة الافتراضية: ${mockGlobals.SYSTEM_CONFIG.ui.language}`);
        console.log(`✅ الثيم الافتراضي: ${mockGlobals.SYSTEM_CONFIG.ui.theme}`);
        
        return true;
    } catch (error) {
        console.log('❌ خطأ في اختبار الإعدادات:', error.message);
        return false;
    }
}

// ========================================
// اختبار قاعدة البيانات - Test Database
// ========================================

function testDatabase() {
    console.log('\n🗄️ اختبار قاعدة البيانات...');
    
    const fs = require('fs');
    
    // إنشاء مجلد البيانات إذا لم يكن موجوداً
    if (!fs.existsSync('data')) {
        fs.mkdirSync('data');
        console.log('✅ تم إنشاء مجلد data');
    } else {
        console.log('✅ مجلد data موجود');
    }
    
    // إنشاء مجلد النسخ الاحتياطية
    if (!fs.existsSync('data/backups')) {
        fs.mkdirSync('data/backups', { recursive: true });
        console.log('✅ تم إنشاء مجلد backups');
    } else {
        console.log('✅ مجلد backups موجود');
    }
    
    // اختبار إنشاء ملف تجريبي
    try {
        const testData = {
            timestamp: new Date().toISOString(),
            test: 'نظام إدارة معمل الأسنان المتقدم v2.0'
        };
        
        fs.writeFileSync('data/test.json', JSON.stringify(testData, null, 2));
        console.log('✅ اختبار الكتابة في مجلد البيانات نجح');
        
        // حذف الملف التجريبي
        fs.unlinkSync('data/test.json');
        console.log('✅ اختبار الحذف من مجلد البيانات نجح');
        
        return true;
    } catch (error) {
        console.log('❌ خطأ في اختبار قاعدة البيانات:', error.message);
        return false;
    }
}

// ========================================
// اختبار الأمان - Test Security
// ========================================

function testSecurity() {
    console.log('\n🔒 اختبار الأمان...');
    
    try {
        // اختبار تشفير بسيط
        const crypto = require('crypto');
        const testPassword = 'test123';
        const hash = crypto.createHash('sha256').update(testPassword).digest('hex');
        
        if (hash && hash.length === 64) {
            console.log('✅ نظام التشفير يعمل بشكل صحيح');
        } else {
            console.log('❌ مشكلة في نظام التشفير');
            return false;
        }
        
        // اختبار إنشاء معرف فريد
        const uuid = crypto.randomUUID();
        if (uuid && uuid.length === 36) {
            console.log('✅ إنشاء المعرفات الفريدة يعمل بشكل صحيح');
        } else {
            console.log('❌ مشكلة في إنشاء المعرفات الفريدة');
            return false;
        }
        
        return true;
    } catch (error) {
        console.log('❌ خطأ في اختبار الأمان:', error.message);
        return false;
    }
}

// ========================================
// اختبار الأداء - Test Performance
// ========================================

function testPerformance() {
    console.log('\n⚡ اختبار الأداء...');
    
    const startTime = process.hrtime.bigint();
    
    // محاكاة عمليات معالجة البيانات
    const testData = [];
    for (let i = 0; i < 10000; i++) {
        testData.push({
            id: i,
            name: `عنصر ${i}`,
            timestamp: new Date().toISOString()
        });
    }
    
    // اختبار البحث
    const searchResult = testData.filter(item => item.id % 100 === 0);
    
    // اختبار الترتيب
    const sortedData = testData.sort((a, b) => a.id - b.id);
    
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // تحويل إلى ميلي ثانية
    
    console.log(`✅ معالجة 10,000 عنصر: ${duration.toFixed(2)} ms`);
    console.log(`✅ نتائج البحث: ${searchResult.length} عنصر`);
    console.log(`✅ الترتيب: ${sortedData.length} عنصر`);
    
    if (duration < 1000) { // أقل من ثانية واحدة
        console.log('✅ الأداء ممتاز');
        return true;
    } else if (duration < 5000) { // أقل من 5 ثواني
        console.log('⚠️ الأداء مقبول');
        return true;
    } else {
        console.log('❌ الأداء ضعيف');
        return false;
    }
}

// ========================================
// تشغيل جميع الاختبارات - Run All Tests
// ========================================

async function runAllTests() {
    console.log('🚀 نظام إدارة معمل الأسنان المتقدم v2.0 - اختبار النظام\n');
    
    const tests = [
        { name: 'المتطلبات الأساسية', func: testPrerequisites },
        { name: 'الملفات الأساسية', func: testCoreFiles },
        { name: 'التبعيات', func: testDependencies },
        { name: 'إعدادات التطبيق', func: testAppConfiguration },
        { name: 'قاعدة البيانات', func: testDatabase },
        { name: 'الأمان', func: testSecurity },
        { name: 'الأداء', func: testPerformance }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            const result = await test.func();
            if (result) {
                passedTests++;
            }
        } catch (error) {
            console.log(`❌ خطأ في اختبار ${test.name}:`, error.message);
        }
    }
    
    console.log('\n========================================');
    console.log('📊 نتائج الاختبار:');
    console.log(`✅ نجح: ${passedTests}/${totalTests}`);
    console.log(`❌ فشل: ${totalTests - passedTests}/${totalTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('========================================\n');
    
    if (passedTests === totalTests) {
        console.log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
        console.log('💡 يمكنك الآن تشغيل التطبيق باستخدام: npm start');
    } else {
        console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
        console.log('📖 راجع ملف INSTALLATION.md للحصول على تعليمات مفصلة.');
    }
    
    return passedTests === totalTests;
}

// تشغيل الاختبارات
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ خطأ في تشغيل الاختبارات:', error);
        process.exit(1);
    });
}

module.exports = {
    runAllTests,
    testPrerequisites,
    testCoreFiles,
    testDependencies,
    testAppConfiguration,
    testDatabase,
    testSecurity,
    testPerformance
};
