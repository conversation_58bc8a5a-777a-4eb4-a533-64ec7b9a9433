/**
 * أنماط نظام اختيار الأسنان
 * Tooth Selection System Styles
 * مستخرج من مشروع معمل الأسنان
 */

/* حاوي مخطط الأسنان الرئيسي */
.tooth-diagram-container {
    border: 3px solid #0d47a1;
    border-radius: 20px;
    padding: 2.5rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #d1e7dd 100%);
    margin: 2rem 0;
    box-shadow: 0 8px 25px rgba(13, 71, 161, 0.15);
    position: relative;
    overflow: hidden;
}

.tooth-diagram-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0d47a1, #1565c0, #1976d2, #1565c0, #0d47a1);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* رأس المخطط */
.diagram-header {
    text-align: center;
    margin-bottom: 2rem;
}

.diagram-header h3 {
    color: #0d47a1;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.diagram-header p {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* الفكوك */
.jaw {
    margin-bottom: 3rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.jaw h4 {
    text-align: center;
    color: #0d47a1;
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.jaw-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.jaw-side {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.side-label {
    font-weight: 600;
    color: #0d47a1;
    font-size: 0.9rem;
    text-align: center;
}

.teeth-row {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.jaw-separator {
    font-size: 2rem;
    color: #0d47a1;
    font-weight: bold;
    align-self: center;
}

/* الأسنان */
.tooth {
    width: 45px;
    height: 60px;
    border: 3px solid #e0e0e0;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 50%, #f0f8ff 100%);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    user-select: none;
    position: relative;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin: 0;
    flex-shrink: 0;
    flex-grow: 0;
}

.tooth::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #0d47a1, #1565c0, #1976d2, #1565c0, #0d47a1);
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tooth-icon {
    font-size: 2rem;
    margin-bottom: 4px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    transition: all 0.3s ease;
}

.tooth-number {
    font-size: 0.85rem;
    font-weight: 900;
    color: #0d47a1;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 8px;
    border: 1px solid #e3f2fd;
    min-width: 24px;
    text-align: center;
}

/* حالات الأسنان */
.tooth:hover {
    border-color: #1565c0;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
    transform: translateY(-4px) scale(1.08);
    box-shadow: 0 8px 25px rgba(21, 101, 192, 0.4);
}

.tooth:hover::before {
    opacity: 1;
}

.tooth.selected {
    background: linear-gradient(135deg, #0d47a1 0%, #1565c0 50%, #1976d2 100%);
    color: white;
    border-color: #0d47a1;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 20px rgba(13, 71, 161, 0.5);
    animation: pulse 2s ease-in-out infinite;
}

.tooth.selected::before {
    opacity: 1;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 8px 20px rgba(13, 71, 161, 0.5);
    }
    50% {
        box-shadow: 0 12px 30px rgba(13, 71, 161, 0.7);
    }
}

.tooth.selected .tooth-number {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.tooth.selected .tooth-icon {
    filter: brightness(0) invert(1);
}

/* أنواع الأسنان */
.tooth.incisor {
    border-color: #28a745;
}

.tooth.canine {
    border-color: #ffc107;
}

.tooth.premolar {
    border-color: #17a2b8;
}

.tooth.molar {
    border-color: #dc3545;
}

/* ملخص الاختيار */
.selection-summary {
    margin-top: 3rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    border: 2px solid #e3f2fd;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 8px 25px rgba(13, 71, 161, 0.1);
    position: relative;
}

.selection-summary::before {
    content: '📋';
    position: absolute;
    top: -15px;
    right: 20px;
    font-size: 2rem;
    background: white;
    padding: 0.5rem;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.summary-card {
    margin-bottom: 2rem;
}

.summary-card h4 {
    color: #0d47a1;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.selected-display {
    min-height: 60px;
    padding: 1rem;
    border: 2px dashed #e0e0e0;
    border-radius: 12px;
    background: #f8f9fa;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.selected-display.empty {
    color: #999;
    font-style: italic;
    justify-content: center;
}

.selected-display.filled {
    border-color: #0d47a1;
    background: #e3f2fd;
    justify-content: flex-start;
}

.selected-tooth {
    background: linear-gradient(135deg, #0d47a1, #1565c0);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(13, 71, 161, 0.3);
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.selected-tooth:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 71, 161, 0.4);
}

.remove-tooth {
    background: rgba(255,255,255,0.3);
    border: none;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    line-height: 1;
    transition: background 0.2s ease;
}

.remove-tooth:hover {
    background: rgba(255,255,255,0.5);
}

.selection-count {
    text-align: center;
    font-size: 1.1rem;
    color: #0d47a1;
    font-weight: 600;
    margin-top: 1rem;
}

/* الإجراءات السريعة */
.quick-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: #0d47a1;
    color: white;
}

.btn-primary:hover {
    background: #1565c0;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-1px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .tooth-diagram-container {
        padding: 1.5rem;
    }
    
    .jaw-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .jaw-separator {
        transform: rotate(90deg);
        font-size: 1.5rem;
    }
    
    .tooth {
        width: 40px;
        height: 50px;
    }
    
    .tooth-icon {
        font-size: 1.5rem;
    }
    
    .tooth-number {
        font-size: 0.75rem;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
